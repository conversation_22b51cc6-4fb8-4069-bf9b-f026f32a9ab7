const axios = require('axios');
const express = require('express');
const bodyParser = require('body-parser');
const multer = require('multer');

const fs = require('fs');
const path = require('path');

import {Buffer} from "buffer";
const crypto = require('crypto');

const lancedb = require("@lancedb/lancedb");
const apacheArrow = require("apache-arrow");

const WebSocket = require('ws');
const mammoth = require("mammoth"); // 把docx转为md格式

// const session = require('express-session');
// const cookieParser = require('cookie-parser');
// const ffmpeg = require('fluent-ffmpeg');
// const temp = require('temp');
// import OpenAI from "openai";

// 用于主项目执行
const {start} = require('./src/main-start');
// 用于派生项目执行
// const {start} = require('./lib/main-start');

start({
  axios,
  express, bodyParser, multer,
  // session, cookieParser,
  fs, path,
  Buffer, crypto,
  lancedb, apacheArrow,
  WebSocket, mammoth,
  // OpenAI,
  // ffmpeg, temp,
}, {
  startServer: true,
  startCSMiniAgentServer: true,
  startCSMiniKbServer: true,
  startWeiXinServer: true,
  startLongArticleServer: true,
  startDocImportServer: true,
})