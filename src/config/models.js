export default {
    data: [
        /*** DeepSeek（官网） ***/
        {
            label: 'DeepSeek-V3.1-满血（官网）',
            value: 'ds_deepseek-chat',
            title: '上下文64k，输出8k',
        },
        {
            label: 'DeepSeek-R1-满血（官网）',
            value: 'ds_deepseek-reasoner',
            title: '上下文64k，输出8k',
        },
        /*** ALiBL ***/
        {
            label: 'DeepSeek-V3.1-满血（ALiBL）',
            value: 'albl_deepseek-v3.1',
            title: '上下文130k，输出65k',
        },
        {
            label: 'DeepSeek-V3-满血（ALiBL）',
            value: 'albl_deepseek-v3',
            title: '上下文64k，输出8k',
        },
        {
            label: 'DeepSeek-R1-满血（ALiBL）',
            value: 'albl_deepseek-r1',
            title: '上下文64k，输出8k',
        },
        {
            label: 'Qwen3-Max（ALiBL）',
            value: 'albl_qwen3-max-preview',
            title: '上下文262k，输入258k，输出32k',
            canToolCall: true,
        },
        {
            label: 'Qwen3-235b（ALiBL）',
            value: 'albl_qwen3-235b-a22b',
            title: '上下文131k，输出8k',
            canToolCall: true,
        },
        {
            label: 'Qwen3-235b【不思考】（ALiBL）',
            value: 'albl_qwen3-235b-a22b|no_think|',
            title: '上下文131k，输出8k',
            canToolCall: true,
        },
        {
            label: 'Qwen3-32b（ALiBL）',
            value: 'albl_qwen3-32',
            title: '上下文131k，输出8k',
            canToolCall: true,
        },
        {
            label: 'Qwen3-32b【不思考】（ALiBL）',
            value: 'albl_qwen3-32b|no_think|',
            title: '上下文131k，输出8k',
            canToolCall: true,
        },
        {
            label: 'Qwen3-14b（ALiBL）',
            value: 'albl_qwen3-14b',
            title: '上下文131k，输出8k',
            canToolCall: true,
        },
        {
            label: 'Qwen3-14b【不思考】（ALiBL）',
            value: 'albl_qwen3-14b|no_think|',
            title: '上下文131k，输出8k',
            canToolCall: true,
        },
        {
            label: 'Qwen-max（ALiBL）',
            value: 'albl_qwen-max',
            title: '上下文32k，输出8k',
            canToolCall: true,
        },
        {
            label: 'Qwen-plus（ALiBL）',
            value: 'albl_qwen-plus',
            title: '上下文131k，输出8k',
            canToolCall: true,
        },
        {
            label: 'Qwen-turbo（ALiBL）',
            value: 'albl_qwen-turbo',
            title: '上下文1000k，输出8k',
            canToolCall: true,
        },
        {
            label: 'Qwen-long（ALiBL）',
            value: 'albl_qwen-long',
            title: '上下文10000k，输出6k',
        },
        {
            label: 'Qwen-vl-max（ALiBL）',
            value: 'albl_qwen-vl-max',
            title: '上下文32k，输出8k',
            canVision: true,
        },
        {
            label: 'Qwen-vl-plus（ALiBL）',
            value: 'albl_qwen-vl-plus',
            title: '上下文8k，输出2k',
            canVision: true,
        },
        {
            label: 'Qwen-vl-ocr（ALiBL）',
            value: 'albl_qwen-vl-ocr',
            title: '上下文34k，输出4k',
            canVision: true,
        },
        // 只支持stream模式
        {
            label: 'Qwen-omni-turbo（ALiBL）',
            value: 'albl_qwen-omni-turbo',
            title: '上下文32k，输出2k',
        },
        {
            label: 'Qwen2.5-14b-instruct-1m（ALiBL）',
            value: 'albl_qwen2.5-14b-instruct-1m',
            title: '上下文1000k，输出8k',
        },
        /*** 智谱AI（官网） ***/
        {
            label: 'GLM-4.5-X【不思考】（ZhiPu）',
            value: 'zhipu_glm-4.5-x|no_think|',
            title: '上下文128k',
            canToolCall: true,
        },
        {
            label: 'GLM-4.5-X（ZhiPu）',
            value: 'zhipu_glm-4.5-x',
            title: '上下文128k',
            canToolCall: true,
        },
        {
            label: 'GLM-4.5【不思考】（ZhiPu）',
            value: 'zhipu_glm-4.5|no_think|',
            title: '上下文128k',
            canToolCall: true,
        },
        {
            label: 'GLM-4.5（ZhiPu）',
            value: 'zhipu_glm-4.5',
            title: '上下文128k',
            canToolCall: true,
        },
        {
            label: 'GLM-4.5-Air【不思考】（ZhiPu）',
            value: 'zhipu_glm-4.5-air|no_think|',
            title: '上下文128k',
            canToolCall: true,
        },
        {
            label: 'GLM-4.5-Air（ZhiPu）',
            value: 'zhipu_glm-4.5-air',
            title: '上下文128k',
            canToolCall: true,
        },
        {
            label: 'GLM-4.5-Flash【不思考】（ZhiPu）',
            value: 'zhipu_glm-4.5-flash|no_think|',
            title: '上下文128k',
            canToolCall: true,
        },
        {
            label: 'GLM-4.5-Flash（ZhiPu）',
            value: 'zhipu_glm-4.5-flash',
            title: '上下文128k',
            canToolCall: true,
        },
        {
            label: 'GLM-4-plus（ZhiPu）',
            value: 'zhipu_glm-4-plus',
            title: '上下文128k',
            canToolCall: true,
        },
        {
            label: 'GLM-4-air（ZhiPu）',
            value: 'zhipu_glm-4-air',
            title: '上下文128k',
            canToolCall: true,
        },
        {
            label: 'GLM-4v-plus-0111（ZhiPu）',
            value: 'zhipu_glm-4v-plus-0111',
            title: '上下文16k',
            canVision: true,
        },
        {
            label: 'GLM-4v-plus（ZhiPu）',
            value: 'zhipu_glm-4v-plus',
            title: '上下文16k',
            canVision: true,
        },
        {
            label: 'GLM-4v（ZhiPu）',
            value: 'zhipu_glm-4v',
            title: '上下文4k',
            canVision: true,
        },
        {
            label: 'Embedding-3（ZhiPu）',
            value: 'zhipu_Embedding-3',
            title: '输入8k，向量维度2048，向量长度1536',
            canEmbed: true,
        },
        /*** MiniMax（官网） ***/
        {
            label: 'MiniMax-Text-01（MiniMax）',
            value: 'minimax_MiniMax-Text-01',
            title: '上下文1000k',
            canVision: true,
            canToolCall: true,
        },
        {
            label: 'abab6.5s-chat（MiniMax）',
            value: 'minimax_abab6.5s-chat',
            title: '上下文245k',
            canVision: true,
            canToolCall: true,
        },
        {
            label: 'DeepSeek-R1（MiniMax）',
            value: 'minimax_DeepSeek-R1',
            title: '上下文245k',
        },
        /*** Moonshot ***/
        {
            label: 'Kimi-k2-0711-preview',
            value: 'msa_kimi-k2-0711-preview',
            title: '上下文13w',
            canToolCall: true,
        },
        {
            label: 'Moonshot-v1-8k',
            value: 'msa_moonshot-v1-8k',
            title: '上下文8k',
            canToolCall: true,
        },
        {
            label: 'Moonshot-v1-32k',
            value: 'msa_moonshot-v1-32k',
            title: '上下文32k',
            canToolCall: true,
        },
        {
            label: 'Moonshot-v1-128k',
            value: 'msa_moonshot-v1-128k',
            title: '上下文128k',
            canToolCall: true,
        },
        {
            label: 'Moonshot-v1-8k',
            value: 'msa_moonshot-v1-8k-vision-preview',
            title: '上下文8k',
            canVision: true,
        },
        {
            label: 'Moonshot-v1-32k',
            value: 'msa_moonshot-v1-32k-vision-preview',
            title: '上下文32k',
            canVision: true,
        },
        {
            label: 'Moonshot-v1-128k',
            value: 'msa_moonshot-v1-128k-vision-preview',
            title: '上下文128k',
            canVision: true,
        },
        /*** OpenAI（Brain） ***/
        {
            label: 'GPT-4.1（Brain）',
            value: 'br_gpt-4.1',
            title: '上下文1047k，输出32k',
            canVision: true,
            canToolCall: true,
        },
        {
            label: 'GPT-4.1-mini（Brain）',
            value: 'br_gpt-4.1-mini',
            title: '上下文1047k，输出32k',
            canVision: true,
            canToolCall: true,
        },
        {
            label: 'GPT-4.1-nano（Brain）',
            value: 'br_gpt-4.1-nano',
            title: '上下文1047k，输出32k',
            canVision: true,
            canToolCall: true,
        },
        {
            label: 'GPT-4o（Brain）',
            value: 'br_gpt-4o',
            title: '上下文128k，输出16k',
            canVision: true,
            canToolCall: true,
        },
        {
            label: 'GPT-4o-mini（Brain）',
            value: 'br_gpt-4o-mini',
            title: '上下文128k，输出16k',
            canVision: true,
            canToolCall: true,
        },
        {
            label: 'o3（Brain）',
            value: 'br_o3',
            title: '上下文200k，输出100k',
        },
        {
            label: 'o3-mini（Brain）',
            value: 'br_o3-mini',
            title: '上下文200k，输出100k',
        },
        {
            label: 'o1（Brain）',
            value: 'br_o1',
            title: '上下文200k，输出100k',
            canVision: true,
            canToolCall: true,
        },
        {
            label: 'o1-mini（Brain）',
            value: 'br_o1-mini',
            title: '上下文128k，输出65k',
            canVision: true,
            canToolCall: true,
        },
        /*** Ollama ***/
        {
            label: 'GPT-OSS-20b（OLL）',
            value: 'ollama_gpt-oss:20b',
            canToolCall: true,
        },
        {
            label: 'Qwen3-32b（OLL）',
            value: 'ollama_qwen3:32b',
            canToolCall: true,
        },
        {
            label: 'Qwen3-32b【不思考】（OLL）',
            value: 'ollama_qwen3:32b|no_think|',
            canToolCall: true,
        },
        {
            label: 'Qwen3-14b（OLL）',
            value: 'ollama_qwen3:14b',
            canToolCall: true,
        },
        {
            label: 'Qwen3-14b【不思考】（OLL）',
            value: 'ollama_qwen3:14b|no_think|',
            canToolCall: true,
        },
        {
            label: 'Qwen3-8b（OLL）',
            value: 'ollama_qwen3:8b',
            canToolCall: true,
        },
        {
            label: 'Qwen3-8b【不思考】（OLL）',
            value: 'ollama_qwen3:8b|no_think|',
            canToolCall: true,
        },
        {
            label: 'Qwen2.5-14b（OLL）',
            value: 'ollama_qwen2.5:14b',
            canToolCall: true,
        },
        {
            label: 'QwQ-32b（OLL）',
            value: 'ollama_qwq:32b',
            canToolCall: true,
        },
        {
            label: 'Gemma3-27b（OLL）',
            value: 'ollama_gemma3:27b',
        },
        {
            label: 'Gemma3-12b（OLL）',
            value: 'ollama_gemma3:12b',
        },
        {
            label: 'DeepSeek-R1-32b（OLL）',
            value: 'ollama_deepseek-r1:32b',
            // title: '上下文13k，输入13k，输出32k',
        },
        {
            label: 'DeepSeek-R1-14b（OLL）',
            value: 'ollama_deepseek-r1:14b',
            // title: '上下文13k，输入13k，输出32k',
        },
        {
            label: 'Command-r7b-7b（OLL）',
            value: 'ollama_command-r7b:7b',
            // title: '上下文13k，输入13k，输出32k',
        },
        {
            label: 'Phi4-32b（OLL）',
            value: 'ollama_phi4:32b',
            // title: '上下文13k，输入13k，输出32k',
        },
        {
            label: 'Phi4-14b（OLL）',
            value: 'ollama_phi4:14b',
            // title: '上下文13k，输入13k，输出32k',
        },
        {
            label: 'Phi4-mini-3.8b（OLL）',
            value: 'ollama_phi4-mini:3.8b',
            // title: '上下文13k，输入13k，输出32k',
            canToolCall: true,
        },
        {
            label: 'DeepSeek-V2-16b（OLL）',
            value: 'ollama_deepseek-v2:16b',
            // title: '上下文13k，输入13k，输出32k',
        },
        {
            label: 'GLM4-9b（OLL）',
            value: 'ollama_glm4:9b',
            // title: '上下文13k，输入13k，输出32k',
        },
        {
            label: 'Llama3.2-Vision-11B（OLL）',
            value: 'ollama_llama3.2-vision:11b',
            // title: '上下文13k，输入13k，输出32k',
        },
        {
            label: 'DeepSeek-coder-v2-16b（OLL）',
            value: 'ollama_deepseek-coder-v2:16b',
            // title: '上下文13k，输入13k，输出32k',
        },
        /*** DeepSeek（Brain-Wild） ***/
        {
            label: 'DeepSeek-R1（Brain-Wild）',
            value: 'brwild_deepseek-r1',
            title: '上下文64k，输出8k',
        },
        /*** Anthropic（Brain-Wild） ***/
        {
            label: 'Claude-3-opus（Brain-Wild）',
            value: 'brwild_claude-3-opus-20240229',
            title: '上下文200k，输出4k',
        },
        {
            label: 'Claude-3.7-sonnet（Brain-Wild）',
            value: 'brwild_claude-3-7-sonnet-20250219',
            title: '上下文200k，输出8k',
        },
        {
            label: 'Claude-3.5-sonnet（Brain-Wild）',
            value: 'brwild_claude-3-5-sonnet-20241022',
            title: '上下文200k，输出8k',
        },
        {
            label: 'Claude-3-sonnet（Brain-Wild）',
            value: 'brwild_claude-3-sonnet-20240229',
            title: '上下文200k，输出4k',
        },
        {
            label: 'Claude-3-haiku（Brain-Wild）',
            value: 'brwild_claude-3-haiku-20240307',
            title: '上下文200k，输出4k',
        },
        /*** OpenAI（Brain-Wild） ***/
        {
            label: 'o3-mini（Brain-Wild）',
            value: 'brwild_o3-mini',
            title: '上下文200k，输出100k',
        },
        // 官网不让用o1
        // {
        //     label: 'o1-128k（Brain）',
        //     value: 'br_o1',
        //     title: '上下文128k，输出100k',
        // },
        {
            label: 'GPT-4o（Brain-Wild）',
            value: 'brwild_gpt-4o',
            title: '上下文128k，输出16k',
            canVision: true,
            canToolCall: true,
        },
        {
            label: 'GPT-4o-mini（Brain-Wild）',
            value: 'brwild_gpt-4o-mini',
            title: '上下文128k，输出16k',
            canVision: true,
            canToolCall: true,
        },
        // {
        //     label: 'o1-mini-128k（Brain-Wild）',
        //     value: 'brwild_o1-mini',
        //     title: '上下文128k，输出65k',
        // },
        // {
        //     label: 'o1-128k（Brain-Wild）',
        //     value: 'brwild_o1',
        //     title: '上下文128k，输出100k',
        // },
        /*** DeepSeek（GJLD） ***/
        {
            label: 'DeepSeek-V3（GJLD）',
            value: 'gjld_deepseek-ai/DeepSeek-V3',
            title: '上下文64k，输出8k',
            canToolCall: true,
        },
        {
            label: 'DeepSeek-R1（GJLD）',
            value: 'gjld_deepseek-ai/DeepSeek-R1',
            title: '上下文64k，输出8k',
        },
    ]
}