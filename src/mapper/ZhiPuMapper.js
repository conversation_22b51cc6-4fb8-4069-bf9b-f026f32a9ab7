import { reqPost<PERSON><PERSON> } from "../util/http-util";
import axios from "axios";
import {TopMapper} from "./TopMapper";

/**
 * 智谱AI映射
 */
export default class ZhiPuMapper extends TopMapper{

    constructor(ctx, cfg) {
        super(ctx);
        this.cfg = cfg;

        this._providerKey = "zhipu_";
        this._apiKey = "a0790040e550677de40498b8e7b267c2.b7Wve14aF9yZvCMP";
        this._url = "https://open.bigmodel.cn";
    }

    isMyModel(model) {
        if (model == null) model = '';

        if (model.startsWith(this._providerKey)) {
            return true;
        }

        return false;
    }

    /**
     * 修复
     * @param {*} pars
     * @returns
     */
    repair(pars) {
        let model = pars.model;
        let messages = pars.messages;

        if (model.startsWith(this._providerKey)) {
            model = model.substring(this._providerKey.length);
        }

        return {
            model: model,
            messages: messages,
        };
    }

    /**
     *
     * @param model
     * @param messages
     * @param cb
     * @param errCb
     * @param opts
     */
    chat(model, messages, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        let self = this;

        // 修复参数
        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        // if (opts.max_tokens == null) opts.max_tokens = 8192;

        let optsProps = '';
        for (const i in opts) {
            if (opts[i] != null) {
                if (i === 'temperature') {
                    optsProps += `${i}:${opts[i]}; `;
                }
                else {
                    optsProps += `${i}; `;
                }
            }
            else {
                delete opts[i];
            }
        }
        if (logLevel == null || logLevel >= 3) log(`调用 ZhiPu.chat ${model} ...（${optsProps}）`);
        let thinking;

        if (model.indexOf('|no_think|') !== -1) {
            model = model.replace('|no_think|', '');
            thinking = { type: 'disabled' };
        }

        const params = {
            model,
            messages: messages,
            temperature: opts.temperature,
            tools: opts.tools,
            max_tokens: opts.max_tokens,
            thinking,
        };

        // log(`${JSON.stringify(params)}`)

        return new Promise(function (resolve, reject) {
            reqPostJson(`${self._url}/api/paas/v4/chat/completions`, params, (r) => {
                if (cb != null) cb(r);
                resolve(r);
            }, (err) => {
                if (errCb != null) errCb(err);
            }, {
                headers: {
                    Authorization: `Bearer ${self._apiKey}`
                }
            });
        });
    }

    /**
     * 聊天（流式）
     * @param model
     * @param messages
     * @param onData
     * @param onEnd
     * @param onError
     * @param opts
     * @returns {Promise<void>}
     */
    async chatStream(model, messages, onData, onEnd, onError, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        let self = this;

        // 修复参数
        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        // if (opts.max_tokens == null) opts.max_tokens = 8192;

        let optsProps = '';
        for (const i in opts) {
            if (opts[i] != null) {
                if (i === 'temperature') {
                    optsProps += `${i}:${opts[i]}; `;
                }
                else {
                    optsProps += `${i}; `;
                }
            }
            else {
                delete opts[i];
            }
        }
        if (logLevel == null || logLevel >= 3) log(`调用 ZhiPu.chatStream ${model} ...（${optsProps}）`);
        let thinking;

        if (model.indexOf('|no_think|') !== -1) {
            model = model.replace('|no_think|', '');
            thinking = { type: 'disabled' };
        }

        const params = {
            model,
            messages: messages,
            temperature: opts.temperature,
            tools: opts.tools,
            max_tokens: opts.max_tokens,
            stream: true, // 启用流式输出
            thinking,
        };

        try {
            const abortController = new AbortController();
            const signal = abortController.signal;

            const response = await axios({
                url: `${self._url}/api/paas/v4/chat/completions`,
                method: 'post',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    'Authorization': `Bearer ${this._apiKey}`,
                    "Accept-Encoding": "gzip,deflate,compress",
                },
                data: params,
                responseType: 'stream', // 设置响应类型为流
                signal,
            });
    
            response.data.on('data', (chunk) => {
                if (onData != null) onData(chunk);
            });
    
            response.data.on('end', () => {
                if (onEnd != null) onEnd();
            });
    
            response.data.on('error', (err) => {
                if (onError != null) onError(err);
            });

            return {
                abortController
            }
        }
        catch(exc) {
            console.error(`chatStream异常 -> ${exc.message}`);
            if (onError != null) onError(new Error(exc.message));
        }
    }

    async embed(model, text, cb, errCb, opts) {
        const { logLevel } = super.repairOpts(opts);
        let self = this;

        // 修复参数
        const repairResult = this.repair({
            model: model,
        });
        model = repairResult.model;

        let optsProps = '';
        for (const i in opts) {
            if (opts[i] != null) {
                optsProps += `${i}; `;
            }
            else {
                delete opts[i];
            }
        }
        if (logLevel == null || logLevel >= 3) log(`调用 ZhiPu.embed | ${model} ...（${optsProps}）`);

        const params = {
            model,
            input: text,
            dimensions: 1536, // 适配OpenAI的向量维度
        };

        return new Promise(function (resolve, reject) {
            reqPostJson(`${self._url}/api/paas/v4/embeddings`, params, (r) => {
                if (cb != null) cb(r);
                resolve(r);
            }, (err) => {
                if (errCb != null) errCb(err);
            }, {
                headers: {
                    Authorization: `Bearer ${self._apiKey}`
                }
            });
        });
    }
}