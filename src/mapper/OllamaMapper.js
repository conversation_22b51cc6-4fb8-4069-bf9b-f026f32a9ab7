import {Ollama} from "ollama";
import axios from "axios";
import {TopMapper} from "./TopMapper";
/**
 * Ollama映射
 */
export default class OllamaMapper extends TopMapper{

    constructor(ctx, cfg) {
        super(ctx);
        this.cfg = cfg;

        // http://test.ovinfo.com:2234
        this._url = this.cfg.host;
    }

    isMyModel(model) {
        if (model == null) model = '';

        if (model.startsWith('ollama_')) return true;

        return false;
    }

    /**
     * 修复
     * @param {*} pars 
     * @returns 
     */
    repair(pars) {
        let model = pars.model;
        let messages = pars.messages;

        if (model.startsWith('ollama_')) {
            model = model.substring('ollama_'.length);
        }

        return {
            model: model,
            messages: messages,
        };
    }

    /**
     * 聊天
     * @param model
     * @param messages {[]}
     * @returns {Promise<ChatResponse>}
     */
    async chat(model, messages, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        if (model == null) model = this.cfg.chatModel;

        // 修复参数
        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        if (model.indexOf('|no_think|') !== -1) {
            model = model.replace('|no_think|', '');
            const userMsg = messages[messages.length - 1];
            userMsg.content += '\n/no_think';
        }

        let optsProps = '';
        for (const i in opts) {
            if (opts[i] != null) {
                optsProps += `${i}; `;
            }
            else {
                delete opts[i];
            }
        }
        if (logLevel == null || logLevel >= 3) log(`调用 Ollama.chat ${model} ...（${optsProps}）`);

        const params = {
            model,
            messages: messages,
            tools: opts.tools,
            options: {
                temperature: opts.temperature,
                max_tokens: opts.max_tokens,
            },
            stream: false,
        };

        try {
            const response = await axios({
                url: `${this._url}/api/chat`,
                method: 'post',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    'Authorization': `Bearer ${this._apiKey}`,
                    "Accept-Encoding": "gzip,deflate,compress",
                },
                data: params,
            });

            // { message: { role, content }, model }
            const result = response.data;
            
            if (cb) cb(result);
            return result;
        }
        catch(exc) {
            if (errCb) errCb(exc);
        }
    }
    async chatSync(model, messages, cb, errCb, opts = {}) {
        return await this.chat(model, messages, cb, errCb, opts);
    }

    /**
     * 聊天（流式输出）
     * @param model
     * @param messages {[]}
     * @param onData
     * @param onEnd
     * @param onError
     */
    async chatStream(model, messages, onData, onEnd, onError, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        if (model == null) model = this.cfg.chatModel;

        // 修复参数
        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        if (model.indexOf('|no_think|') !== -1) {
            model = model.replace('|no_think|', '');
            const userMsg = messages[messages.length - 1];
            userMsg.content += '\n/no_think';
        }

        let optsProps = '';
        for (const i in opts) {
            if (opts[i] != null) {
                optsProps += `${i}; `;
            }
            else {
                delete opts[i];
            }
        }
        if (logLevel == null || logLevel >= 3) log(`调用 Ollama.chatStream ${model} ...（${optsProps}）`);

        try {
            const params = {
                model,
                messages: messages,
                tools: opts.tools,
                options: {
                    temperature: opts.temperature,
                    max_tokens: opts.max_tokens,
                },
                stream: true,
            };

            const abortController = new AbortController();
            const signal = abortController.signal;

            const response = await axios({
                url: `${this._url}/api/chat`,
                method: 'post',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    'Authorization': `Bearer ${this._apiKey}`,
                    "Accept-Encoding": "gzip,deflate,compress",
                },
                data: params,
                responseType: 'stream',
                signal,
            });
    
            response.data.on('data', (chunk) => {
                if (onData != null) onData(chunk);
            });
    
            response.data.on('end', () => {
                if (onEnd != null) onEnd();
            });
    
            response.data.on('error', (err) => {
                if (onError != null) onError(err);
            });

            return {
                abortController
            }
        } catch (error) {
            if (onError) {
                onError(error); // 在try-catch中捕获到的错误也通过onError回调处理
            } else {
                throw error; // 如果没有提供onError回调，则重新抛出错误
            }
        }
    }
    async chatStreamSync(model, messages, onData, onEnd, onError, opts) {
        return await this.chatStream(model, messages, onData, onEnd, onError, opts);
    }


    /**
     * 嵌入
     * @param model
     * @param input {string}
     * @returns {Promise<>}
     */
    async embed(model, input, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        if (model == null) model = this.cfg.embedModel;

        if (logLevel == null || logLevel >= 3) log(`调用 Ollama.embed ...（${model}）`);

        const ollama = new Ollama({ host: this.cfg.host });
        // 修复参数
        const repairResult = this.repair({
            model: model,
        });
        model = repairResult.model;

        const result = await ollama.embed({
            model: model,
            input: input,
            options: {
                temperature: 0,
            },
        });

        return result;
    }

    // /**
    //  * 嵌入
    //  * @param model
    //  * @param input {string}
    //  * @returns {Promise<EmbedResponse>}
    //  */
    // async embedSync(model, input) {
    //     if (model == null) model = this.cfg.embedModel;
    //     const ollama = new Ollama({ host: this.cfg.host });
    //
    //     // console.log(`请求 embedSync | model: ${model}, input: ${input}`);
    //
    //     // 修复参数
    //     const repairResult = this.repair({
    //         model: model,
    //     });
    //     model = repairResult.model;
    //
    //     const result = await ollama.embed({
    //         model: model,
    //         input: input,
    //         options: {
    //             temperature: 0,
    //         },
    //     });
    //
    //     // console.log(`embedSync 返回结果: ${JSON.stringify(result)}`);
    //
    //     return result;
    // }
}