import {reqPost<PERSON><PERSON>} from "../util/http-util";
import {TopMapper} from "./TopMapper";
import axios from "axios";

/**
 * 月之暗面（kimi）
 */
export default class MoonshotAIMapper extends TopMapper{

    constructor(ctx, cfg) {
        super(ctx);
        this.cfg = cfg;

        this._providerKey = "msa_";
        this._apiKey = 'sk-XBGeJ1gkKe22SqyMlEaWKVmDDDAqhtxJ8pCOUl5UYWYN5I5P';
        this._url = 'https://api.moonshot.cn/v1';
    }

    isMyModel(model) {
        if (model == null) model = '';

        if (model.startsWith(this._providerKey)) {
            return true;
        }
        if (model.startsWith('moonshot-')) return true;

        return false;
    }

    /**
     * 修复
     * @param {*} pars
     * @returns
     */
    repair(pars) {
        let model = pars.model;
        let messages = pars.messages;

        if (model.startsWith(this._providerKey)) {
            model = model.substring(this._providerKey.length);
        }

        return {
            model: model,
            messages: messages,
        };
    }

    /**
     * 聊天
     * @param model
     * @param messages
     * @param cb
     * @param errCb
     * @param opts
     */
    async chat(model, messages, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        const self = this;

        // 修复参数
        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        let optsProps = '';
        for (const i in opts) {
            optsProps += `${i}; `;
        }
        if (logLevel == null || logLevel >= 3) log(`调用 Moonshot.chat ...（${optsProps}）`);

        return new Promise((resolve, reject) => {
            reqPostJson(this._url.concat('/chat/completions'), {
                model: model,
                messages: messages,
                temperature: opts.temperature,
                max_tokens: opts.max_tokens,
                tools: opts.tools,
            }, (r) => {
                if (cb != null) cb(r);
                resolve(r);
            }, (err) => {
                if (errCb != null) errCb(err);
            }, {
                headers: {
                    Authorization: `Bearer ${self._apiKey}`
                }
            });
        });
    }

    async chatStream(model, messages, onData, onEnd, onError, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        let self = this;

        // 修复参数
        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        // if (opts.max_tokens == null) opts.max_tokens = 8192;

        let optsProps = '';
        for (const i in opts) {
            optsProps += `${i}; `;
        }
        if (logLevel == null || logLevel >= 3) log(`调用 Moonshot.chatStream ${model} ...（${optsProps}）`);

        const params = {
            model,
            messages: messages,
            temperature: opts.temperature,
            tools: opts.tools,
            max_tokens: opts.max_tokens,
            stream: true, // 启用流式输出
        };

        try {
            const abortController = new AbortController();
            const signal = abortController.signal;

            const response = await axios({
                url: self._url.concat('/chat/completions'),
                method: 'post',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    'Authorization': `Bearer ${self._apiKey}`,
                    "Accept-Encoding": "gzip,deflate,compress",
                },
                data: params,
                responseType: 'stream', // 设置响应类型为流
                signal,
            });

            response.data.on('data', (chunk) => {
                if (onData != null) onData(chunk);
            });

            response.data.on('end', () => {
                if (onEnd != null) onEnd();
            });

            response.data.on('error', (err) => {
                if (onError != null) onError(err);
            });

            return {
                abortController
            }
        }
        catch(exc) {
            console.error(`chatStream异常 -> ${exc.message}`);
            if (onError != null) onError(new Error(exc.message));
        }
    }

    // /**
    //  * 聊天（流式）
    //  * @param model
    //  * @param messages
    //  * @param onData
    //  * @param onEnd
    //  * @param onError
    //  * @param opts
    //  * @returns {Promise<void>}
    //  */
    // async chatStream111(model, messages, onData, onEnd, onError, opts = {}) {
    //     const { logLevel } = super.repairOpts(opts);
    //     const { OpenAI } = this.ctx.imports;
    //     const self = this;
    //
    //     // 修复参数
    //     const repairResult = this.repair({
    //         model: model,
    //         messages: messages,
    //     });
    //     model = repairResult.model;
    //     messages = repairResult.messages;
    //
    //     let optsProps = '';
    //     for (const i in opts) {
    //         optsProps += `${i}; `;
    //     }
    //     if (logLevel == null || logLevel >= 3) log(`调用 Moonshot.chatStream ${model} ...（${optsProps}）`);
    //
    //     const params = {
    //         model,
    //         messages: messages,
    //         temperature: opts.temperature,
    //         tools: opts.tools,
    //         max_tokens: opts.max_tokens,
    //         stream: true, // 启用流式输出
    //     };
    //
    //     try {
    //         const client = new OpenAI({
    //             apiKey: self._apiKey,
    //             baseURL: self._url,
    //         });
    //
    //         const stream = await client.chat.completions.create(params);
    //         for await (const chunk of stream) {
    //             // chunk是对象，需要转为字符串返回
    //             // delta: { role, content }
    //             if (onData != null) onData(JSON.stringify(chunk.choices[0].delta));
    //         }
    //
    //         if (onEnd != null) onEnd();
    //     } catch (exc) {
    //         if (onError != null) onError(exc);
    //     }
    // }
}