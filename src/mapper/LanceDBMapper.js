// import * as lancedb from "@lancedb/lancedb";
// import { Schema, Field, Float32, Utf8, FixedSizeList } from "apache-arrow";

/**
 * LanceDB映射
 */
export default class LanceDBMapper {

    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     * 连接数据库
     * @param dbDirPath 数据库目录路径，例：/data/lancedb/
     * @returns {Promise<Connection>}
     */
    async connectSync(dbDirPath) {
        const lancedb = this.ctx.imports.lancedb;
        return await lancedb.connect(dbDirPath);
    }

    /**
     * 向表中添加字段（估计是老版的）
     * @param conn
     * @param tableName
     * @param fieldName
     * @param fieldType
     * @returns {Promise<void>}
     */
    async addFieldToTable(conn, tableName, fieldName, fieldType) {
        const tbl = await conn.openTable(tableName);
        await tbl.addColumns([{
            name: fieldName,
            type: fieldType,
            valueSql: "''"
        }]);
    }

    /**
     * 向表中添加字段（估计是新版的）
     * @param conn
     * @param tableName
     * @param fieldName
     */
    // async addFieldToTable(conn, tableName, fieldName, fieldType) {
    //     const tbl = await conn.openTable(tableName);
    //     await tbl.addColumns([{
    //         name: fieldName,
    //         expression: `CAST(NULL AS ${fieldType})`
    //     }]);
    // }

    /**
     * 向量搜索
     * @param conn
     * @param tableName
     * @param fieldName
     * @param kwV {number[]} 关键词向量
     * @param limit {number}
     * @returns {Promise<[]>}
     */
    async vectorSearchSync(conn, tableName, fieldName, kwV, limit) {
        const tbl = await conn.openTable(tableName);
        return tbl.vectorSearch(kwV).column(fieldName).limit(limit).toArray();
        // return tbl.vectorSearch().column(fieldName).query(kwV).limit(limit).execute();
    }

    /**
     * 查询多个关键词（或）
     * @param conn
     * @param tableName
     * @param fieldName
     * @param kwList
     * @param limit
     * @returns {Promise<any[]>}
     */
    async searchMultiKwSync(conn, tableName, fieldName, kwList, limit) {
        const tbl = await conn.openTable(tableName);

        let whereLikeStr = '';
        for (const i in kwList) {
            if (parseInt(i) > 0) {
                whereLikeStr += 'or ';
            }
            whereLikeStr += `${fieldName} like '%${kwList[i]}%' `;
        }

        return tbl.query().where(whereLikeStr).limit(limit).toArray();
    }

    /**
     * 创建空表
     * 如果已存在此表会报错
     * @param conn {Connection}
     * @param tableName {string} 表名
     * @param fields {[]} 表字段，例：[{ name: "id", type: "string" }, { name: "vector", type: "float32-array", (listSize): 1024 }]
     * @returns {Promise<Table>}
     */
    async createEmptyTableSync(conn, tableName, fields) {
        const { Schema, Field, Float32, Utf8, FixedSizeList } = this.ctx.imports.apacheArrow;

        // // 删除老表
        // try {
        //     await conn.dropTable(tableName);
        // } catch(e) {}
        // 定义字段
        const lanceDBFields = [];
        for (const field of fields) {
            let lanceDBType;
            if (field.type === 'float32-array') {
                let listSize = 1024;
                if (field.listSize) {
                    listSize = field.listSize;
                }
                lanceDBType = new FixedSizeList(listSize, new Field('item', new Float32(), false));
                log(`创建列：${field.name}, ${listSize}`);
            }
            else {
                lanceDBType = new Utf8();
            }

            const lanceDBField = new Field(field.name, lanceDBType, false);
            lanceDBFields.push(lanceDBField);
        }
        // 创建空表
        return conn.createEmptyTable(tableName, new Schema(lanceDBFields), {
            // existOk: true, // 如果为true，则在create模式下不会报错，不做任何操作
            mode: "create",
            // mode: "overwrite",
        });
    }

    /**
     * 获取表数据
     * @param conn
     * @param tableName
     * @param where
     * @returns {Promise<void>}
     */
    async getTableRows(conn, tableName, where) {
        const tbl = await conn.openTable(tableName);
        return tbl.query().where(where).toArray();
    }

    /**
     * 添加数据到表
     * @param conn
     * @param tableName
     * @param rows {[]} 数据，例：[{ id: "1", title_vector: [0.23, 0.45] }]
     * @returns {Promise<void>}
     */
    async addRowsToTable(conn, tableName, rows) {
        const tbl = await conn.openTable(tableName);
        return tbl.add(rows);
    }

    /**
     * 更新表数据
     * @param conn
     * @param tableName
     * @param where {string} 例：id = "1"
     * @param values {*} 例：{ title_vector: [0.23, 0.45] }
     * @returns {Promise<void>}
     */
    async updateRowInTable(conn, tableName, where, values) {
        const tbl = await conn.openTable(tableName);
        return tbl.update(where, values);
    }

    /**
     * 删除表数据
     * @param conn
     * @param tableName
     * @param where {string} 例：id = "1"
     * @returns {Promise<*>}
     */
    async deleteRowInTable(conn, tableName, where) {
        const tbl = await conn.openTable(tableName);
        return tbl.delete(where);
    }

    /**
     * 清空表数据
     * @param conn
     * @param tableName
     * @returns {Promise<*>}
     */
    async clearRowsInTable(conn, tableName) {
        const tbl = await conn.openTable(tableName);
        return tbl.delete(`true`);
    }
}