import {reqPost<PERSON>son} from "../util/http-util";
import axios from "axios";
import {TopMapper} from "./TopMapper";

/**
 * OpenAI映射
 */
export default class OpenAIBrainWildMapper extends TopMapper{

    constructor(ctx, cfg) {
        super(ctx);
        this.cfg = cfg;

        //（推荐）.../api/wild/openai
        this._url1 = cfg.url1;
        // .../api/wild/chatGpt
        this._url = cfg.url;
    }

    isMyModel(model) {
        if (model == null) model = '';

        if (model.startsWith('brwild_gpt')) return true;
        if (model.startsWith('brwild_o1')) return true;
        if (model.startsWith('brwild_o3')) return true;
        if (model.startsWith('brwild_o4')) return true;
        if (model.startsWith('brwild_text-embedding-3-small')) return true;

        // 兼容旧模型
        if (model.startsWith('gpt')) return true;
        if (model.startsWith('o1')) return true;
        if (model.startsWith('o3')) return true;

        return false;
    }

    /**
     * 修复
     * @param {*} pars 
     * @returns 
     */
    repair(pars) {
        let model = pars.model;
        let messages = pars.messages;

        if (model.startsWith('brwild_')) {
            model = model.substring('brwild_'.length);
        }

        if (model.indexOf('o1-') !== -1 || model.indexOf('o3-') !== -1) {
            for (const message of messages) {
                if (message.role === 'system') {
                    message.role = 'user';
                }
            }
        }

        return {
            model: model,
            messages: messages,
        };
    }

    /**
     *
     * @param model
     * @param messages
     * @param cb
     * @param errCb
     * @param opts
     */
    chat1(model, messages, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);

        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        let optsProps = '';
        for (const i in opts) {
            optsProps += `${i}; `;
        }
        if (logLevel == null || logLevel >= 3) log(`调用 OpenAIBrainWild.chat1 ${model} ...（${optsProps}）`);

        reqPostJson(this._url1, {
            ink: 'abc131', sac: 'cz.cn', un: 'sys_acc_0', pwd: 'd9d0b4b4',
            cmd: '/v1/chat/completions',
            params: {
                model: model, messages: messages,
                ...opts,
            },
        }, (r) => {
            if (r.success) {
                if (cb != null) cb(r.data);
            }
            else {
                if (errCb != null) errCb({ message: r.msg });
            }
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
            }
        });
    }

    /**
     *
     * @param {*} model
     * @param {*} messages
     * @param {*} opts
     * @param cb
     * @param errCb
     * @returns
     */
    chat1Sync(model, messages, opts, cb, errCb) {
        const self = this;
        return new Promise((resolve, reject) => {
            self.chat1(model, messages, (r) => {
                if (cb) cb(r);
                resolve(r);
            }, (err) => {
                if (errCb) errCb(err);
            }, opts);
        });
    }

    /**
     * 聊天
     * @param model
     * @param messages
     * @param cb
     * @param errCb
     */
    chat(model, messages, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);

        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        if (logLevel == null || logLevel >= 3) log(`调用 OpenAIBrainWild.chat ${model} ...`);

        reqPostJson(this._url, {
            ink: 'abc131', sac: 'cz.cn', un: 'sys_acc_0', pwd: 'd9d0b4b4',
            // cmd: 'v1/chat/completions',
            model: model, msgs: messages
        }, (r) => {
            if (cb != null) cb(r);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
            }
        });
    }

    /**
     *
     * @param model
     * @param messages
     * @returns {Promise<unknown>}
     */
    chatSync(model, messages) {
        const self = this;

        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        return new Promise((resolve, reject) => {
            self.chat(model, messages, (r) => {
                resolve(r);
            }, (err) => {
            });
        });
    }

    /**
     *
     * @param model
     * @param messages
     * @param onData
     * @param onEnd
     * @param onError
     * @param opts
     * @return {Promise<void>}
     */
    async chatStream(model, messages, onData, onEnd, onError, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        try {
            const repairResult = this.repair({
                model: model,
                messages: messages,
            });
            model = repairResult.model;
            messages = repairResult.messages;

            let optsProps = '';
            for (const i in opts) {
                optsProps += `${i}; `;
            }
            if (logLevel == null || logLevel >= 3) log(`调用 OpenAIBrainWild.chatStream ${model} ...（${optsProps}）`);

            const params = {
                ink: 'abc131', sac: 'cz.cn', un: 'sys_acc_0', pwd: 'd9d0b4b4',
                model,
                msgs: messages,
                ...opts,
            };

            const abortController = new AbortController();
            const signal = abortController.signal;

            const response = await axios({
                url: `${this._url}Stream`,
                method: 'post',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    "Accept-Encoding": "gzip,deflate,compress",
                },
                data: params,
                responseType: 'stream', // 设置响应类型为流
                signal,
            });
    
            response.data.on('data', (chunk) => {
                if (onData != null) onData(chunk);
            });
    
            response.data.on('end', () => {
                if (onEnd != null) onEnd();
            });
    
            response.data.on('error', (err) => {
                if (onError != null) onError(err);
            });

            return {
                abortController
            }
        } 
        catch(exc) {
            console.error(`chatStream异常 -> ${exc.message}`);
            if (onError != null) onError(new Error(exc.message));
        }
    }


    /**
     * 向量化内容
     * @param model
     * @param text
     * @param cb {*} { model, data: [{ embedding: [...] }], usage: { prompt_tokens, total_tokens } }
     * @param errCb
     */
    async embed(model, text, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);

        if (logLevel == null || logLevel >= 3) log(`调用 OpenAIBrainWild.embed ${model} ...（文本长度：${text.length}）`);
        return new Promise((resolve, reject) => {
            reqPostJson(this.cfg.url1, {
                ink: 'abc131', sac: 'cz.cn', un: 'sys_acc_0', pwd: 'd9d0b4b4',
                cmd: '/v1/embeddings', params: {
                    model: model, input: text,
                }
            }, (r) => {
                if (r.success === false) {
                    if (cb != null) cb(r);
                    resolve(r)
                }
                else {
                    if (cb != null) cb(r.data);
                    resolve(r.data)
                }
            }, (err) => {
                if (errCb != null) errCb(err);
            }, {
                headers: {}
            });
        });
    }

    // async embedSync(model, text, cb, errCb) {
    //     const self = this;
    //     return new Promise((resolve, reject) => {
    //         self.embed(model, text, (r) => {
    //             resolve(r);
    //         }, (err) => {
    //             if (errCb) errCb(err);
    //         });
    //     });
    // }
}