import {reqP<PERSON><PERSON><PERSON>} from "../util/http-util";
// import FormData from "_form-data@4.0.0@form-data";
import FormData from "form-data";
import axios from "axios";
import {TopMapper} from "./TopMapper";

/**
 * 百度千帆AppBuilder
 */
export default class BaiduQianFanAppBuilderMapper extends TopMapper{

    constructor(ctx, cfg) {
        super(ctx);
        this.cfg = cfg;

        this.url = cfg.url;
        this.apikey = cfg.apikey;
    }

    /**
     * 与Agent应用对话
     * @param appId
     * @param message
     * @param cb {*} cb({ answer })
     * @param errCb
     */
    chatAgent(appId, message, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        const self = this;
        if (opts.stream == null) opts.stream = false;

        this.createConversation(appId, (resp) => {
            let conversationId = resp.conversation_id;

            reqPostJson(self._buildReqUrl(`/v2/app/conversation/runs`), {
                app_id: appId, query: message, stream: opts.stream, conversation_id: conversationId, file_ids: [],
            }, (resp) => {
                if (cb != null) cb(resp);
            }, (err) => {
                if (errCb != null) errCb(err);
            }, {
                headers: {
                    Authorization: `Bearer ${self.apikey}`
                }
            });

        }, (err) => {
            if (errCb != null) errCb(err);
        });
    }

    /**
     * 与Agent应用对话(流式)
     * @param appId
     * @param message
     * @param cb
     * @param errCb
     * @param opts
     */
    chatAgentStream(appId, message, cb, errCb, opts = {}) {
        const self = this;

        this.createConversation(appId, (resp) => {
            let conversationId = resp.conversation_id;

            const reqResp = reqPostJson(self._buildReqUrl(`/v2/app/conversation/runs`), {
                app_id: appId, query: message, stream: true, conversation_id: conversationId, file_ids: [],
            }, (resp) => {
                if (cb != null) cb(resp);
            }, (err) => {
                if (errCb != null) errCb(err);
            }, {
                headers: {
                    Authorization: `Bearer ${self.apikey}`
                },
                responseType: 'stream'
            });

            // if (cb != null) cb(reqResp);
        }, (err) => {
            if (errCb != null) errCb(err);
        });
    }

    /**
     * 创建会话
     * @param appId
     * @param cb {*} cb({ request_id, conversation_id })
     * @param errCb
     */
    createConversation(appId, cb, errCb) {
        reqPostJson(this._buildReqUrl(`/v2/app/conversation`), {
            app_id: appId
        }, (resp) => {
            if (cb != null) cb(resp);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
                Authorization: `Bearer ${this.apikey}`
            }
        });
    }

    /**
     * 获取文档列表
     * @param kbId 知识库ID
     * @param marker 起始位置，知识库文档id（用于翻页）
     * @param maxKeys 数据大小，默认10，最大值100
     * @param cb {*} cb({ data: [{ id, name, meta }] })
     * @param errCb {*} errCb({ requestId, message, code })
     */
    getDocumentList(kbId, marker, maxKeys, cb, errCb) {
        reqPostJson(this._buildReqUrl(`/v2/knowledgeBase?Action=DescribeDocuments`), {
            knowledgeBaseId: kbId,
            marker: marker,
            maxKeys: maxKeys,
        }, (resp) => {
            if (cb != null) cb(resp);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
                Authorization: `Bearer ${this.apikey}`
            }
        });
    }

    /**
     * 上传文档到知识库
     * @param kbId
     * @param filePath
     * @param cb {*} cb({ requestId, documentId })
     * @param errCb {*} errCb({ requestId, message, code })
     */
    uploadDocumentByPath(kbId, filePath, cb, errCb) {
        const { fs } = this.ctx.imports;
        const fileName = filePath.split('/').pop();
        const fileData = fs.readFileSync(filePath);
        this.uploadDocument(kbId, fileName, fileData, cb, errCb);
    }

    /**
     * 上传文档到知识库
     * @param kbId
     * @param fileName
     * @param fileData
     * @param cb {*} cb({ requestId, documentId })
     * @param errCb {*} errCb({ requestId, message, code })
     */
    uploadDocument(kbId, fileName, fileData, cb, errCb) {
        const self = this;

        const formData = new FormData();
        formData.append('file', fileData, {
            filename: fileName, // 传递文件名
            contentType: 'application/octet-stream', // MIME 类型
        });

        // 添加 payload
        const payload = {
            id: kbId,
            source: {
                type: "file"
            },
            contentFormat: "rawText", // rawText (允许配置后续分割策略), qa(不支持配置后续分割策略)
            processOption: {
                template: "ppt", // 可选项为 ppt、paper、qaPair、resume、custom、default
                parser: {
                    choices: ["layoutAnalysis", "ocr"]
                },
                chunker: {
                    choices: ["separator"],
                    separator: {
                        separators: [",", "!", "?"],
                        targetLength: 333,
                        overlapRate: 0.09
                    },
                    // "pattern": {   #当choices中包含 pattern时需要填写这个参数
                    //   "markPosition": "head",
                    //   "regex": "正则表达式",
                    //   "targetLength": 333,
                    //   "overlapRate": 0.09
                    //},
                    prependInfo: ["title", "filename"]
                },
                knowledgeAugmentation: {
                    choices: ["faq"],
                }
            }
        };

        // 将 payload 转换为 JSON 字符串
        formData.append('payload', JSON.stringify(payload));

        // 使用 axios 上传文件
        axios.post(self._buildReqUrl(`/v2/knowledgeBase?Action=UploadDocuments`), formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
                ...formData.getHeaders(), // 获取并设置正确的请求头
                Authorization: `Bearer ${this.apikey}`,
            },
        }).then((resp) => {
            if (cb != null) cb(resp.data);
        }).catch((err) => {
            if (errCb != null) errCb(err);
        });
    }

    /**
     * 删除知识库中文档
     * @param kbId
     * @param docId
     * @param cb {*} cb({ requestId })
     * @param errCb {*} errCb({ requestId, message, code })
     */
    deleteDocument(kbId, docId, cb, errCb) {
        reqPostJson(this._buildReqUrl(`/v2/knowledgeBase?Action=DeleteDocument`), {
            knowledgeBaseId: kbId, documentId: docId
        }, (resp) => {
            if (cb != null) cb(resp);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
                Authorization: `Bearer ${this.apikey}`
            }
        });
    }

    _buildReqUrl(cmd) {
        return this.url.concat(cmd);
    }
}