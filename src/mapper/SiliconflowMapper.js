import { reqPost<PERSON><PERSON> } from "../util/http-util";
import axios from "axios";
import {TopMapper} from "./TopMapper";

/**
 * Siliconflow（硅基流动）映射
 */
export default class SiliconflowMapper extends TopMapper{

    constructor(ctx, cfg) {
        super(ctx);
        this.cfg = cfg;

        this._apiKey = "sk-nwutbbspkwpwtadqisvrnhuixebzbnxqiedojfysxuiqcvvc";
        this._url = "https://api.siliconflow.cn";
    }

    isMyModel(model) {
        if (model == null) model = '';

        if (model.startsWith('gjld_')) {
            return true;
        }

        return false;
    }

    /**
     * 修复
     * @param {*} pars 
     * @returns 
     */
    repair(pars) {
        let model = pars.model;
        let messages = pars.messages;

        if (model.startsWith('gjld_')) {
            model = model.substring('gjld_'.length);
        }

        return {
            model: model,
            messages: messages,
        };
    }

    /**
     *
     * @param model
     * @param messages
     * @param cb
     * @param errCb
     * @param opts
     */
    chat(model, messages, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        let self = this;

        // 修复参数
        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        if (opts.max_tokens == null) opts.max_tokens = 4096;

        let optsProps = '';
        for (const i in opts) {
            optsProps += `${i}; `;
        }
        if (logLevel == null || logLevel >= 3) log(`调用 Siliconflow.chat ${model} ...（${optsProps}）`);

        const params = {
            model,
            messages: messages,
            tools: opts.tools,
            temperature: opts.temperature,
            max_tokens: opts.max_tokens,
        };

        return new Promise(function (resolve, reject) {
            reqPostJson(`${self._url}/chat/completions`, params, (r) => {
                if (cb != null) cb(r);
                resolve(r);
            }, (err) => {
                if (errCb != null) errCb(err);
            }, {
                headers: {
                    Authorization: `Bearer ${self._apiKey}`
                }
            });
        });
    }

    /**
     * 聊天（流式）
     * @param model
     * @param messages
     * @param onData
     * @param onEnd
     * @param onError
     * @param opts
     * @returns {Promise<void>}
     */
    async chatStream(model, messages, onData, onEnd, onError, opts) {
        const { logLevel } = super.repairOpts(opts);
        if (opts == null) opts = {};

        // 修复参数
        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        if (opts.max_tokens == null) opts.max_tokens = 4096;

        let optsProps = '';
        for (const i in opts) {
            optsProps += `${i}; `;
        }
        if (logLevel == null || logLevel >= 3) log(`调用 Siliconflow.chatStream ${model} ...（${optsProps}）`);

        const params = {
            model,
            messages: messages,
            tools: opts.tools,
            temperature: opts.temperature,
            max_tokens: opts.max_tokens,
            stream: true, // 启用流式输出
        };

        try {
            const abortController = new AbortController();
            const signal = abortController.signal;

            const response = await axios({
                url: `${this._url}/chat/completions`,
                method: 'post',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    'Authorization': `Bearer ${this._apiKey}`,
                    "Accept-Encoding": "gzip,deflate,compress",
                },
                data: params,
                responseType: 'stream', // 设置响应类型为流
                signal,
            });

            response.data.on('data', (chunk) => {
                if (onData != null) onData(chunk);
            });
    
            response.data.on('end', () => {
                if (onEnd != null) onEnd();
            });
    
            response.data.on('error', (err) => {
                if (onError != null) onError(err);
            });

            return {
                abortController
            }
        }
        catch(exc) {
            console.error(`chatStream异常 -> ${exc.message}`);
            if (onError != null) onError(new Error(exc.message));
        }
    }
}