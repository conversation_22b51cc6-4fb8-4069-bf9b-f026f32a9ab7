import {reqGet, reqPostJson} from "../util/http-util";

/**
 * 基于 llama-index + openai
 */
export default class CSBotKb0Mapper {

    constructor(ctx, cfg) {
        this.ctx = ctx;
        this.url = cfg.url;
        this.innerKey = cfg.innerKey;
    }

    /**
     * 聊天
     * @param kbId 知识库id
     * @param content 聊天内容
     * @param cb
     * @param errCb
     */
    chat(kbId, content, cb, errCb) {
        reqPostJson(this._buildReqUrl('/kbmgra/chatBot'), {
            kb_id: kbId, input_text: content
        }, (r) => {
            if (cb != null) cb(r);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
            }
        });
    }

    /**
     * 设置知识碎片
     * @param kbId 知识库id
     * @param kfId 知识碎片id
     * @param content 碎片内容
     * @param cb
     * @param errCb
     */
    setKf(kbId, kfId, content, cb, errCb) {
        reqPostJson(this._buildReqUrl('/kbmgra/setZSSP'), {
            kb_id: kbId, zssp_id: kfId, zssp_con: content
        }, (r) => {
            if (cb != null) cb(r);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
            }
        });
    }

    /**
     * 删除知识碎片
     * @param kbId 知识库id
     * @param kfId 知识碎片id
     * @param cb
     * @param errCb
     */
    delKf(kbId, kfId, cb, errCb) {
        reqPostJson(this._buildReqUrl('/kbmgra/delZSSP'), {
            kb_id: kbId, zssp_id: kfId
        }, (r) => {
            if (cb != null) cb(r);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
            }
        });
    }

    /**
     * 清空知识碎片
     * @param kbId 知识库id
     * @param cb
     * @param errCb
     */
    clearKf(kbId, cb, errCb) {
        reqPostJson(this._buildReqUrl('/kbmgra/clearZSSP'), {
            kb_id: kbId
        }, (r) => {
            if (cb != null) cb(r);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
            }
        });
    }

    /**
     * 全量训练
     * @param kbId 知识库id
     * @param cb
     * @param errCb
     */
    fullTraining(kbId, cb, errCb) {
        reqPostJson(this._buildReqUrl('/kbmgra/trainKB'), {
            kb_id: kbId
        }, (r) => {
            if (cb != null) cb(r);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
            }
        });
    }

    _buildReqUrl(cmd) {
        return this.url.concat(cmd);
    }
}