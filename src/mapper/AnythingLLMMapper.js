import {reqGet, reqPostJson} from "../util/http-util";
import {TopMapper} from "./TopMapper";
const fs = require('fs');
const axios = require('axios');
const FormData = require('form-data');

/**
 * AnythingLLM接口映射
 */
export default class AnythingLLMMapper extends TopMapper{

    constructor(ctx, cfg) {
        super(ctx);
        this.url = cfg.url;
        this.apikey = cfg.apikey;
        this.workDirPath = cfg.workDirPath;
    }

    /**
     * 校验API秘钥是否正确
     * @param apikey
     * @param cb
     * @param errCb {*} ({ status, data: { error } })
     */
    auth(apikey, cb, errCb) {
        if (apikey == null) apikey = this.apikey;
        reqGet(this._buildReqUrl('/api/v1/auth'), {
        }, (r) => {
            if (cb != null) cb(r);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
                Authorization: `Bearer ${apikey}`
            }
        });
    }

    /**
     * 聊天（不推荐，用的是工作空间的默认线程）
     * @param slug 工作空间id
     * @param message 内容
     * @param mode 模式：chat | query
     * @param cb cb({ textResponse, sources: [{ title }], error, type, id })
     * @param errCb {*} ({ status, data: { error } })
     */
    chat(slug, message, mode, cb, errCb) {
        reqPostJson(this._buildReqUrl(`/api/v1/workspace/${slug}/chat`), {
            message: message, mode: mode,
        }, (resp) => {
            if (cb != null) cb(resp);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
                Authorization: `Bearer ${this.apikey}`
            }
        });
    }

    /**
     * 临时线程聊天（推荐）
     * @param slug 工作空间id
     * @param message 内容
     * @param mode 模式：chat | query
     * @param cb cb({ textResponse, sources: [{ title }], error, type, id })
     * @param errCb {*} ({ status, data: { error } })
     */
    tempThreadChat(slug, message, mode, cb, errCb) {
        const self = this;
        let threadSlug = null;
        // 创建新线程
        self.newWorkspaceThread(slug, (r) => {
            threadSlug = r.thread.slug;
            // 聊天
            self.chatWorkspaceThread(slug, threadSlug, message, mode, (r) => {
                const chatRes = r;
                // 删除线程
                self.deleteWorkspaceThread(slug, threadSlug, (r) => {
                    // log(`删除聊天线程成功 -> ${JSON.stringify(r)}`);
                }, (err) => {
                    // log(`删除聊天线程失败 -> ${err.response.data.message}`);
                });
                if (cb != null) cb(chatRes);
            }, (err) => {
                if (errCb != null) errCb(err);
            });
        }, (err) => {
            if (errCb != null) errCb(err);
        });
    }

    /**
     * 更新文本内容到工作空间
     * @param kbId
     * @param kfId 知识碎片id
     * @param slug 工作空间id
     * @param rawText 文本内容
     * @param cb cb(<location>)
     * @param errCb
     */
    setWorkspaceRawText(kbId, kfId, slug, rawText, cb, errCb) {
        const self = this;
        let fileName = `kf_${kbId}_${kfId}_raw-text`;
        // 删除原本的文件
        self.deleteWorkspaceRawText(kbId, kfId, slug, () => {
            // 更新新文件
            reqPostJson(self._buildReqUrl(`/api/v1/document/raw-text`), {
                textContent: rawText,
                metadata: {
                    title: fileName,
                }
            }, (resp) => {
                // resp: { success, documents: [{ location: 'custom-documents/xxx.json' }] }
                const location = resp.documents[0].location;
                self.updateEmbeddings(slug, [location], [], (resp) => {
                    if (cb != null) cb(location);
                }, (err) => {
                    if (errCb != null) errCb(err);
                });
            }, (err) => {
                if (errCb != null) errCb(err);
            }, {
                headers: {
                    Authorization: `Bearer ${self.apikey}`
                }
            });
        }, (err) => {
            if (errCb != null) errCb(err);
        });
    }

    /**
     * 从工作空间删除文本内容
     * @param kbId
     * @param kfId
     * @param slug
     * @param cb
     * @param errCb
     */
    deleteWorkspaceRawText(kbId, kfId, slug, cb, errCb) {
        const self = this;
        let fileName = self._getRawTextFileName(kbId, kfId);
        if (fileName == null) {
            log(`没有找到对应文件，删除操作被取消`);
            if (cb != null) cb();
        }
        else {
            let location = `custom-documents/${fileName}`;
            // 从向量库删除
            self.updateEmbeddings(slug, [], [location], (resp) => {
                // 删除文档
                self._deleteFileInDocDirPath(location);
                if (cb != null) cb();
            }, (err) => {
                if (errCb != null) errCb(err);
            });
        }
    }

    /**
     * 从工作空间删除文本内容
     * @param kbId
     * @param kfId
     * @param slug
     * @param location
     * @param cb
     * @param errCb
     */
    deleteWorkspaceRawTextByLocation(kbId, kfId, slug, location, cb, errCb) {
        const self = this;
        // 从向量库删除
        self.updateEmbeddings(slug, [], [location], (resp) => {
            // 删除文档
            self._deleteFileInDocDirPath(location);
            if (cb != null) cb();
            // // 移动文件到待删目录
            // let newLocation = location.replace('custom-documents/', 'deleted-documents/');
            // self.moveFiles([
            //     { from: location, to: newLocation.concat('.').concat(Date.now()) }
            // ], (resp) => {
            //     if (cb != null) cb();
            // }, (err) => {
            //     if (errCb != null) errCb(err);
            // });
        }, (err) => {
            if (errCb != null) errCb(err);
        });
    }


    /* --- 工作空间 --- */

    /**
     * 创建工作空间线程
     * @param slug
     * @param cb {*} cb({ thread: { slug } })
     * @param errCb
     */
    newWorkspaceThread(slug, cb, errCb) {
        reqPostJson(this._buildReqUrl(`/api/v1/workspace/${slug}/thread/new`), {
        }, (resp) => {
            if (cb != null) cb(resp);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
                Authorization: `Bearer ${this.apikey}`
            }
        });
    }

    /**
     * 删除工作空间线程
     * @param slug
     * @param threadSlug
     * @param message
     * @param mode chat | query
     * @param cb {*} cb({ textResponse, sources: [{ title }] })
     * @param errCb
     */
    chatWorkspaceThread(slug, threadSlug, message, mode, cb, errCb) {
        reqPostJson(this._buildReqUrl(`/api/v1/workspace/${slug}/thread/${threadSlug}/chat`), {
            message: message,
            mode: mode,
        }, (resp) => {
            if (cb != null) cb(resp);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
                Authorization: `Bearer ${this.apikey}`
            }
        });
    }

    /**
     * 删除工作空间线程
     * @param slug
     * @param threadSlug
     * @param cb
     * @param errCb
     */
    deleteWorkspaceThread(slug, threadSlug, cb, errCb) {
        reqGet(this._buildReqUrl(`/api/v1/workspace/${slug}/thread/${threadSlug}`), {
        }, (resp) => {
            if (cb != null) cb(resp);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
                Authorization: `Bearer ${this.apikey}`
            },
            method: 'delete'
        });
    }

    /**
     * 创建工作空间
     * @param name
     * @param cb {*} cb({ workspace: { slug }, message })
     * @param errCb {*} ({ status, data: { error } })
     */
    newWorkspace(name, cb, errCb) {
        reqPostJson(this._buildReqUrl(`/api/v1/workspace/new`), {
            name: name,
        }, (resp) => {
            if (cb != null) cb(resp);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
                Authorization: `Bearer ${this.apikey}`
            }
        });
    }

    /**
     * 把文档加入到向量库或从向量库删除
     * @param slug
     * @param adds {Array} 不能为null
     * @param deletes {Array} 不能为null
     * @param cb
     * @param errCb {*} ({ status, data: { error } })
     */
    updateEmbeddings(slug, adds, deletes, cb, errCb) {
        reqPostJson(this._buildReqUrl(`/api/v1/workspace/${slug}/update-embeddings`), {
            adds, deletes
        }, (resp) => {
            if (cb != null) cb(resp);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
                Authorization: `Bearer ${this.apikey}`
            }
        });
    }


    /* --- 文档 --- */

    /**
     * 上传文档
     * @param filePath
     * @param cb {*} cb({ success, documents: [{ location }]})
     * @param errCb {*} ({ status, data: { error } })
     */
    uploadDocumentByPath(filePath, cb, errCb) {
        const fileName = filePath.split('/').pop();
        const fileData = fs.readFileSync(filePath);
        this.uploadDocument(fileName, fileData, cb, errCb);
    }

    /**
     * 上传文档
     * @param fileName
     * @param fileData {Buffer}
     * @param cb
     * @param errCb {*} ({ status, data: { error } })
     */
    uploadDocument(fileName, fileData, cb, errCb) {
        const self = this;
        // 读取文件到内存，返回 Buffer 对象
        // const fileData = fs.readFileSync(filePath);

        const formData = new FormData();
        formData.append('file', fileData, {
            filename: fileName, // 传递文件名
            contentType: 'application/octet-stream', // MIME 类型
        });

        // 使用 axios 上传文件
        axios.post(self._buildReqUrl(`/api/v1/document/upload`), formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
                ...formData.getHeaders(), // 获取并设置正确的请求头
                Authorization: `Bearer ${self.apikey}`,
            },
        }).then((resp) => {
            if (cb != null) cb(resp.data);
        }).catch((err) => {
            if (errCb != null) errCb(err);
        });
    }

    /**
     * 移动文件
     * @param moveItems {[]} [{ from, to }]
     * @param cb
     * @param errCb
     */
    moveFiles(moveItems, cb, errCb) {
        reqPostJson(this._buildReqUrl(`/api/v1/document/move-files`), {
            files: moveItems
        }, (resp) => {
            if (cb != null) cb(resp);
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
                Authorization: `Bearer ${this.apikey}`
            }
        });
    }

    /**
     * 获取真实文件名
     * @param kbId
     * @param kfId
     * @returns {string}
     * @private
     */
    _getRawTextFileName(kbId, kfId) {
        // 文件名开始部分内容
        let fileStartKw = `raw-kf_${kbId}_${kfId}_raw-text`;
        // 文件存放目录
        const docDirPath = this._getDocDirPath().concat('/custom-documents');
        // 根据fileStartKw找到文件并删除
        const list = fs.readdirSync(docDirPath);
        for (const file of list) {
            if (file.startsWith(fileStartKw)) {
                return file;
            }
        }
    }

    /**
     * 从目录中删除文件
     * 1. 如果文件名中包含startKw就删除
     * @param kbId {string} 知识库id
     * @param kfId {string} 知识碎片id
     * @private
     */
    _deleteRawTextFileInDocDirPath(kbId, kfId) {
        // 文件名开始部分内容
        let fileStartKw = `raw-kf_${kbId}_${kfId}_raw-text`;
        // 文件存放目录
        const docDirPath = this._getDocDirPath().concat('/custom-documents');
        // 根据fileStartKw找到文件并删除
        const list = fs.readdirSync(docDirPath);
        for (const file of list) {
            const toDeleteFilePath = `${docDirPath}/${file}`;
            if (file.startsWith(fileStartKw)) {
                log(`删除文件：${toDeleteFilePath}`);
                fs.unlinkSync(toDeleteFilePath);
            }
        }
    }

    /**
     * 删除文档库中的文件
     * @param path 相对于文档库中的路径
     * @private
     */
    _deleteFileInDocDirPath(path) {
        // 文件存放目录
        const docDirPath = this._getDocDirPath();
        // 删除文件
        const toDeleteFilePath = docDirPath.concat('/').concat(path);
        log(`删除文件：${toDeleteFilePath}`);
        // 检查文件是否存在
        if (fs.existsSync(toDeleteFilePath)) {
            fs.unlinkSync(toDeleteFilePath);
        }
    }

    /**
     * 获取文档库在服务器上的路径（用于清理删除的文件）
     * @returns {string}
     * @private
     */
    _getDocDirPath() {
        const { path } = this.ctx.imports;
        return path.join(this.workDirPath, '/documents');
    }

    _buildReqUrl(cmd) {
        return this.url.concat(cmd);
    }
}