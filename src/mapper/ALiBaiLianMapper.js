import { reqPost<PERSON><PERSON> } from "../util/http-util";
import axios from "axios";
import {TopMapper} from "./TopMapper";

/**
 * 阿里百炼映射
 */
export default class ALiBaiLianMapper extends TopMapper{

    constructor(ctx, cfg) {
        super(ctx);
        this.cfg = cfg;

        this._apiKey = "sk-1b204a7fa8f24b93afa295c149198564";
        this._url = "https://dashscope.aliyuncs.com";
    }

    isMyModel(model) {
        if (model == null) model = '';

        if (model.startsWith('albl_')) {
            return true;
        }

        return false;
    }

    /**
     * 修复
     * @param {*} pars
     * @returns
     */
    repair(pars) {
        let model = pars.model;
        let messages = pars.messages;

        if (model.startsWith('albl_')) {
            model = model.substring('albl_'.length);
        }

        return {
            model: model,
            messages: messages,
        };
    }

    /**
     *
     * @param model
     * @param messages
     * @param cb
     * @param errCb
     * @param opts
     */
    chat(model, messages, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        let self = this;

        // 修复参数
        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        // if (opts.max_tokens == null) opts.max_tokens = 8192;

        let optsProps = '';
        for (const i in opts) {
            optsProps += `${i}; `;
        }
        if (logLevel == null || logLevel >= 3) log(`调用 ALiBaiLian.chat ${model} ...（${optsProps}）`);
        let enable_thinking;

        if (model.indexOf('|no_think|') !== -1) {
            model = model.replace('|no_think|', '');
            enable_thinking = false;
            // const userMsg = messages[messages.length - 1];
            // userMsg.content += '\n/no_think';
        }
        // 非流式输出不能使用深度思考
        else if (model === 'qwen3-235b-a22b') {
            enable_thinking = false;
        }

        const params = {
            model,
            messages: messages,
            temperature: opts.temperature,
            tools: opts.tools,
            max_tokens: opts.max_tokens,
            enable_thinking,
        };

        // if (model.indexOf('|no_think|') !== -1) {
        //     params.enable_thinking = false;
        // }
        // // 非流式输出不能使用深度思考
        // else if (model === 'qwen3-235b-a22b') {
        //     params.enable_thinking = false;
        // }

        return new Promise(function (resolve, reject) {
            reqPostJson(`${self._url}/compatible-mode/v1/chat/completions`, params, (r) => {
                if (cb != null) cb(r);
                resolve(r);
            }, (err) => {
                if (errCb != null) errCb(err);
            }, {
                headers: {
                    Authorization: `Bearer ${self._apiKey}`
                }
            });
        });
    }

    /**
     * 聊天（流式）
     * @param model
     * @param messages
     * @param onData
     * @param onEnd
     * @param onError
     * @param opts
     * @returns {Promise<void>}
     */
    async chatStream(model, messages, onData, onEnd, onError, opts = {}) {
        const { logLevel } = super.repairOpts(opts);

        // 修复参数
        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        // if (opts.max_tokens == null) opts.max_tokens = 8192;

        let optsProps = '';
        for (const i in opts) {
            optsProps += `${i}; `;
        }
        if (logLevel == null || logLevel >= 3) log(`调用 ALiBaiLian.chatStream ${model} ...（${optsProps}）`);
        let enable_thinking;

        if (model.indexOf('|no_think|') !== -1) {
            model = model.replace('|no_think|', '');
            enable_thinking = false;
            // const userMsg = messages[messages.length - 1];
            // userMsg.content += '\n/no_think';
        }
        // 非流式输出不能使用深度思考
        else if (model === 'qwen3-235b-a22b') {
            enable_thinking = false;
        }

        const params = {
            model,
            messages: messages,
            temperature: opts.temperature,
            tools: opts.tools,
            max_tokens: opts.max_tokens,
            stream: true, // 启用流式输出
            enable_thinking,
        };

        try {
            const abortController = new AbortController();
            const signal = abortController.signal;

            const response = await axios({
                url: `${this._url}/compatible-mode/v1/chat/completions`,
                method: 'post',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    'Authorization': `Bearer ${this._apiKey}`,
                    "Accept-Encoding": "gzip,deflate,compress",
                },
                data: params,
                responseType: 'stream', // 设置响应类型为流
                signal,
            });
    
            response.data.on('data', (chunk) => {
                if (onData != null) onData(chunk);
            });
    
            response.data.on('end', () => {
                if (onEnd != null) onEnd();
            });
    
            response.data.on('error', (err) => {
                if (onError != null) onError(err);
            });

            return {
                abortController
            }
        }
        catch(exc) {
            console.error(`chatStream异常 -> ${exc.message}`);
            if (onError != null) onError(exc);
        }
    }
}