/**
 * 知识库
 * 存储在本地 data/kb/{kbId}.json 文件中
 */
export default class KbMapper {

  constructor(ctx) {
    this.ctx = ctx;
  }

  /**
   * 存储到本地文件中
   * @param kbId
   * @param form
   */
  save(kbId, form) {
    const fs = this.ctx.imports.fs;
    const path = this.ctx.imports.path;

    const filePath = `${this.ctx.rootPath}/data/kb/${kbId}.json`;
    if (!fs.existsSync(filePath)) {
      const dirPath = path.dirname(filePath);
      fs.mkdirSync(dirPath, { recursive: true }); // 递归创建目录
    }
    fs.writeFileSync(filePath, JSON.stringify(form));
  }

  /**
   * 加载本地文件
   * @param kbId
   * @return {{}|any}
   */
  load(kbId) {
    const fs = this.ctx.imports.fs;
    const filePath = `${this.ctx.rootPath}/data/kb/${kbId}.json`;
    if (!fs.existsSync(filePath)) {
      return {};
    }
    return JSON.parse(fs.readFileSync(filePath));
  }

}