import {reqPost<PERSON>son} from "../util/http-util";
import axios from "axios";
import {TopMapper} from "./TopMapper";

/**
 * OpenAI映射
 * 常见报错：
 * - Request failed with status code 429 （账户没钱了）
 */
export default class OpenAIBrainMapper extends TopMapper{

    constructor(ctx, cfg) {
        super(ctx);
        this.cfg = cfg;

        this._url = 'http://43.157.44.52:8088/api/openai';
    }

    isMyModel(model) {
        if (model == null) model = '';

        if (model.startsWith('br_gpt')) return true;
        if (model.startsWith('br_o1')) return true;
        if (model.startsWith('br_o3')) return true;
        if (model.startsWith('br_o4')) return true;
        if (model.startsWith('br_text-embedding-3-small')) return true;

        return false;
    }

    /**
     * 修复
     * @param {*} pars 
     * @returns 
     */
    repair(pars) {
        let model = pars.model;
        let messages = pars.messages;

        if (model.startsWith('br_')) {
            model = model.substring('br_'.length);
        }

        if (model.indexOf('o1-') !== -1 || model.indexOf('o3-') !== -1) {
            for (const message of messages) {
                if (message.role === 'system') {
                    message.role = 'user';
                }
            }
        }

        return {
            model: model,
            messages: messages,
        };
    }

    /**
     *
     * @param model
     * @param messages
     * @param cb
     * @param errCb
     * @param opts
     */
    chat1(model, messages, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);

        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        let optsProps = '';
        for (const i in opts) {
            optsProps += `${i}; `;
        }
        if (logLevel == null || logLevel >= 3) log(`调用 OpenAIBrain.chat1 ${model} ...（${optsProps}）`);

        reqPostJson(`${this._url}/chat`, {
            ink: 'abc131', sac: 'cz.cn', un: 'sys_acc_0', pwd: 'd9d0b4b4',
            msgs: messages,
            model: model,
            opts: opts,
        }, (r) => {
            if (r.success) {
                if (cb != null) cb(r.data);
            }
            else {
                if (errCb != null) errCb({ message: r.msg });
            }
        }, (err) => {
            if (errCb != null) errCb(err);
        }, {
            headers: {
            }
        });
    }

    /**
     *
     * @param {*} model
     * @param {*} messages
     * @param {*} opts
     * @param cb
     * @param errCb
     * @returns
     */
    chat1Sync(model, messages, opts, cb, errCb) {
        const self = this;
        return new Promise((resolve, reject) => {
            self.chat1(model, messages, (r) => {
                if (cb) cb(r);
                resolve(r);
            }, (err) => {
                if (errCb) errCb(err);
            }, opts);
        });
    }

    /**
     * 聊天
     * @param model
     * @param messages
     * @param cb
     * @param errCb
     */
    chat(model, messages, cb, errCb, opts = {}) {
        const self = this;
        return new Promise((resolve, reject) => {
            self.chat1(model, messages, (r) => {
                if (cb) cb(r);
                resolve(r);
            }, (err) => {
                if (errCb) errCb(err);
            }, opts);
        });
    }

    /**
     *
     * @param model
     * @param messages
     * @returns {Promise<unknown>}
     */
    chatSync(model, messages) {
        const self = this;
        return self.chat(model, messages, null, null);
    }

    /**
     *
     * @param model
     * @param messages
     * @param onData
     * @param onEnd
     * @param onError
     * @param opts
     * @return {Promise<void>}
     */
    async chatStream(model, messages, onData, onEnd, onError, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        try {
            const repairResult = this.repair({
                model: model,
                messages: messages,
            });
            model = repairResult.model;
            messages = repairResult.messages;

            let optsProps = '';
            for (const i in opts) {
                optsProps += `${i}; `;
            }
            if (logLevel == null || logLevel >= 3) log(`调用 OpenAIBrain.chatStream ${model} ...（${optsProps}）`);

            const params = {
                ink: 'abc131', sac: 'cz.cn', un: 'sys_acc_0', pwd: 'd9d0b4b4',
                model: model,
                msgs: messages,
                opts: opts,
            };

            const abortController = new AbortController();
            const signal = abortController.signal;

            const response = await axios({
                url: `${this._url}/chat-stream`,
                method: 'post',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    "Accept-Encoding": "gzip,deflate,compress",
                },
                data: params,
                responseType: 'stream', // 设置响应类型为流
                signal,
            });
    
            response.data.on('data', (chunk) => {
                if (onData != null) onData(chunk);
            });
    
            response.data.on('end', () => {
                if (onEnd != null) onEnd();
            });
    
            response.data.on('error', (err) => {
                if (onError != null) onError(err);
            });

            return {
                abortController
            }
        } 
        catch(exc) {
            console.error(`chatStream异常 -> ${exc.message}`);
            if (onError != null) onError(new Error(exc.message));
        }
    }

    async embed(model, text, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);

        if (logLevel == null || logLevel >= 3) log(`调用 OpenAIBrain.embed ...（${model}）`);
        const repairResult = this.repair({
            model: model,
        });
        model = repairResult.model;

        return new Promise((resolve, reject) => {
            reqPostJson(`${this._url}/req`, {
                ink: 'abc131', sac: 'cz.cn', un: 'sys_acc_0', pwd: 'd9d0b4b4',
                cmd: '/v1/embeddings', params: {
                    model: model, input: text,
                }
            }, (r) => {
                if (r.success === false) {
                    if (cb != null) cb(r);
                    resolve(r)
                }
                else {
                    if (cb != null) cb(r.data);
                    resolve(r.data)
                }
            }, (err) => {
                if (errCb != null) errCb(err);
            }, {
                headers: {}
            });
        });
    }
}