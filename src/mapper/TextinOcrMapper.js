import {downloadFile} from "../util/http-util";

export class TextinOcrMapper {

    constructor(ctx, data = {}) {
        this.ctx = ctx;
        this.data = data;

        this.data.appId = '680200501e0616de15fbee0db6160f9e';
        this.data.appSecret = 'b40ba9526c06a7e78de7ddb05c6d12de';
        this.data.baseUrl = 'https://api.textin.com';
    }

    async convertDocToMd(docFilePath, opts = {}) {
        const self = this;
        const options = {
            page_start: 0,
            page_count: 1000,
            table_flavor: 'md',
            parse_mode: 'auto',
            get_image: 'objects',
            image_output_type: 'default',
            page_details: 0,
        };

        let inputFilePath = docFilePath;

        // 根据inputFilePath生成outputFilePath，把原文件后缀改成.md就行
        let extName = inputFilePath.split('.').pop();

        log(`[TextinOcrMapper] convertDocToMd ...`)
        const response1 = await this.recognizePdf2Md(inputFilePath, options);
        if (response1.data.code === 200) {
            let markdown = response1.data.result.markdown;
            // 把附件下载到本地目录
            if (opts.attachment) {
                markdown = await self.downloadAttachmentToLocalAndReplace(markdown,
                    opts.attachment.saveDirPath, opts.attachment.replacementParentUrl, opts);
            }

            return markdown;
        }
        else {
            console.error(`请求失败 -> ${response1.data.code}`)
            throw new Error(`请求失败 -> ${response1.data.code}`)
        }
    }

    async recognizePdf2Md(image_path, options, is_url = false) {
        const { fs, axios } = this.ctx.imports;

        const url = `${this.data.baseUrl}/ai/service/v1/pdf_to_markdown`;

        const headers = {
            'x-ti-app-id': this.data.appId,
            'x-ti-secret-code': this.data.appSecret,
            'Content-Type': is_url ? 'text/plain' : 'application/octet-stream',
        }

        const image = is_url ? image_path : fs.readFileSync(image_path);

        const config = {
            headers: headers,
            params: options,
        };

        return axios.post(url, image, config);
    }

    /**
     * 下载附件到本地并替换内容
     * @param content
     * @param saveDirPath
     * @param opts
     * @returns {Promise<*>}
     */
    async downloadAttachmentToLocalAndReplace(content, saveDirPath, replacementParentUrl, opts = {}) {
        const { fs } = this.ctx.imports;

        log(`[TextinOcrMapper] downloadAttachmentToLocalAndReplace ...`)
        let newContent = content
        let saveFileDirPath = saveDirPath;

        if (!fs.existsSync(saveFileDirPath)) {
            fs.mkdirSync(saveFileDirPath, {recursive: true});
        }

        // 替换markdown格式附件
        {
            const reg = new RegExp(/!\[(.*?)\]\(([^()]+)\)/g);
            const matches = content.matchAll(reg)
            for (const match of matches) {
                const url = match[2]
                const fileName = url.substring(url.lastIndexOf('/') + 1);

                log(`[TextinOcrMapper] Download attachment from Textin: ${url}`);
                const filePath = await downloadFile(url, saveFileDirPath)
                newContent = newContent.replace(url, `${replacementParentUrl}/${fileName}`)
            }
        }
        // 替换html中图片
        {
            const reg = new RegExp(/src[=]["']*([^"'\s]+)["']*/g);
            const matches = content.matchAll(reg)
            for (const match of matches) {
                const url = match[1]
                const fileName = url.substring(url.lastIndexOf('/') + 1);

                log(`[TextinOcrMapper] Download image from Textin: ${url}`);
                const filePath = await downloadFile(url, saveFileDirPath)
                newContent = newContent.replace(url, `${replacementParentUrl}/${fileName}`)
            }
        }

        return newContent
    }
}