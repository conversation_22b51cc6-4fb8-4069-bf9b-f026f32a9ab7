import {reqPost<PERSON><PERSON>} from "../util/http-util";
import axios from "axios";
import {TopMapper} from "./TopMapper";

/**
 * Anthropic映射
 */
export default class AnthropicBrainWildMapper extends TopMapper{

    constructor(ctx, cfg) {
        super(ctx);
        this.cfg = cfg;

        this._url = 'http://************:8088/api/wild/anthropic';
        // this._url = 'http://localhost:8088/api/wild/anthropic';
    }

    isMyModel(model) {
        if (model == null) model = '';

        if (model.startsWith('brwild_claude')) return true;

        return false;
    }

    /**
     * 修复
     * @param {*} pars 
     * @returns 
     */
    repair(pars) {
        let model = pars.model;
        let messages = pars.messages;

        if (model.startsWith('brwild_')) {
            model = model.substring('brwild_'.length);
        }

        return {
            model: model,
            messages: messages,
        };
    }

    /**
     * 聊天
     * @param model
     * @param messages
     * @param cb
     * @param errCb
     * @param opts
     */
    chat(model, messages, cb, errCb, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        const self = this;

        // let max_tokens = 4096;

        const repairResult = this.repair({
            model: model,
            messages: messages,
        });
        model = repairResult.model;
        messages = repairResult.messages;

        let systemPrompt = null;
        if (messages[0].role === 'system') {
            systemPrompt = messages[0].content;
            messages.splice(0, 1);
        }

        let optsProps = '';
        for (const i in opts) {
            optsProps += `${i}; `;
        }
        if (logLevel == null || logLevel >= 3) log(`调用 AnthropicBrainWild.chat ${model} ...（${optsProps}）`);

        const params = {
            model: model,
            messages: messages,
            system: systemPrompt,
            temperature: opts.temperature,
            // max_tokens: max_tokens,
            // thinking: {
            //     type: "disabled"
            // },
        };

        return new Promise(function (resolve, reject) {
            try {
                reqPostJson(self._url, {
                    ink: 'abc131', sac: 'cz.cn', un: 'sys_acc_0', pwd: 'd9d0b4b4',
                    cmd: '/v1/messages',
                    params: params
                }, (r) => {
                    if (r.success) {
                        if (cb != null) cb(r.data);
                        resolve(r.data);
                    }
                    else {
                        if (errCb != null) errCb({ message: r.msg });
                        if (errCb) errCb(r.msg);
                    }
                }, (err) => {
                    if (errCb) errCb(err);
                }, {
                    headers: {
                    }
                });
            } catch (exc) {
                if (errCb) errCb(exc);
            }
        });
    }

    /**
     *
     * @param model
     * @param messages
     * @param onData
     * @param onEnd
     * @param onError
     * @param opts
     * @return {Promise<void>}
     */
    async chatStream(model, messages, onData, onEnd, onError, opts = {}) {
        const { logLevel } = super.repairOpts(opts);
        const self = this;

        try {
            const repairResult = this.repair({
                model: model,
                messages: messages,
            });
            model = repairResult.model;
            messages = repairResult.messages;

            let systemPrompt = null;
            if (messages[0].role === 'system') {
                systemPrompt = messages[0].content;
                messages.splice(0, 1);
            }

            let optsProps = '';
            for (const i in opts) {
                optsProps += `${i}; `;
            }
            if (logLevel == null || logLevel >= 3) log(`调用 AnthropicBrainWild.chatStream ${model} ...（${optsProps}）`);

            const params = {
                ink: 'abc131', sac: 'cz.cn', un: 'sys_acc_0', pwd: 'd9d0b4b4',
                cmd: '/v1/messages',
                params: {
                    model: model,
                    messages: messages,
                    system: systemPrompt,
                    temperature: opts.temperature,
                    // thinking: {
                    //     type: "disabled"
                    // },
                }
            };

            const response = await axios({
                url: `${self._url}-stream`,
                method: 'post',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    "Accept-Encoding": "gzip,deflate,compress",
                },
                data: params,
                responseType: 'stream' // 设置响应类型为流
            });
    
            response.data.on('data', (chunk) => {
                if (onData != null) onData(chunk);
            });
    
            response.data.on('end', () => {
                if (onEnd != null) onEnd();
            });
    
            response.data.on('error', (err) => {
                if (onError != null) onError(err);
            });
        }
        catch(exc) {
            console.error(`chatStream异常 -> ${exc.message}`);
            if (onError != null) onError(new Error(exc.message));
        }
    }
}