/**
 * 获取部分字符串
 * @param str 原始字符串
 * @param max 字符串最大长度，如果字符串长度超过这个值就截断，并且加上“...”表示被截断了
 */
export function getPartialStr(str, max) {
  if (str != null && str.length != null && str.length > max) {
    return str.substring(0, max - 1) + "...";
  }
  return str;
}

/**
 * 把字符串根据指定的最大长度进行拆分成字符串数组
 * @param {*} str 
 * @param {*} maxLength 
 * @returns {string[]}
 */
export function splitUpStr(str, maxLength) {
  if (!str || str.length <= maxLength) return [str];
  const result = [];
  for (let i = 0; i < str.length; i += maxLength) {
    result.push(str.substring(i, i + maxLength));
  }
  return result;
}