// yyyyMMddHHmmssSSS
export function newId() {
    const now = new Date();
    const zero = '0';
    let id = '';
    /*1*/
    id = id.concat(now.getFullYear());
    // id += Math.random().toString().replace('0.', '').substr(0, 4);
    /*2*/
    if ((now.getMonth() + 1) < 10) {
        id = id.concat(zero).concat(now.getMonth() + 1);
    }
    else {
        id = id.concat(now.getMonth() + 1);
    }
    if (now.getDate() < 10) {
        id = id.concat(zero).concat(now.getDate());
    }
    else {
        id = id.concat(now.getDate());
    }
    // id += Math.random().toString().replace('0.', '').substr(0, 4);
    /*3*/
    if (now.getHours() < 10) {
        id = id.concat(zero).concat(now.getHours());
    }
    else {
        id = id.concat(now.getHours());
    }
    if (now.getMinutes() < 10) {
        id = id.concat(zero).concat(now.getMinutes());
    }
    else {
        id = id.concat(now.getMinutes());
    }
    // id += Math.random().toString().replace('0.', '').substr(0, 4);
    /*4*/
    if (now.getSeconds() < 10) {
        id = id.concat(zero).concat(now.getSeconds());
    }
    else {
        id = id.concat(now.getSeconds());
    }
    if (now.getMilliseconds() < 10) {
        id = id.concat(zero).concat(now.getMilliseconds());
    }
    else {
        var mStr = now.getMilliseconds().toString();
        id = id.concat(mStr.substr(mStr.length - 3, 3));
    }
    // id += Math.random().toString().replace('0.', '').substr(0, 4);
    return id;
}

export function newGuid() {
    function s4() { return Math.floor((1 + Math.random()) * 0x10000).toString(16).substring(1); }
    return s4().concat(s4()).concat('-').concat(s4()).concat('-').concat(s4()).concat('-').concat(s4()).concat('-')
        .concat(s4()).concat(s4()).concat(s4());
}

// yyyyMMddHHmmssSSS-fhke21
export function newCUIdA() {
    var str0 = newId();
    var str1 = newGuid().substr(0, 6)
    return str0.concat("-").concat(str1);
}