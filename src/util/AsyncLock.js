/**
 * 异步锁
 */
export class AsyncLock {

  constructor() {
    this.debug = false;
    this.locked = false; // 锁的状态
    this.queue = []; // 等待队列
  }

  lock() {
    const self = this;
    return new Promise((resolve, reject) => {
      if (!self.locked) {
        self.locked = true;
        resolve();
      }
      else {
        self.queue.push({
          resolve, time: Date.now()
        }); // 加入等待队列
      }

      if (self.debug) console.log(`lock 队列数：${self.queue.length}`);
    });
  }

  unlock() {
    const self = this;
    if (self.queue.length > 0) {
      // 取出等待队列的第一个，并执行
      const first = self.queue.shift(); // 移除第一个
      if (first) {
        first.resolve(); // 执行第一个
      }
    }
    if (self.queue.length === 0) {
      self.locked = false;
    }

    if (self.debug) console.log(`unlock 队列数：${self.queue.length}`);
  }
}