import {AsyncLock} from "./AsyncLock";

/**
 * 任务处理队列
 */
export default class TaskProcQueue {
    constructor(cfg) {
        this._debug = false;
        // 可配置变量
        this._procInterval = 800;
        // 系统变量
        this._canProcTask = false;
        this._queue = [];

        if (cfg == null) cfg = {
            // 处理间隔（毫秒）
            procInterval: null,
            // 每次处理完毕后触发（允许外部借用队列的心跳服务）
            afterProc: null,
        };
        this._cfg = cfg;

        if (cfg.debug) {
            this._debug = cfg.debug;
        }
        if (cfg.procInterval) {
            this._procInterval = cfg.procInterval;
        }

        this._lock0 = new AsyncLock();
        this._proc();
    }

    /**
     * 加入任务
     * @param task name, pars, proc(pars, cb)
     */
    async push(task) {
        if (this._debug) console.log(`加入任务：${task.name}`);
        await this._lock0.lock();

        this._queue.push(task);

        this._lock0.unlock();
    }

    /**
     * 启动队列
     */
    start() {
        if (this._debug) console.log(`启动任务处理队列`);
        this._canProcTask = true;
    }

    /**
     * 停止队列
     */
    stop() {
        if (this._debug) console.log(`停止任务处理队列`);
        this._canProcTask = false;
    }

    _procTask(task) {
        const self = this;
        return new Promise(function (resolve, reject) {
            try {
                if (self._debug) console.log(`执行任务：${task.name}`);
                task.proc(task.pars, (r) => {
                    resolve({ success: true, data: r });
                });
            } catch (exc) {
                resolve({ success: false, data: exc });
            }
        });
    }

    async _proc() {
        const self = this;

        if (self._canProcTask && this._queue.length > 0) {
            await self._lock0.lock();
            while (true) {
                const task = self._queue[0];
                await self._procTask(task);
                self._queue.splice(0, 1);
                if (self._queue.length <= 0) break;
            }
            self._lock0.unlock();
        }

        if (self._cfg.afterProc != null) {
            try {
                self._cfg.afterProc();
            } catch (exc) {
                console.error(`TaskProcQueue.afterProc error -> ${exc.message}`);
            }
        }

        setTimeout(() => {
            self._proc();
        }, self._procInterval);
    }
}