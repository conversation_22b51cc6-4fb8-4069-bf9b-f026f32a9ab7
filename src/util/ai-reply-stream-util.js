import AnthropicBrainWildMapper from "../mapper/AnthropicBrainWildMapper";

/**
 * AI回复流输出块帮助类（OpenAI）
 */
export class AIReplyStreamChunkHelper1 {
    constructor(cfg) {
        this._cfg = cfg;
        this._totalReasoning = '';
        this._totalChatReply = '';
        // 不完整暂存（如果一个chunk不完整时，就暂存下等你下个完整后再输出）
        this.bwzzc = '';
    }

    build(r, opts) {
        if (opts == null) opts = {};
        if (opts.fieldName == null) opts.fieldName = 'content';
        if (opts.fieldName_reasoning == null) opts.fieldName_reasoning = 'reasoning_content';
        // 返回完整回复
        if (opts.returnTotalReply == null) opts.returnTotalReply = true;

        let debug = opts.debug;
        // 推理块
        let reasoning = '';
        // 回复块
        let chatReply = '';
        let raw = r.trim();

        if (debug) console.log('------------------------------------------------------------------')
        if (debug) console.log('\n接收数据：\n' + raw + '\n');

        if (raw === ': keep-alive') return;
        if (raw.indexOf('"success":false') != -1 && raw.startsWith("{") && raw.endsWith("}")) {
            const obj = JSON.parse(raw);
            return `请求异常 -> ${obj.msg}`;
        }

        // 接上之前的不完整块
        if (this.bwzzc != null && this.bwzzc.length > 0) {
            if (debug) console.log(`\n【不完整暂存】：\n${this.bwzzc}\n`);
            raw = this.bwzzc + raw;
            this.bwzzc = '';
        }

        const splitList = raw.split('\n');
        for (const i in splitList) {
            const n0 = splitList[i];

            // 去除“data: ”前缀
            const n = n0.substring(6);
            if (n != null && n.trim().length > 0) {
                if (n === '[DONE]') {
                }
                else {
                    let obj;
                    // 尝试解析，检测块是否完整
                    try {
                        obj = JSON.parse(n);
                    }
                    catch (exc) {
                        if (debug) console.log(`\n｛可能不是完整的块｝：${n}\n`);
                        // 暂存不完整的块
                        let curIndex = parseInt(i);
                        for (let j = curIndex; j < splitList.length; j++) {
                            if (splitList[j].trim().length > 0) {
                                if (this.bwzzc.length > 0) this.bwzzc += '\n\n';
                                this.bwzzc += `${splitList[j]}`;
                            }
                        }
                        break;
                    }

                    if (obj) {
                        if (obj.choices.length > 0 && obj.choices[0].delta) {
                            if (obj.choices[0].delta[opts.fieldName_reasoning] != null && obj.choices[0].delta[opts.fieldName_reasoning].length > 0) {
                                reasoning += obj.choices[0].delta[opts.fieldName_reasoning] || '';
                            }
                            if (obj.choices[0].delta[opts.fieldName] != null && obj.choices[0].delta[opts.fieldName].length > 0) {
                                chatReply += obj.choices[0].delta[opts.fieldName] || '';
                            }
                        }
                    }
                }
            }
        }

        if (debug) console.log(`\n返回文本：\n${chatReply}\n`);

        if (opts.returnTotalReply) {
            this._totalReasoning += reasoning;
            this._totalChatReply += chatReply;
        }

        if (this._cfg.onReplyChange) {
            if (opts.returnTotalReply) {
                if (this._cfg.onReasoningChange) {
                    this._cfg.onReasoningChange(this._totalReasoning);
                }
                this._cfg.onReplyChange(this._totalChatReply);
            }
            else {
                if (this._cfg.onReasoningChange) {
                    this._cfg.onReasoningChange(reasoning);
                }
                this._cfg.onReplyChange(chatReply);
            }
        }
    }
}

export class AnthropicAIReplyStreamChunkHelper {
    constructor(cfg) {
        this._cfg = cfg;
        this._totalReasoning = '';
        this._totalChatReply = '';
        // 不完整暂存（如果一个chunk不完整时，就暂存下等你下个完整后再输出）
        this.bwzzc = '';
    }

    build(r, opts) {
        if (opts == null) opts = {};
        // 返回完整回复
        if (opts.returnTotalReply == null) opts.returnTotalReply = true;

        let debug = opts.debug;
        // 推理块
        let reasoning = '';
        // 回复块
        let chatReply = '';
        let raw = r.trim();

        if (debug) console.log('------------------------------------------------------------------')
        if (debug) console.log('\n接收数据：\n' + raw + '\n');

        if (raw === ': keep-alive') return;
        if (raw.indexOf('"success":false') != -1 && raw.startsWith("{") && raw.endsWith("}")) {
            const obj = JSON.parse(raw);
            return `请求异常 -> ${obj.msg}`;
        }

        // 接上之前的不完整块
        if (this.bwzzc != null && this.bwzzc.length > 0) {
            if (debug) console.log(`\n【不完整暂存】：\n${this.bwzzc}\n`);
            raw = this.bwzzc + raw;
            this.bwzzc = '';
        }

        const splitList = raw.split('\n');
        for (const i in splitList) {
            const n0 = splitList[i];

            if (n0.startsWith('event:')) {
            }
            else {
                // 去除“data: ”前缀
                const n = n0.substring(6);
                if (n != null && n.trim().length > 0) {
                    if (n === '[DONE]') {
                    }
                    else {
                        let obj;
                        // 尝试解析，检测块是否完整
                        try {
                            obj = JSON.parse(n);
                        }
                        catch (exc) {
                            if (debug) console.log(`\n｛可能不是完整的块｝：${n}\n`);
                            // 暂存不完整的块
                            let curIndex = parseInt(i);
                            for (let j = curIndex; j < splitList.length; j++) {
                                if (splitList[j].trim().length > 0) {
                                    if (this.bwzzc.length > 0) this.bwzzc += '\n\n';
                                    this.bwzzc += `${splitList[j]}`;
                                }
                            }
                            break;
                        }

                        if (obj) {
                            if (obj.delta && obj.delta.type === 'text_delta') {
                                if (obj.delta.text != null && obj.delta.text.length > 0) {
                                    chatReply += obj.delta.text || '';
                                }
                            }
                        }
                    }
                }
            }
        }

        if (debug) console.log(`\n返回文本：\n${chatReply}\n`);

        if (opts.returnTotalReply) {
            this._totalReasoning += reasoning;
            this._totalChatReply += chatReply;
        }

        if (this._cfg.onReplyChange) {
            if (opts.returnTotalReply) {
                if (this._cfg.onReasoningChange) {
                    this._cfg.onReasoningChange(this._totalReasoning);
                }
                this._cfg.onReplyChange(this._totalChatReply);
            }
            else {
                if (this._cfg.onReasoningChange) {
                    this._cfg.onReasoningChange(reasoning);
                }
                this._cfg.onReplyChange(chatReply);
            }
        }
    }
}

/**
 * AI回复流输出块帮助类（MoonshotAI）
 */
export class MoonshotAIReplyStreamChunkHelper {
    constructor(cfg) {
        this._cfg = cfg;
        this._totalChatReply = '';
    }

    build(r, opts) {
        if (opts == null) opts = {};
        // 返回完整回复
        if (opts.returnTotalReply == null) opts.returnTotalReply = true;

        let debug = opts.debug;
        let raw = r.trim();

        if (debug) console.log('------------------------------------------------------------------')
        if (debug) console.log('接收数据：\n' + raw + '\n');

        if (raw.indexOf('"success":false') != -1 && raw.startsWith("{") && raw.endsWith("}")) {
            const obj = JSON.parse(raw);
            return `请求异常 -> ${obj.msg}`;
        }

        const list = this._parseStream(r);

        let chatReply = '';
        for (const i in list) {
            chatReply += list[i].content || '';
        }

        if (debug) console.log(`\n返回文本：\n${chatReply}\n`);

        if (opts.returnTotalReply) {
            this._totalChatReply += chatReply;
        }

        if (this._cfg.onReplyChange) {
            if (opts.returnTotalReply) {
                this._cfg.onReplyChange(this._totalChatReply);
            }
            else {
                this._cfg.onReplyChange(chatReply);
            }
        }
    }

    _parseStream(rawData) {
        let result = [];
        let temp = '';
        let depth = 0;

        // 遍历每个字符
        for (let i = 0; i < rawData.length; i++) {
            const char = rawData[i];

            // 检测到左大括号，深度加1
            if (char === '{') {
                depth++;
                temp += char;
            } else if (char === '}') {
                // 检测到右大括号，深度减1
                depth--;
                temp += char;

                // 当深度为0时，表示一个完整的 JSON 对象
                if (depth === 0) {
                    try {
                        // 尝试解析这个临时字符串为 JSON 对象
                        const parsed = JSON.parse(temp);
                        result.push(parsed);
                        temp = ''; // 清空临时字符串
                    } catch (e) {
                        console.error('解析错误:', e);
                    }
                }
            } else {
                // 其他字符直接追加到临时字符串
                temp += char;
            }
        }

        return result;
    }
}

/**
 * AI回复流输出块帮助类（Ollama）
 */
export class OllamaAIReplyStreamChunkHelper {
    constructor(cfg) {
        this._cfg = cfg;
        this._totalChatReply = '';
    }

    build(r, opts) {
        if (opts == null) opts = {};
        // 返回完整回复
        if (opts.returnTotalReply == null) opts.returnTotalReply = true;

        let debug = opts.debug;
        let raw = r.trim();

        if (debug) console.log('------------------------------------------------------------------')
        if (debug) console.log('接收数据：\n' + raw + '\n');

        if (raw.indexOf('"success":false') != -1 && raw.startsWith("{") && raw.endsWith("}")) {
            const obj = JSON.parse(raw);
            return `请求异常 -> ${obj.msg}`;
        }

        const list = this._parseStream(r);

        let chatReply = '';
        for (const i in list) {
            chatReply += list[i].message.content || '';
        }

        if (debug) console.log(`\n回复块：\n${chatReply}\n`);

        if (opts.returnTotalReply) {
            this._totalChatReply += chatReply;
            if (this._totalChatReply.indexOf('<think>') !== -1) {
                if (this._totalChatReply.indexOf('</think>') !== -1) {
                    const arr = this._totalChatReply.split('</think>');
                    if (this._cfg.onReasoningChange) {
                        if (arr[0].indexOf('<think>\n') !== -1) {
                            this._cfg.onReasoningChange(arr[0].substring('<think>\n'.length).trim());
                        }
                        else {
                            this._cfg.onReasoningChange(arr[0].substring('<think>'.length).trim());
                        }
                    }
                    if (this._cfg.onReplyChange) {
                        this._cfg.onReplyChange(arr[1]);
                    }
                }
                else {
                    if (this._cfg.onReasoningChange) {
                        if (this._totalChatReply.indexOf('<think>\n') !== -1) {
                            this._cfg.onReasoningChange(this._totalChatReply.substring('<think>\n'.length).trim());
                        }
                        else {
                            this._cfg.onReasoningChange(this._totalChatReply.substring('<think>'.length).trim());
                        }
                    }
                    if (this._cfg.onReplyChange) {
                        this._cfg.onReplyChange('');
                    }
                }
            }
            else {
                if (this._cfg.onReasoningChange) {
                    this._cfg.onReasoningChange('');
                }
                if (this._cfg.onReplyChange) {
                    this._cfg.onReplyChange(this._totalChatReply);
                }
            }
        }
        else {
            if (this._cfg.onReasoningChange) {
                this._cfg.onReasoningChange('');
            }
            if (this._cfg.onReplyChange) {
                this._cfg.onReplyChange(chatReply);
            }
        }
    }

    _parseStream(rawData) {
        let result = [];

        const list = rawData.split('\n');
        for (const item of list) {
            if (item && item.trim().length > 0) {
                result.push(JSON.parse(item));
            }
        }

        return result;
    }
}

/**
 * 【废弃】AI回复流输出块帮助类（OpenAI）
 */
export class AIReplyStreamChunkHelper {
    constructor() {
        // 不完整暂存（如果一个chunk不完整时，就暂存下等你下个完整后再输出）
        this.bwzzc = '';
    }

    build(r, opts) {
        if (opts == null) opts = {};

        let debug = opts.debug;
        let chatReply = '';
        let canBuildReply = true;
        let raw = r.trim();

        if (debug) console.log('------------------------------------------------------------------')
        if (debug) console.log('接收数据：\n' + raw + '\n');

        // 这是最后一个不完整chunk
        if (this.bwzzc.length > 0 && (raw.endsWith('data: [DONE]') || raw.endsWith('"usage":null}') || raw.endsWith('{"reasoning_tokens":0}}}'))) {
            if (debug) console.log('********************************************************************')
            if (debug) console.log(`处理不完整暂存：\n${raw}`);
            raw = this.bwzzc + raw;
            this.bwzzc = '';
        }
        // 这是一个不完整chunk
        else if (!raw.endsWith('"usage":null}')
            && !raw.endsWith('{"reasoning_tokens":0}}}') && !raw.endsWith(('data: [DONE]'))) {
            if (debug) console.log('********************************************************************')
            if (debug) console.log(`是不完整chunk：\n${raw}`);
            this.bwzzc += raw;
            canBuildReply = false;
        }
        // 这是一个不完整chunk
        else if (!raw.startsWith('data: ')) {
            if (debug) console.log('********************************************************************')
            if (debug) console.log(`是不完整chunk：\n${raw}`);
            this.bwzzc += raw;
            canBuildReply = false;
        }
        else {
            if (debug) console.log('********************************************************************')
            if (debug) console.log(`是完整chunk`);
        }

        if (canBuildReply) {
            const splitList = raw.split('\n');
            for (const i in splitList) {
                const n0 = splitList[i];

                // 去除“data: ”前缀
                const n = n0.substring(6);
                if (n != null && n.trim().length > 0) {
                    // if (debug) console.log('------------------------------------------------------------------')
                    // if (debug) console.log(n);

                    if (n === '[DONE]') {
                    }
                    else {
                        try {
                            const obj = JSON.parse(n);
                            // if (debug) console.log(obj);
                            if (obj.choices.length > 0) {
                                if (obj.choices[0].delta.content != null && obj.choices[0].delta.content.length > 0) {
                                    chatReply += obj.choices[0].delta.content || '';
                                    // if (debug) console.log(`生成的回复：${chatReply}`);
                                }
                            }
                        } catch (exc) {
                            if (debug) console.error(`解析异常 -> ${exc.message}`);
                            if (debug) console.log(`异常chunk：${raw}`);
                            return;
                        }
                    }
                }
            }
        }

        return chatReply;
    }
}