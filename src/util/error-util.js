/**
 * 解析错误消息
 * @param {*} exc 
 * @returns 
 */
export function parseErrorMsg(exc) {
    let str = ``;

    if (exc == null) {
        str = `err is null`;
    }
    else if (exc.error) {
        if (typeof(exc) === 'object') {
            if (exc.error.message) {
                str = `${exc.error.message}`;
            }
            else {
                str = `${JSON.stringify(exc.error)}`;
            }
        }
        else {
            str = `${exc.error}`;
        }
    }
    else if (exc.response) {
        if (exc.response.data) {
            if (exc.response.data.message) {
                str = `${exc.message} -> ${exc.response.data.message}`;
            }
            else if (exc.response.data.error) {
                if (exc.response.data.error.message) {
                    str = `${exc.message} -> ${exc.response.data.error.message}`;
                }
                else {
                    str = `${exc.message} -> ${exc.response.data.error}`;
                }
            }
            else {
                str = `${exc.message} -> ${JSON.stringify(exc.response.data)}`;
            }
        }
        else if (exc.response.error) {
            if (exc.response.error.message) {
                str = `${exc.message} -> ${exc.response.error.message}`;
            }
            else {
                str = `${exc.response.error}`;
            }
        }
        else {
            str = `${JSON.stringify(exc.response)}`;
        }
    }
    else if (exc.message) {
        str = `${exc.message}`;
    }
    else {
        str = `${exc}`;
    }

    return str;
}