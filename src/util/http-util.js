// const axios = require("axios");
import axios from "axios";

/**
 * 用GET请求
 * @param url
 * @param pars
 * @param callback
 * @param errorCallback
 * @param opts
 */
export function reqGet(url, pars, callback, errorCallback, opts = {}) {
    if (pars == null) pars = {};
    if (opts.headers == null) opts.headers = {};
    if (opts.method == null) opts.method = "get";

    let idx = 0;
    for (const prop in pars) {
        if (idx === 0) {
            url = url.concat(`?${prop}=${pars[prop]}`);
        }
        else {
            url = url.concat(`&${prop}=${pars[prop]}`);
        }
        idx++;
    }

    axios[opts.method.toLowerCase()](url, {
        headers: opts.headers
    }).then(function (response) {
        const r = response.data;
        if (callback != null) {
            callback(r);
        }
    }).catch(function (error) {
        if (errorCallback != null) {
            errorCallback(error);
        }
    });
}

/**
 * 用GET请求（同步）
 * @param url
 * @param pars
 * @param opts
 */
export async function reqGetSync(url, pars, opts = {}) {
    if (pars == null) pars = {};
    if (opts.headers == null) opts.headers = {};
    if (opts.method == null) opts.method = "get";

    let idx = 0;
    for (const prop in pars) {
        if (idx === 0) {
            url = url.concat(`?${prop}=${pars[prop]}`);
        }
        else {
            url = url.concat(`&${prop}=${pars[prop]}`);
        }
        idx++;
    }

    const response = await axios[opts.method.toLowerCase()](url, {
        headers: opts.headers
    });
    return response.data;
}

/**
 * 用POST提交JSON内容
 * @param url
 * @param pars
 * @param callback

 * @param errorCallback
 * @param opts
 */
export function reqPostJson(url, pars, callback, errorCallback, opts = {}) {
    if (pars == null) pars = {};
    // if (opts.withCredentials == null) opts.withCredentials = true;
    if (opts.headers == null) opts.headers = {};
    if (opts.headers['Content-Type'] == null) opts.headers['Content-Type'] = 'application/json;charset=utf-8';

    axios({
        url: url,
        method: 'post',
        headers: opts.headers,
        data: pars,
        withCredentials: opts.withCredentials,
        responseType: opts.responseType,
    }).then(function (response) {
        const r = response.data;
        if (callback != null) {
            callback(r);
        }
    }).catch(function (error) {
        if (errorCallback != null) {
            errorCallback(error);
        }
    });
}

/** 
 * 用POST提交JSON内容（同步）
 * @param url
 * @param pars
 * @param opts
 */
export async function reqPostJsonSync(url, pars, opts = {}) {
    if (pars == null) pars = {};
    if (opts.headers == null) opts.headers = {};
    if (opts.headers['Content-Type'] == null) opts.headers['Content-Type'] = 'application/json;charset=utf-8';

    const response = await axios({
        url: url,
        method: 'post',
        headers: opts.headers,
        data: pars,
        withCredentials: opts.withCredentials,
        responseType: opts.responseType,
    });
    return response.data;
}

/**
 * 用POST提交表单
 * @param url
 * @param pars
 * @param callback
 * @param errorCallback
 * @param opts
 */
export function reqPostForm(url, pars, callback, errorCallback, opts = {}) {
    if (pars == null) pars = {};
    if (opts.headers == null) opts.headers = {};
    if (opts.headers['Content-Type'] == null) opts.headers['Content-Type'] = 'application/x-www-form-urlencoded';

    axios({
        url: url,
        method: 'post',
        headers: opts.headers,
        data: pars,
        withCredentials: opts.withCredentials,
        responseType: opts.responseType,
    }).then(function (response) {
        const r = response.data;
        if (callback != null) {
            callback(r);
        }
    }).catch(function (error) {
        if (errorCallback != null) {
            errorCallback(error);
        }
    });
}

/**
 * 用POST提交表单（同步）
 * @param url
 * @param pars
 * @param opts
 */
export async function reqPostFormSync(url, pars, opts = {}) {
    if (pars == null) pars = {};
    if (opts.headers == null) opts.headers = {};
    if (opts.headers['Content-Type'] == null) opts.headers['Content-Type'] = 'application/x-www-form-urlencoded';

    const response = await axios({
        url: url,
        method: 'post',
        headers: opts.headers,
        data: pars,
        withCredentials: opts.withCredentials,
        responseType: opts.responseType,
    });
    return response.data;
}

/**
 * 下载文件
 * 将文件下载到保存目录中
 * 根据文件下载地址中最后一个节点作为文件名
 * @param {*} url 文件下载地址
 * @param {*} saveDirPath 保存目录
 */
export function downloadFile(url, saveDirPath) {
    return new Promise((resolve, reject) => {
        const https = require('https');
        const http = require('http');
        const fs = require('fs');
        const path = require('path');

        // 确保保存目录存在
        if (!fs.existsSync(saveDirPath)) {
            fs.mkdirSync(saveDirPath, { recursive: true });
        }

        // 从URL中提取文件名
        const fileName = url.substring(url.lastIndexOf('/') + 1);
        const filePath = path.join(saveDirPath, fileName);

        // 创建写入流
        const fileStream = fs.createWriteStream(filePath);

        // 根据URL协议选择http或https
        const client = url.startsWith('https') ? https : http;

        const request = client.get(url, (response) => {
            // 处理重定向
            if (response.statusCode === 301 || response.statusCode === 302) {
                const redirectUrl = response.headers.location;
                fileStream.close();
                // 递归调用以处理重定向
                downloadFile(redirectUrl, saveDirPath)
                    .then(resolve)
                    .catch(reject);
                return;
            }

            // 检查响应状态码
            if (response.statusCode !== 200) {
                reject(new Error(`下载失败，状态码: ${response.statusCode}`));
                return;
            }

            // 将响应数据写入文件
            response.pipe(fileStream);

            // 处理错误
            fileStream.on('error', (err) => {
                fs.unlink(filePath, () => {}); // 删除不完整的文件
                reject(err);
            });

            // 完成下载
            fileStream.on('finish', () => {
                fileStream.close();
                resolve(filePath);
            });
        });

        // 处理请求错误
        request.on('error', (err) => {
            fs.unlink(filePath, () => {}); // 删除不完整的文件
            reject(err);
        });

        // 设置请求超时
        request.setTimeout(30000, () => {
            request.abort();
            fs.unlink(filePath, () => {}); // 删除不完整的文件
            reject(new Error('请求超时'));
        });
    });
}