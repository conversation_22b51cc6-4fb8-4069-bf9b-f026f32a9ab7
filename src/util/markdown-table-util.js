/**
 * 构建Markdown表格
 * @param {*} columns [{ name, label }]
 * @param {*} rows [{}]
 */
export function buildMarkdownTable(columns, rows) {
    let str = '';
    // 生成表头
    for (const i in columns) {
        const col = columns[i];
        if (parseInt(i) === 0) {
            str += `|`;
        }
        str += ` ${col.label} |`;
    }
    str += '\n';
    for (const i in columns) {
        if (parseInt(i) === 0) {
            str += `|`;
        }
        str += ` --- |`;
    }
    str += '\n';
    // 生成表体
    for (const i in rows) {
        const row = rows[i];
        for (const j in columns) {
            const col = columns[j];

            if (col.name === '{index}') {
                str += ` ${parseInt(i) + 1} |`;
            }
            else {
                let val = row[col.name];
                val = val + '';

                if (val && val.indexOf('\n') !== -1) {
                    val = val.replaceAll('\n', '');
                }
                if (val && val.indexOf('\r') !== -1) {
                    val = val.replaceAll('\r', '');
                }
                if (val && val.indexOf('\t') !== -1) {
                    val = val.replaceAll('\t', '');
                }
                if (val && val.indexOf('.') !== -1) {
                    val = val.replaceAll('.', '');
                }
                if (val && val.indexOf('|') !== -1) {
                    val = val.replaceAll('|', ' ');
                }
                if (val && val.indexOf('---') !== -1) {
                    val = val.replaceAll('---', ' ');
                }
                if (col.maxLength && val.length > col.maxLength) {
                    val = val.substring(0, col.maxLength) + '...';
                }

                if (parseInt(j) === 0) {
                    str += `|`;
                }
                str += ` ${val} |`;
            }
        }
        str += '\n';
    }
    return str;
}