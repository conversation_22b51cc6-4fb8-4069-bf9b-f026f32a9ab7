/**
 * 计算两个向量的相似度
 * @param vectorA {Array|Float32Array} 向量A
 * @param vectorB {Array|Float32Array} 向量B
 * @returns {number} 相似度，例：0.68，值越高相似度就越高
 */
export function calculateCosineSimilarity (vectorA, vectorB) {
    // 将输入转换为数组，支持 Array 和类数组对象
    const arrayA = Array.from(vectorA);
    const arrayB = Array.from(vectorB);

    // 确保两个向量的长度相同
    if (arrayA.length !== arrayB.length) {
        throw new Error('Both vectors must be of the same length.');
    }

    // 计算点积
    const dotProduct = arrayA.reduce((sum, a, index) => sum + a * arrayB[index], 0);

    // 计算模
    const magnitudeA = Math.sqrt(arrayA.reduce((sum, a) => sum + a * a, 0));
    const magnitudeB = Math.sqrt(arrayB.reduce((sum, b) => sum + b * b, 0));

    // 计算余弦相似度
    if (magnitudeA === 0 || magnitudeB === 0) {
        return 0; // 避免除以零的情况
    }

    // return dotProduct / (magnitudeA * magnitudeB);

    // 归一化向量（可选）
    const normalizedA = arrayA.map(x => x / magnitudeA);
    const normalizedB = arrayB.map(x => x / magnitudeB);

    // 使用Kahan求和算法计算点积（可选）
    let sum = 0, compensation = 0;
    for (let i = 0; i < arrayA.length; i++) {
        const y = normalizedA[i] * normalizedB[i] - compensation;
        const t = sum + y;
        compensation = (t - sum) - y;
        sum = t;
    }

    return sum;
}