
/**
 * 清理markdown中的base64图片
 * @param {string} markdownContent markdown内容
 * @returns {string} 清理后的markdown内容
 */
export function clearImageInMarkdown(markdownContent) {
    if (!markdownContent || typeof markdownContent !== 'string') {
        return markdownContent;
    }

    // 移除包含base64数据的图片标记
    // 匹配格式: ![alt text](data:image/...;base64,...)
    const base64ImageRegex = /!\[.*?\]\(data:image\/[^;]+;base64,[^)]+\)/g;

    // 移除这些图片标记
    let cleanedContent = markdownContent.replace(base64ImageRegex, '');

    // 同时移除可能的多余空行
    cleanedContent = cleanedContent.replace(/\n{3,}/g, '\n\n');

    return cleanedContent.trim();
}

/**
 * 解析markdown中的所有图片
 * @param markdownContent
 * @returns {Array} 例：[{ name, content, ext }] content就是base64内容不是DataUrl，ext是图片格式，例：.jpg / .png
 */
export function parseImagesInMarkdown(markdownContent) {

}