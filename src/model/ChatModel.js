import TopObject from "./TopObject";
import {MiniMaxChatAdapter} from "../ai_chat_adapter/MiniMaxChatAdapter";
import {MoonshotAIChatAdapter} from "../ai_chat_adapter/MoonshotAIChatAdapter";
import {OpenAIBrainWildAIChatAdapter} from "../ai_chat_adapter/OpenAIBrainWildAIChatAdapter";
import {DeepSeekChatAdapter} from "../ai_chat_adapter/DeepSeekChatAdapter";
import {OpenAIBrainAIChatAdapter} from "../ai_chat_adapter/OpenAIBrainAIChatAdapter";
import {SiliconflowChatAdapter} from "../ai_chat_adapter/SiliconflowChatAdapter";
import {OllamaChatAdapter} from "../ai_chat_adapter/OllamaChatAdapter";
import {ALiBaiLianChatAdapter} from "../ai_chat_adapter/ALiBaiLianChatAdapter";
import {ZhiPuChatAdapter} from "../ai_chat_adapter/ZhiPuChatAdapter";

import {
    OllamaAIReplyStreamChunkHelper,
    MoonshotAIReplyStreamChunkHelper,
    AIReplyStreamChunkHelper1,
    AnthropicAIReplyStreamChunkHelper
} from "../util/ai-reply-stream-util";
import {AnthropicBrainWildAIChatAdapter} from "../ai_chat_adapter/AnthropicBrainWildAIChatAdapter";

/**
 * 与基座模型聊天
 */
export default class ChatModel extends TopObject {
    constructor(ctx) {
        super(ctx);
        this.moonshotAIChatAdapter = new MoonshotAIChatAdapter(ctx);
        this.openAIBrainWildAIChatAdapter = new OpenAIBrainWildAIChatAdapter(ctx);
        this.anthropicBrainWildAIChatAdapter = new AnthropicBrainWildAIChatAdapter(ctx);
        this.deepSeekChatAdapter = new DeepSeekChatAdapter(ctx);
        this.openAIBrainAIChatAdapter = new OpenAIBrainAIChatAdapter(ctx);
        this.siliconflowChatAdapter = new SiliconflowChatAdapter(ctx);
        this.ollamaChatAdapter = new OllamaChatAdapter(ctx);
        this.aLiBaiLianChatAdapter = new ALiBaiLianChatAdapter(ctx);
        this.miniMaxChatAdapter = new MiniMaxChatAdapter(ctx);
        this.zhiPuChatAdapter = new ZhiPuChatAdapter(ctx);
    }

    /**
     * 聊天（全量输出）
     * @param model {*}
     * @param messages {Array} [{ role, content }]
     * @param opts {*} attachments, tools
     * @returns {Promise<ChatModelResult>} { reply: "", tool_calls: [{ type, name, args }], usage: { total: 0 } }
     */
    async chat1(model, messages, opts) {
        return await this.chat(model, messages, null, null, opts);
    }

    /**
     * 聊天（全量输出）
     * @param model {*}
     * @param messages {Array} [{ role, content }]
     * @param cb {function} { reply: "", tool_calls: [{ type, name, args }] }
     * @param errCb {function}
     * @param opts {*} attachments, tools
     * @param opts.attachments {[{ name, type, pushType, file, content }]} 附件
     * @param opts.temperature
     * @param opts.channel
     * @param opts.tools {Array} [{ name: "", descr: "", params: { required: [], props: { "xxx": { type: "string", descr: "" } } } }]
     * @returns {Promise<ChatModelResult>}
     */
    async chat(model, messages, cb, errCb, opts) {
        if (!opts) opts = {};
        // MoonshotAI
        if (this.moonshotAIChatAdapter.isMyModel(model)) {
            return await this.moonshotAIChatAdapter.chat(model, messages, cb, errCb, opts);
        }
        // Ollama
        else if (this.ollamaChatAdapter.isMyModel(model)) {
            return await this.ollamaChatAdapter.chat(model, messages, cb, errCb, opts);
        }
        // DeepSeek
        else if (this.deepSeekChatAdapter.isMyModel(model)) {
            return await this.deepSeekChatAdapter.chat(model, messages, cb, errCb, opts);
        }
        // 智谱AI
        else if (this.zhiPuChatAdapter.isMyModel(model)) {
            return await this.zhiPuChatAdapter.chat(model, messages, cb, errCb, opts);
        }
        // MiniMax
        else if (this.miniMaxChatAdapter.isMyModel(model)) {
            return await this.miniMaxChatAdapter.chat(model, messages, cb, errCb, opts);
        }
        // ALiBaiLian
        else if (this.aLiBaiLianChatAdapter.isMyModel(model)) {
            return await this.aLiBaiLianChatAdapter.chat(model, messages, cb, errCb, opts);
        }
        // Siliconflow
        else if (this.siliconflowChatAdapter.isMyModel(model)) {
            return await this.siliconflowChatAdapter.chat(model, messages, cb, errCb, opts);
        }
        // Anthropic（Brain-Wild）
        else if (this.anthropicBrainWildAIChatAdapter.isMyModel(model)) {
            return await this.anthropicBrainWildAIChatAdapter.chat(model, messages, cb, errCb, opts);
        }
        // OpenAI（Brain）
        else if (this.openAIBrainAIChatAdapter.isMyModel(model)) {
            return await this.openAIBrainAIChatAdapter.chat(model, messages, cb, errCb, opts);
        }
        // OpenAI（Brain-Wild）
        else if (this.openAIBrainWildAIChatAdapter.isMyModel(model)) {
            return await this.openAIBrainWildAIChatAdapter.chat(model, messages, cb, errCb, opts);
        } else {
            console.error(`未能识别的模型 -> ${model}`);
            // throw new Error(`未能识别的模型: ${model}`);
        }
    }

    /**
     * 聊天（流式输出）
     * @param model
     * @param messages
     * @param onData
     * @param onEnd
     * @param onError
     * @param opts {*} attachments, tools
     */
    async chatStream(model, messages, onData, onEnd, onError, opts) {
        if (!opts) opts = {};
        // MoonshotAI
        if (this.moonshotAIChatAdapter.isMyModel(model)) {
            return this.moonshotAIChatAdapter.chatStream(model, messages, onData, onEnd, onError, opts);
        }
        // Ollama
        else if (this.ollamaChatAdapter.isMyModel(model)) {
            return this.ollamaChatAdapter.chatStream(model, messages, onData, onEnd, onError, opts);
        }
        // DeepSeek
        else if (this.deepSeekChatAdapter.isMyModel(model)) {
            return this.deepSeekChatAdapter.chatStream(model, messages, onData, onEnd, onError, opts);
        }
        // 智谱AI
        else if (this.zhiPuChatAdapter.isMyModel(model)) {
            return this.zhiPuChatAdapter.chatStream(model, messages, onData, onEnd, onError, opts);
        }
        // MiniMax
        else if (this.miniMaxChatAdapter.isMyModel(model)) {
            return this.miniMaxChatAdapter.chatStream(model, messages, onData, onEnd, onError, opts);
        }
        // ALiBaiLian
        else if (this.aLiBaiLianChatAdapter.isMyModel(model)) {
            return this.aLiBaiLianChatAdapter.chatStream(model, messages, onData, onEnd, onError, opts);
        }
        // Siliconflow
        else if (this.siliconflowChatAdapter.isMyModel(model)) {
            return this.siliconflowChatAdapter.chatStream(model, messages, onData, onEnd, onError, opts);
        }
        // Anthropic（Brain-Wild）
        else if (this.anthropicBrainWildAIChatAdapter.isMyModel(model)) {
            return this.anthropicBrainWildAIChatAdapter.chatStream(model, messages, onData, onEnd, onError, opts);
        }
        // OpenAI（Brain）
        else if (this.openAIBrainAIChatAdapter.isMyModel(model)) {
            return this.openAIBrainAIChatAdapter.chatStream(model, messages, onData, onEnd, onError, opts);
        }
        // OpenAI（Brain-Wild）
        else if (this.openAIBrainWildAIChatAdapter.isMyModel(model)) {
            return this.openAIBrainWildAIChatAdapter.chatStream(model, messages, onData, onEnd, onError, opts);
        } else {
            console.error(`未能识别的模型: ${model}`);
            // throw new Error(`未能识别的模型: ${model}`);
        }

    }

    /**
     * 聊天（流式输出）（统一的回复块）
     * @param model
     * @param messages
     * @param onData
     * @param onEnd
     * @param onError
     * @param opts {*} attachments, tools
     */
    async chatStream1(model, messages, onData, onEnd, onError, opts) {
        const {Buffer} = this.ctx.imports;

        let hasSendReasoningStartTag = false;
        let hasSendReasoningEndTag = false;
        let reasoningChunk;
        let replyChunk;
        let aiReplyStreamChunkHelper;

        if (model.startsWith('ollama_')) {
            aiReplyStreamChunkHelper = new OllamaAIReplyStreamChunkHelper({
                onReasoningChange(val) {
                    reasoningChunk = val;
                },
                onReplyChange(val) {
                    replyChunk = val;
                }
            });
        } else if (model.startsWith('moonshot-')) {
            aiReplyStreamChunkHelper = new MoonshotAIReplyStreamChunkHelper({
                onReasoningChange(val) {
                    reasoningChunk = val;
                },
                onReplyChange(val) {
                    replyChunk = val;
                }
            });
        } else if (model.indexOf('claude') !== -1) {
            aiReplyStreamChunkHelper = new AnthropicAIReplyStreamChunkHelper({
                onReasoningChange(val) {
                    reasoningChunk = val;
                },
                onReplyChange(val) {
                    replyChunk = val;
                }
            });
        } else {
            aiReplyStreamChunkHelper = new AIReplyStreamChunkHelper1({
                onReasoningChange(val) {
                    reasoningChunk = val;
                },
                onReplyChange(val) {
                    replyChunk = val;
                }
            });
        }

        return await this.chatStream(model, messages, (chunk) => {
            const r = chunk.toString('utf8');
            // console.log('原始数据：' + r);

            // 输出回复内容
            aiReplyStreamChunkHelper.build(r, {
                debug: opts.debug,
                returnTotalReply: false,
            });

            if (reasoningChunk && reasoningChunk.length > 0) {
                if (hasSendReasoningStartTag) {
                    const buffer = Buffer.from(reasoningChunk, 'utf8');
                    onData(buffer);
                } else {
                    hasSendReasoningStartTag = true;
                    const buffer = Buffer.from('<think>\n' + reasoningChunk, 'utf8');
                    onData(buffer);
                }
            }
            if (replyChunk && replyChunk.length > 0) {
                if (hasSendReasoningStartTag && !hasSendReasoningEndTag) {
                    hasSendReasoningEndTag = true;
                    const buffer = Buffer.from('\n</think>\n' + replyChunk, 'utf8');
                    onData(buffer);
                } else {
                    const buffer = Buffer.from(replyChunk, 'utf8');
                    onData(buffer);
                }
            }
        }, onEnd, onError, opts);
    }

    /**
     * 向量化内容
     * @param model
     * @param text
     * @param cb
     * @param errCb
     * @param opts {*}
     * @returns {Promise<void>}
     */
    async embed(model, text, cb, errCb, opts) {
        // OpenAI（Brain）
        if (this.openAIBrainAIChatAdapter.isMyModel(model)) {
            return await this.openAIBrainAIChatAdapter.embed(model, text, cb, errCb, opts);
        }
        // OpenAI（Brain-Wild）
        else if (this.openAIBrainWildAIChatAdapter.isMyModel(model)) {
            return await this.openAIBrainWildAIChatAdapter.embed(model, text, cb, errCb, opts);
        }
        // 智谱AI
        else if (this.zhiPuChatAdapter.isMyModel(model)) {
            return await this.zhiPuChatAdapter.embed(model, text, cb, errCb, opts);
        }
        // Ollama
        else if (this.ollamaChatAdapter.isMyModel(model)) {
            return await this.ollamaChatAdapter.embed(model, text, cb, errCb, opts);
        }
        else {
            console.error(`未能识别的模型: ${model}`);
            // throw new Error(`未能识别的模型: ${model}`);
        }
    }

    /**
     * 聊天（全量输出）
     * @param model {*}
     * @param messages {Array} [{ role, content }]
     * @param opts {*} attachments, tools
     * @returns {Promise<unknown>}
     */
    chatSync(model, messages, opts) {
        const self = this;
        return self.chat(model, messages, (r) => {
        }, (err) => {
        }, opts);
    }
}