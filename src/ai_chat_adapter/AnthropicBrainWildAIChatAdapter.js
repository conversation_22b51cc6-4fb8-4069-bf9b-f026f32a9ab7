import { BaseAIChatAdapter } from "./BaseAIChatAdapter";
import { parseErrorMsg } from "../util/error-util";

export class AnthropicBrainWildAIChatAdapter extends BaseAIChatAdapter {

    constructor(ctx) {
        super(ctx);
    }

    getMapper() {
        return this.ctx.shortTimeCacheService.getAnthropicBrainWildMapper();
    }

    chat(model, messages, cb, errCb, opts) {
        const self = this;

        return new Promise(async function (resolve, reject) {
            try {
                // 转换附件
                self.buildAttachments_anthropic(opts.attachments, messages);
                delete opts.attachments;

                // 转换工具
                opts.tools = self.buildTools(opts.tools);

                await self.getMapper()[self.getChatFuncName()](model, messages, (r) => {
                    try {
                        if (r.success === false) {
                            if (errCb) errCb(r.msg);
                            return;
                        }

                        const message = r.content[0];

                        let cbObj = {
                        };

                        if (message.type === 'tool_use') {
                            cbObj = {
                                reply: null,
                                tool_calls: [],
                                usage: {
                                    total: r.usage.input_tokens + r.usage.output_tokens,
                                },
                            };

                            for (const tool_call of r.content) {
                                if (tool_call.type === 'tool_use') {
                                    cbObj.tool_calls.push({
                                        type: 'function',
                                        name: tool_call.name,
                                        args: tool_call.input,
                                    });
                                }
                            }
                        }
                        else {
                            cbObj = {
                                reply: message.text,
                                usage: {
                                    total: r.usage.input_tokens + r.usage.output_tokens,
                                },
                            };
                        }

                        if (cb) cb(cbObj);
                        resolve(cbObj);
                    } catch (exc) {
                        if (errCb) errCb(exc);
                    }
                }, (err) => {
                    // if (errCb) errCb(err);
                    resolve({
                        success: false,
                        reply: `出错了 -> ${parseErrorMsg(err)}`,
                        usage: {
                            total: 0,
                        }
                    });
                }, {
                    ...opts,
                });
            } catch (exc) {
                if (errCb) errCb(exc);
            }
        });
    }

    /**
     * 聊天流式输出
     * @param model
     * @param messages
     * @param onData
     * @param onEnd
     * @param onError
     * @param opts
     */
    async chatStream(model, messages, onData, onEnd, onError, opts) {
        const self = this;

        // 转换附件
        self.buildAttachments_anthropic(opts.attachments, messages);
        delete opts.attachments;
        // 转换工具
        opts.tools = self.buildTools(opts.tools);

        return await self.getMapper()[self.getChatStreamFuncName()](model, messages, onData, onEnd, onError, opts);
    }
}