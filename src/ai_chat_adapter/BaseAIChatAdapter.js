import {parseErrorMsg} from "../util/error-util";
export class BaseAIChatAdapter {

    constructor(ctx) {
        this.ctx = ctx;
    }

    getMapper() {
    }

    getChatFuncName() {
        return 'chat';
    }

    getChatStreamFuncName() {
        return 'chatStream';
    }

    getEmbedFuncName() {
        return 'embed';
    }

    /**
     * 转换附件（OpenAI标准）
     * @param {*} attachments 
     * @param {*} messages 
     */
    buildAttachments(attachments, messages) {
        if (attachments && attachments.length > 0) {
            let lastMessage = messages[messages.length - 1];
            if (lastMessage.role === 'system') {
                lastMessage = { role: 'user', content: null };
                messages.push(lastMessage);
            }

            const conList = [];
            // 插入附件
            for (const i in attachments) {
                const attachment = attachments[i];
                if (attachment.type == 'image') {
                    conList.push({ type: 'image_url', image_url: { url: attachment.content } });
                }
            }
            // 插入提问
            if (lastMessage.content && lastMessage.content.length > 0) {
                conList.push({ type: 'text', text: lastMessage.content });
            }

            lastMessage.content = conList;
        }
    }


    buildAttachments_anthropic(attachments, messages) {
        if (attachments && attachments.length > 0) {
            const removeBase64Prefix = function(base64String) {
                const prefixPattern = /^data:(image|audio|video|application)\/[a-zA-Z0-9]+;base64,/;
                return base64String.replace(prefixPattern, '');
            };

            const lastMessage = messages[messages.length - 1];

            const conList = [];
            // 插入附件
            for (const i in attachments) {
                const attachment = attachments[i];
                if (attachment.type === 'image') {
                    conList.push({
                        type: 'image',
                        source: {
                            type: 'base64',
                            media_type: attachment.content.match(/^data:((image|audio|video|application)\/[a-zA-Z0-9]+);base64,/)[1],
                            data: removeBase64Prefix(attachment.content)
                        }
                    });
                }
            }
            // 插入提问
            if (lastMessage.content && lastMessage.content.length > 0) {
                conList.push({ type: 'text', text: lastMessage.content });
            }

            lastMessage.content = conList;
        }
    }

    /**
     * 转换工具（OpenAI标准）
     * @param {*} tools [{ name, descr, params: { required: [], props: { 'xxx': { type, descr } } } }]
     * @returns 
     */
    buildTools(tools) {
        if (tools && tools.length > 0) {
            const newTools = [];
            tools.forEach((n) => {
                const properties = {};

                for (const i in n.params.props) {
                    const p = n.params.props[i];

                    const newProp = {
                        type: p.type,
                        description: p.descr,
                    };

                    if (p.enum) newProp.enum = p.enum;

                    properties[i] = newProp;
                }

                newTools.push({
                    type: 'function',
                    function: {
                        name: n.name,
                        description: n.descr,
                        parameters: {
                            type: 'object',
                            required: n.params.required,
                            properties: properties,
                        },
                    }
                });
            });
            return newTools;
        }
    }

    buildToolCalls(tool_calls) {
        if (tool_calls != null && tool_calls.length > 0) {
            const new_tool_calls = [];
            tool_calls.forEach((n) => {
                new_tool_calls.push({
                    type: 'function',
                    name: n.function.name,
                    args: n.function.arguments,
                });
            });
            return new_tool_calls;
        }
    }

    /**
     * 获取tokens使用量
     * @param {*} r 
     * @returns 
     */
    buildChatUsage(r) {
        let usage = {
            total: 0, input: 0, output: 0,
        };
        // 获取tokens使用量
        if (r.usage != null) {
            // 总量
            if (r.usage.total_tokens) {
                usage.total = r.usage.total_tokens;
            }
            // 输入量
            if (r.usage.prompt_tokens > 0) {
                usage.input = r.usage.prompt_tokens;
            }
            // 输出量
            if (r.usage.completion_tokens > 0) {
                usage.output = r.usage.completion_tokens;
            }
        }
        return usage;
    }

    /**
     * 是否为我的模型
     * @param model
     * @returns {boolean}
     */
    isMyModel(model) {
        return this.getMapper().isMyModel(model);
    }

    /**
     * 聊天
     * @param model
     * @param messages
     * @param cb
     * @param errCb
     * @param opts
     * @returns {Promise<ChatModelResult>}
     */
    chat(model, messages, cb, errCb, opts) {
        const self = this;

        return new Promise(async function (resolve, reject) {
            // 转换附件
            self.buildAttachments(opts.attachments, messages);
            delete opts.attachments;

            // 转换工具
            opts.tools = self.buildTools(opts.tools);

            await self.getMapper()[self.getChatFuncName()](model, messages, (r) => {

                if (r == null || r.choices == null) {
                    log(`出错了 -> 请求返回结果为空 -> ${JSON.stringify(r)}`);
                    resolve({
                        success: false,
                        reply: '出错了 -> 请求返回结果为空',
                        usage: {
                            total: 0,
                        },
                    });
                    return;
                }

                const message = r.choices[0].message;
                let usage = self.buildChatUsage(r);
                // log(`【调试】${JSON.stringify(usage)}`)

                const cbObj = {
                    reply: message.content,
                    tool_calls: self.buildToolCalls(message.tool_calls),
                    usage: usage,
                };

                if (cb) cb(cbObj);
                resolve(cbObj);
            }, (err) => {
                // if (errCb != null) errCb(err);
                resolve({
                    success: false,
                    reply: `出错了 -> ${parseErrorMsg(err)}`,
                    usage: {
                        total: 0,
                    }
                });
            }, {
                ...opts,
            });
        });
    }

    /**
     * 聊天流式输出
     * @param model
     * @param messages
     * @param onData
     * @param onEnd
     * @param onError
     * @param opts
     */
    async chatStream(model, messages, onData, onEnd, onError, opts) {
        const self = this;

        // 转换附件
        self.buildAttachments(opts.attachments, messages);
        delete opts.attachments;
        // 转换工具
        opts.tools = self.buildTools(opts.tools);

        return await self.getMapper()[self.getChatStreamFuncName()](model, messages, onData, onEnd, onError, opts);
    }

    async embed(model, text, cb, errCb, opts) {
        const self = this;
        const r = await self.getMapper()[self.getEmbedFuncName()](model, text, cb, errCb, opts);
        if (r.success === false) {
            return r
        }
        // ollama标准
        else if (r.embeddings) {
            return {
                embedding: r.embeddings[0],
                usage: {
                    total: 0,
                }
            };
        }
        // openai标准
        else if (r.data && r.data[0]) {
            let usageTotal = 0
            if (r.usage) {
                usageTotal = r.usage.total_tokens
            }

            return {
                embedding: r.data[0].embedding,
                usage: {
                    total: usageTotal,
                }
            };
        }
        return null;
    }
}