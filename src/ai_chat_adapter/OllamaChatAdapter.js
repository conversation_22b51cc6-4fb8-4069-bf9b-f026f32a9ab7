import { parseErrorMsg } from "../util/error-util";
import { BaseAIChatAdapter } from "./BaseAIChatAdapter";

export class OllamaChatAdapter extends BaseAIChatAdapter {

    constructor(ctx) {
        super(ctx);
    }

    getMapper() {
        return this.ctx.shortTimeCacheService.getOllamaMapper();
    }

    /**
     * 聊天
     * @param model
     * @param messages
     * @param opts
     * @param cb
     * @param errCb
     * @returns {Promise<ChatModelResult>}
     */
    chat(model, messages, cb, errCb, opts) {
        const self = this;
        if (opts == null) opts = {};
        
        return new Promise(function (resolve, reject) {
            // 转换附件
            self.buildAttachments(opts.attachments, messages);
            delete opts.attachments;

            // 转换工具
            opts.tools = self.buildTools(opts.tools);

            self.ctx.shortTimeCacheService.getOllamaMapper().chatSync(model, messages, (r) => {
                const message = r.message;

                const cbObj = {
                    reply: message.content,
                    tool_calls: self.buildToolCalls(message.tool_calls),
                    usage: {
                        total: 0,
                    },
                };

                if (cb) cb(cbObj);
                resolve(cbObj);
            }, (err) => {
                // if (errCb != null) errCb(err);
                resolve({
                    success: false,
                    reply: `出错了 -> ${parseErrorMsg(err)}`,
                    usage: {
                        total: 0,
                    }
                });
            }, {
                ...opts,
            });
        });
    }

    /**
     * 转换附件
     * @param {*} attachments 
     * @param {*} messages 
     */
    buildAttachments(attachments, messages) {
        if (attachments && attachments.length > 0) {
            const lastMessage = messages[messages.length - 1];
            lastMessage.images = [];

            const removeBase64Prefix = function (base64String) {
                const prefixPattern = /^data:(image|audio|video|application)\/[a-zA-Z0-9]+;base64,/;
                return base64String.replace(prefixPattern, '');
            };

            for (const i in attachments) {
                const attachment = attachments[i];
                if (attachment.type == 'image') {
                    lastMessage.images.push(removeBase64Prefix(attachment.content));
                }
            }
        }
    }
}