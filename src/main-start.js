import {toDateTimeStr} from "./util/time-util";
import SystemService from "./service/SystemService";

import LoginController from "./module/user/controller/LoginController";
import test_controller from "./controller/test_controller.js";
import bot_kb_bqfab_controller from "./controller/bot_kb_bqfab_controller";
import LanceDBClientController from "./controller/LanceDBClientController";
import OllamaController from "./controller/OllamaController";
import ChatModelController from "./controller/ChatModelController";
import {AnthropicAPIMapperController} from "./controller/AnthropicAPIMapperController";

import ChatModel from "./model/ChatModel";
import BotChatController from "./controller/BotChatController";
import BotKb0Controller from "./controller/BotKb0Controller";

import CSMiniAgentService from "./module/cs-mini-agent/service/CSMiniAgentService";
import DataSourceController from "./module/cs-data-source/controller/DataSourceController";
import DocImportController from "./module/doc-import/controller/DocImportController";
import {LongArticleService} from "./module/long-article/service/LongArticleService";
import {DocImportService} from "./module/doc-import/service/DocImportService";

export function start(imports, opts = {}) {
  if (opts == null) opts = {};
  const {
    express, 
    // session, cookieParser, 
    bodyParser, fs, path, 
    // lancedb, apacheArrow,
  } = imports;
  imports.ChatModel = ChatModel;

  global.log = function log(...msg) {
    let t = '';
    t = t.concat(toDateTimeStr(new Date()));
    t = t.concat(' ');
    msg.forEach((n) => {
      t = t.concat(n);
    });
    console.log(t);
  }

  const ctx = {
    imports: {
      ...imports,
      // lancedb, apacheArrow,
    }
  };
  const systemService = new SystemService(ctx);
  systemService.init({
    rootPath: opts.rootPath,
  });

  log('*** csaui-app ***');
  log(`Version: ${ctx.config.version}`);
  log(`Env: ${ctx.config.env}`);
  // 执行程序所在目录
  log(`RootPath: ${ctx.rootPath}`);
  // 用户触发执行程序所在目录
  log(`UserExecDirPath: ${path.resolve()}`)

  if (opts.startServer !== false) {
    // *** 启动服务 ***
    const app = express();
    // app.use(session({
    //   secret: '+bd3#25a',
    //   resave: true,
    //   saveUninitialized: true,
    //   cookie: {
    //     secure: false, // 是否只允许https
    //     // maxAge: 1000 * 60 * 60 * 24 * 30, // 30天
    //   }
    // }));
    // app.use(cookieParser());

    //解决跨域
    app.all('*', function (req, res, next) {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Content-Length, Authorization, Accept, X-Requested-With');
      res.header('Access-Control-Allow-Methods', 'PUT, POST, GET, DELETE, OPTIONS');
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // *** 全局请求日志中间件 ***
    if (ctx.config.env === 'dev') {
    }
    else {
      app.use((req, res, next) => {
        const ip = req.ip || req.connection.remoteAddress;
        const method = req.method;
        const url = req.originalUrl || req.url;

        const msg = `${ip} | ${method} ${url}`
        log(msg);
        ctx.shortTimeCacheService.getUserActionLogService().push('main_access', msg);
        next();
      });
    }

    // *** 静态托管（允许直接访问静态资源） ***
    app.use('/', express.static(ctx.rootPath + '/public', {
      setHeaders: function (res, path, stat) {
        res.set('Access-Control-Allow-Origin', '*');
      }
    }));
    // 用于获取post表单参数
    app.use(bodyParser.urlencoded({extended: false}));
    // 用于获取body是json的数据
    app.use(bodyParser.json({limit: '10mb', extended: true}));



    // *** 配置路由 ***
    // 接收get请求
    app.get('/api/test/0', function (req, res) {
      // 获取url参数，例：/?lang=zh-CN
      var lang = req.query.lang;
      // 获取url参数，例：/:lang/:page
      var lang = req.params.lang;
      var page = req.params.page;
      res.send("1");
      // res.send(fs.readFileSync('./view/index.html', 'utf-8'));
    });

    new LoginController(ctx, app);
    test_controller(app, ctx);
    new BotKb0Controller(ctx, app);
    // bot_kb1_controller(app, ctx);
    bot_kb_bqfab_controller(app, ctx);
    new OllamaController(ctx, app);
    new ChatModelController(ctx, app);
    new LanceDBClientController(ctx, app);
    new BotChatController(ctx, app);
    new DataSourceController(ctx, app);
    new AnthropicAPIMapperController(ctx, app);

    if (opts.startCSMiniAgentServer === true) {
      ctx.csMiniAgentService = new CSMiniAgentService(ctx, ctx.config.cs_mini_agent);
      ctx.csMiniAgentService.start({
        app
      });
    }

    if (opts.startCSMiniKbServer === true) {
      ctx.shortTimeCacheService.getCSMiniKbService().start({
        app
      })
    }

    if (opts.startLongArticleServer === true) {
      ctx.longArticleService = new LongArticleService(ctx);
      ctx.longArticleService.start({
        app
      });
    }

    if (opts.startDocImportServer === true) {
      ctx.docImportService = new DocImportService(ctx);
      ctx.docImportService.start({
        app
      })
    }

    const port = ctx.config.port;
    app.listen(port, () => {
      log(`server started at ${port}`)
    });
  }
  else {
    if (opts.startCSMiniAgentServer === true) {
      ctx.csMiniAgentService = new CSMiniAgentService(ctx, ctx.config.cs_mini_agent);
      ctx.csMiniAgentService.start({
      });
    }
  }

  return ctx;
}