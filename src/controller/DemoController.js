import TopController from "./TopController";

export default class DemoController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;

        app.post('/api/xxx/111', async function(req, res) {
            self.logReq(req, '/api/xxx/111');
            try {
                const model = req.body.model;

                res.send({
                    success: true,
                    data: null,
                });
            } catch (exc) {
                self.replyException(res, exc);
            }
        });
    }

}