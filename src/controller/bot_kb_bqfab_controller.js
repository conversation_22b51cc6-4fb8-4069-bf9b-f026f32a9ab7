import {getPartialStr} from "../util/str-util";

export default function bot_kb_bqfab_controller(app, ctx) {

    const debug = true;

    app.post('/api/kb-bqfab/chat-agent-stream', function(req, res) {
        try {
            const appId = req.body.appId;
            const content = req.body.content;

            if (debug) log(`kb-bqfab 聊天 - appId: ${appId}, content: ${getPartialStr(content, 100)}`);

            res.set({
                'Content-Type': 'text/event-stream; charset=utf-8',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
            });
            res.status(200);

            ctx.shortTimeCacheService.getBaiduQianFanAppBuilderMapper().chatAgentStream(appId, content, (r) => {
                const resStream = r;
                resStream.on('data', (chunk) => {
                    res.write(chunk);
                });
                resStream.on('end', () => {
                    res.end();
                });
            }, (err) => {
                if (debug) log(`kb-bqfab 聊天 异常`);
                log(err);
                res.send({ success: false, msg: err.message });
            });
        }
        catch (err) {
            log(err);
        }
    });
}