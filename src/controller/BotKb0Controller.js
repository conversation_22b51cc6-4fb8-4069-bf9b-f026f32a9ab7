import {getPartialStr} from "../util/str-util";
import TopController from "./TopController";

/**
 * 知识库A型接口
 * @param app
 * @param ctx
 */
export default class BotKb0Controller extends TopController{

    constructor(ctx, app) {
        super(ctx);

        // *** kbType 知识库类型，推荐度从左到右 ***
        // cmk（CS迷你知识库）, bdqfab（百度千帆AppBuilder）, oqw（Ollama）, ogpt（OpenAI）

        const self = this;
        const debug = true;

        /**
         * 聊天
         * kbType 知识库类型（）
         * kbId 知识库id, content 聊天内容
         */
        app.post('/api/kb0/chat', async function (req, res) {
            try {
                const kbType = req.body.kbType;
                const kbId = req.body.kbId;
                const content = req.body.content;
                // 加工内容类型：0 默认，1 加上MD
                const procConType = req.body.procConType;
                // 排除关键词
                const exclude_kw = req.body.exclude_kw;

                const bindId = req.body.bindId;
                const chatModelPlatform = req.body.chatModelPlatform;
                const chatModel = req.body.chatModel;

                self.logReq(req, '/api/kb0/chat', `chatModel: ${chatModel}, chatModelPlatform: ${chatModelPlatform}, kbType: ${kbType}, kbId: ${kbId}, bindId: ${bindId}, `
                    + `content: ${getPartialStr(content, 100)}, procConType: ${procConType}`);

                if (kbType === 'cmk') {
                    const chatResult = await ctx.shortTimeCacheService.getCSMiniKbService().chatSync(kbId, content, {
                        procConType, exclude_kw,
                        chatModelPlatform: chatModelPlatform,
                        chatModel: chatModel,
                    });
                    res.send({
                        success: true,
                        data: {output_text: chatResult},
                    });
                } else if (kbType === 'bdqfab') {
                    ctx.shortTimeCacheService.getBaiduQianFanAppBuilderMapper().chatAgent(bindId, content, (r) => {
                        res.send({success: true, data: {output_text: r.answer}});
                    }, err => {
                        res.send({success: false, msg: err.message});
                    });
                } else if (kbType === 'ogpt') {
                    ctx.shortTimeCacheService.getCSBotKb0Mapper().chat(kbId, content, (r) => {
                        res.send(r);
                    }, (err) => {
                        res.send({success: false, msg: err.msg});
                    });
                } else if (kbType === 'oqw') {
                    ctx.shortTimeCacheService.getCSBotKb0V2Mapper().chat(kbId, content, (r) => {
                        res.send(r);
                    }, (err) => {
                        res.send({success: false, msg: err.msg});
                    });
                } else {
                    res.send({success: false, msg: `未能识别的kbType - ${kbType}`});
                }
            } catch (err) {
                self.replyException(res, err);
            }
        });

        /**
         * 聊天
         * kbType 知识库类型（）
         * kbId 知识库id, content 聊天内容
         */
        app.post('/api/kb0/chat-stream', async function (req, res) {
            try {
                const kbType = req.body.kbType;
                const kbId = req.body.kbId;
                const content = req.body.content;
                // 加工内容类型：0 默认，1 加上MD
                const procConType = req.body.procConType;
                // 排除关键词
                const exclude_kw = req.body.exclude_kw;

                const bindId = req.body.bindId;
                const chatModelPlatform = req.body.chatModelPlatform;
                const chatModel = req.body.chatModel;

                self.logReq(req, '/api/kb0/chat', `chatModel: ${chatModel}, chatModelPlatform: ${chatModelPlatform}, kbType: ${kbType}, kbId: ${kbId}, bindId: ${bindId}, `
                    + `content: ${getPartialStr(content, 100)}, procConType: ${procConType}`);

                if (kbType === 'cmk') {
                    await ctx.shortTimeCacheService.getCSMiniKbService().chatStreamSync(kbId, content,
                        (chunk) => {
                            // if (debug) log(`------------------------------------------------------------------`);
                            // if (debug) log(`${chunk.toString('utf8')}`);
                            res.write(chunk);
                        }, () => {
                            res.end();
                        }, (err) => {
                            log('chat-stream错误 -> ', err);
                            res.status(500).send({success: false, msg: err.message});
                        }, {
                            procConType, exclude_kw,
                            chatModelPlatform: chatModelPlatform,
                            chatModel: chatModel,
                        });
                } else {
                    res.send({success: false, msg: `未能识别的kbType - ${kbType}`});
                }
            } catch (err) {
                self.replyException(res, err);
            }
        });

        /**
         * 创建知识库
         */
        app.post('/api/kb0/newKb', async function (req, res) {
            try {
                const ip = req.ip;
                const kbType = req.body.kbType;
                const kbId = req.body.kbId;
                const form = req.body.form;

                if (debug) log(`${ip} | /api/kb0/newKb | kbType: ${kbType}, kbId: ${kbId}, form: ${JSON.stringify(form)}`);

                ctx.shortTimeCacheService.getKbMapper().save(kbId, form);

                if (kbType === 'cmk') {
                    await ctx.shortTimeCacheService.getCSMiniKbService().createKBSync(kbId);
                    res.send({success: true});
                } else {
                    res.send({success: true});
                }
            } catch (err) {
                log(err);
                res.send({success: false, msg: `请求异常 -> ${err.message}`});
            }
        });

        /**
         * 修改知识库
         */
        app.post('/api/kb0/editKb', async function (req, res) {
            try {
                const ip = req.ip;
                const kbType = req.body.kbType;
                const kbId = req.body.kbId;
                const form = req.body.form;

                if (debug) log(`${ip} | /api/kb0/editKb | kbType: ${kbType}, kbId: ${kbId}, form: ${JSON.stringify(form)}`);

                ctx.shortTimeCacheService.getKbMapper().save(kbId, form);

                if (kbType === 'cmk') {
                    try {
                        await ctx.shortTimeCacheService.getCSMiniKbService().createKBSync(kbId);
                    } catch (exc) {
                        log(exc.message);
                    }
                    res.send({success: true});
                } else {
                    res.send({success: true});
                }
            } catch (err) {
                log(err);
                res.send({success: false, msg: `请求异常 -> ${err.message}`});
            }
        });

        /**
         * 删除知识库
         */
        app.post('/api/kb0/delKb', function (req, res) {
            try {
                const ip = req.ip;
                const kbType = req.body.kbType;
                const kbId = req.body.kbId;

                if (debug) log(`${ip} | /api/kb0/delKb | kbType: ${kbType}, kbId: ${kbId}`);

                res.send({success: false, msg: "暂不支持此功能"});
            } catch (err) {
                log(err);
                res.send({success: false, msg: `请求异常 -> ${err.message}`});
            }
        });

        /**
         * 设置知识碎片
         * kbType 知识库类型
         * kbId 知识库id, kfId 知识碎片id, content 知识碎片内容
         */
        app.post('/api/kb0/setKf', async function (req, res) {
            try {
                const ip = req.ip;
                const kbType = req.body.kbType;
                const kbId = req.body.kbId;
                const kfId = req.body.kfId;
                const title = req.body.title;
                const content = req.body.content;
                // 是否是预设回答，例：'1' 是，'0' 否
                const is_yshd = req.body.is_yshd;

                if (debug) log(`${ip} | /api/kb0/setKf | kbType: ${kbType}, kbId: ${kbId}, kfId: ${kfId}, is_yshd: ${is_yshd}`
                    + `, title: ${title}, content: ${getPartialStr(content, 50).replaceAll('\n', '')}`);

                if (kbType === 'cmk') {
                    await ctx.shortTimeCacheService.getCSMiniKbService().setKfSync(kbId, {
                        id: kfId, title: title, content: content, is_yshd: is_yshd,
                    });
                    res.send({success: true});
                } else if (kbType === 'ogpt') {
                    ctx.shortTimeCacheService.getCSBotKb0Mapper().setKf(kbId, kfId, content, (r) => {
                        if (debug) log(`kb0 设置知识碎片 ${JSON.stringify(r)}`);
                        res.send(r);
                    }, (err) => {
                        if (debug) log(`kb0 设置知识碎片 -> ${err.msg}`);
                        res.send({success: false, msg: err.message});
                    });
                } else if (kbType === 'oqw') {
                    ctx.shortTimeCacheService.getCSBotKb0V2Mapper().setKf(kbId, kfId, content, (r) => {
                        if (debug) log(`kb0 设置知识碎片 ${JSON.stringify(r)}`);
                        res.send(r);
                    }, (err) => {
                        if (debug) log(`kb0 设置知识碎片 -> ${err.msg}`);
                        res.send({success: false, msg: err.message});
                    });
                } else {
                    res.send({success: false, msg: `未能识别的kbType - ${kbType}`});
                }
            } catch (err) {
                log(`${err} -> ${err.stack}`);
                res.send({success: false, msg: `请求异常 -> ${err.message}`});
            }
        });

        /**
         * 删除知识碎片
         * kbType 知识库类型
         * kbId 知识库id, kfId 知识碎片id
         */
        app.post('/api/kb0/delKf', async function (req, res) {
            try {
                const ip = req.ip;
                const kbType = req.body.kbType;
                const kbId = req.body.kbId;
                const kfId = req.body.kfId;

                if (debug) log(`${ip} | /api/kb0/delKf | kbType: ${kbType}, kbId: ${kbId}, kfId: ${kfId}`);

                if (kbType === 'cmk') {
                    await ctx.shortTimeCacheService.getCSMiniKbService().deleteKfSync(kbId, kfId);
                    res.send({success: true});
                } else if (kbType === 'ogpt') {
                    ctx.shortTimeCacheService.getCSBotKb0Mapper().delKf(kbId, kfId, (r) => {
                        if (debug) log(`kb0 删除知识碎片 ${JSON.stringify(r)}`);
                        res.send(r);
                    }, (err) => {
                        if (debug) log(`kb0 删除知识碎片 -> ${err.msg}`);
                        res.send({success: false, msg: err.message});
                    });
                } else if (kbType === 'oqw') {
                    ctx.shortTimeCacheService.getCSBotKb0V2Mapper().delKf(kbId, kfId, (r) => {
                        if (debug) log(`kb0 删除知识碎片 ${JSON.stringify(r)}`);
                        res.send(r);
                    }, (err) => {
                        if (debug) log(`kb0 删除知识碎片 -> ${err.msg}`);
                        res.send({success: false, msg: err.message});
                    });
                } else {
                    res.send({success: false, msg: `未能识别的kbType - ${kbType}`});
                }
            } catch (err) {
                log(err);
                res.send({success: false, msg: `请求异常 -> ${err.message}`});
            }
        });

        /**
         * 清空知识碎片
         * kbType 知识库类型
         * kbId 知识库id
         */
        app.post('/api/kb0/clearKf', async function (req, res) {
            try {
                const ip = req.ip;
                const kbType = req.body.kbType;
                const kbId = req.body.kbId;

                if (debug) log(`${ip} | /api/kb0/clearKf | kbType: ${kbType}, kbId: ${kbId}`);

                if (kbType === 'cmk') {
                    await ctx.shortTimeCacheService.getCSMiniKbService().clearKfSync(kbId);
                    res.send({success: true});
                } else if (kbType === 'ogpt') {
                    ctx.shortTimeCacheService.getCSBotKb0Mapper().clearKf(kbId, (r) => {
                        if (debug) log(`kb0 清空知识碎片 ${JSON.stringify(r)}`);
                        res.send(r);
                    }, (err) => {
                        if (debug) log(`kb0 清空知识碎片 -> ${err.msg}`);
                        res.send({success: false, msg: err.message});
                    });
                } else if (kbType === 'oqw') {
                    ctx.shortTimeCacheService.getCSBotKb0V2Mapper().clearKf(kbId, (r) => {
                        if (debug) log(`kb0 清空知识碎片 ${JSON.stringify(r)}`);
                        res.send(r);
                    }, (err) => {
                        if (debug) log(`kb0 清空知识碎片 -> ${err.msg}`);
                        res.send({success: false, msg: err.message});
                    });
                } else {
                    res.send({success: false, msg: `未能识别的kbType - ${kbType}`});
                }
            } catch (err) {
                log(err);
                res.send({success: false, msg: `请求异常 -> ${err.message}`});
            }
        });

        /**
         * 全量训练知识库
         * kbType 知识库类型
         * kbId 知识库id
         */
        app.post('/api/kb0/fullTraining', function (req, res) {
            try {
                const ip = req.ip;
                const kbType = req.body.kbType;
                const kbId = req.body.kbId;

                if (debug) log(`${ip} | /api/kb0/fullTraining | kbType: ${kbType}, kbId: ${kbId}`);

                if (kbType === 'cmk') {
                    res.send({ success: true });
                } else if (kbType === 'ogpt') {
                    ctx.shortTimeCacheService.getCSBotKb0Mapper().fullTraining(kbId, (r) => {
                        if (debug) log(`kb0 全量训练知识库 ${JSON.stringify(r)}`);
                        res.send(r);
                    }, (err) => {
                        if (debug) log(`kb0 全量训练知识库 -> ${err.msg}`);
                        res.send({success: false, msg: err.message});
                    });
                } else if (kbType === 'oqw') {
                    ctx.shortTimeCacheService.getCSBotKb0V2Mapper().fullTraining(kbId, (r) => {
                        if (debug) log(`kb0 全量训练知识库 ${JSON.stringify(r)}`);
                        res.send(r);
                    }, (err) => {
                        if (debug) log(`kb0 全量训练知识库 -> ${err.msg}`);
                        res.send({success: false, msg: err.message});
                    });
                } else {
                    res.send({success: false, msg: `未能识别的kbType - ${kbType}`});
                }
            } catch (err) {
                log(err);
                res.send({success: false, msg: `请求异常 -> ${err.message}`});
            }
        });
    }
}