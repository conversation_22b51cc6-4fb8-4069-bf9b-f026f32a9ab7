import TopController from './TopController';

export default class ChatModelController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;

        /**
         * 发送消息
         * @param {String} req.body.token 令牌
         * @param {String} req.body.model 模型
         * @param {[]} req.body.messages 消息
         * @param {[]} req.body.opts.attachments 附件
         * @param {[]} req.body.opts.tools 工具
         * @param {[]} req.body.opts.max_tokens 最大令牌数
         */
        app.post('/api/chat-model/chat', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/chat-model/chat', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                const model = req.body.model;
                const messages = req.body.messages;
                const opts = req.body.opts || {};

                // console.log(`${model} | ${JSON.stringify(messages)}`)
                // console.log(`${JSON.stringify(opts)}`)
                // ...
                const r = await ctx.shortTimeCacheService.getChatModel().chat(model, messages, (r) => {
                    }, (err) => {
                        self.replyException(res, err, null, req);
                    },
                    opts);
                res.send({success: true, data: r});
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         * 发送消息（流式输出）
         * @param {String} req.body.token 令牌
         * @param {String} req.body.model 模型
         * @param {[]} req.body.messages 消息
         * @param {[]} req.body.opts.attachments 附件
         * @param {[]} req.body.opts.tools 工具
         * @param {[]} req.body.opts.max_tokens 最大令牌数
         */
        app.post('/api/chat-model/chat-stream', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/chat-model/chat-stream', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;

                const model = req.body.model;
                const messages = req.body.messages;
                const opts = req.body.opts || {};
                // ...
                res.set({
                    'Content-Type': 'text/event-stream; charset=utf-8',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                });
                res.status(200);
                // ...
                await ctx.shortTimeCacheService.getChatModel().chatStream1(model, messages,
                    (chunk) => {
                        res.write(chunk);
                    }, () => {
                        res.end();
                    }, (err) => {
                        self.replyException(res, err);
                    }, opts);
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });
    }
}