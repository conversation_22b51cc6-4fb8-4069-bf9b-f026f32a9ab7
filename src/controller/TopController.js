import TopObject from "../model/TopObject";

export default class TopController extends TopObject {

    /**
     * 验证登录
     * @param {Object} req 请求
     * @param {Object} res 响应
     * @returns {Boolean} 是否登录
     */
    async validateLogin(req, res) {
        const token = req.body.token || req.query.token;
        const r = await this.ctx.shortTimeCacheService.getLoginService().isLogin(token, req, res);
        if (!r.isLogin) {
            res.send({ success: false, msg: '未登陆', code: 10001 });
            return null;
        }
        return r;
    }

    /**
     * 记录请求日志
     * @param req
     * @param cmd
     * @param someStr 采样信息
     * @param validateLoginResult
     */
    logReq(req, cmd, someStr, validateLoginResult) {
        const ip = req.ip;

        let loginUserLabel = '';
        let someStrLabel = '';

        if (validateLoginResult) {
            if (validateLoginResult.isLogin) {
                const usr = validateLoginResult.user;
                loginUserLabel = ` ${usr.username}@${usr.sac}:${usr.realname} |`;
            }
            else {
                loginUserLabel = ` 未登录 |`;
            }
        }

        if (someStr != null && someStr.length > 0) {
            if (someStr.length > 50) {
                someStrLabel = ` （${someStr.substring(0, 100)}...）`;
            }
            else {
                someStrLabel = ` （${someStr}）`;
            }
        }

        const msg = `请求接口 | ${ip} |${loginUserLabel} ${cmd} ...${someStrLabel}`;
        log(msg);
        this.ctx.shortTimeCacheService.getUserActionLogService().push('main_api', msg);
    }

    /**
     * 回复异常
     * @param res
     * @param exc
     * @param tag
     * @param req
     */
    replyException(res, exc, tag, req) {
        let tagLabel = '';
        let ipLabel = '';

        if (tag != null && tag.length > 0) {
            tagLabel = `(${tag})`;
        }

        if (req) {
            const ip = req.ip;
            ipLabel = ` | ${ip}`;
        }

        if (exc == null) {
            log(`请求接口异常${tagLabel}${ipLabel} -> null`);
        }
        else if (exc.data) {
            if (exc.data.error) {
                log(`请求接口异常${tagLabel}${ipLabel} -> ${exc.data.error}`);
            }
            else {
                log(`请求接口异常${tagLabel}${ipLabel} -> ${JSON.stringify(exc.data)}`);
            }
        }
        else if (exc.error) {
            if (typeof(exc) === 'object') {
                if (exc.error.message) {
                    log(`请求接口异常${tagLabel}${ipLabel} -> ${exc.error.message}`);
                }
                else {
                    log(`请求接口异常${tagLabel}${ipLabel} -> ${JSON.stringify(exc.error)}`);
                }
            }
            else {
                log(`请求接口异常${tagLabel}${ipLabel} -> ${exc.error}`);
            }
        }
        else if (exc.response) {
            if (exc.response.data) {
                if (exc.response.data.message) {
                    log(`请求接口异常${tagLabel}${ipLabel} -> ${exc.message} -> ${exc.response.data.message}`);
                }
                else if (exc.response.data.error) {
                    if (exc.response.data.error.message) {
                        log(`请求接口异常${tagLabel}${ipLabel} -> ${exc.message} -> ${exc.response.data.error.message}`);
                    }
                    else {
                        log(`请求接口异常${tagLabel}${ipLabel} -> ${exc.message} -> ${exc.response.data.error}`);
                    }
                }
                else {
                    log(`请求接口异常${tagLabel}${ipLabel} -> ${exc.message} -> ${exc.response.data}`);
                }
            }
            else if (exc.response.error) {
                log(`请求接口异常${tagLabel}${ipLabel} -> ${exc.message} -> ${exc.response.error}`);
            }
            else {
                log(`请求接口异常${tagLabel}${ipLabel} -> ${exc.message} -> ${exc.response}`);
            }
        }
        else if (exc.message) {
            log(`请求接口异常${tagLabel}${ipLabel} -> ${exc.message}`);
        }
        else {
            log(`请求接口异常${tagLabel}${ipLabel} -> ${exc}`);
        }

        if (exc && exc.stack) {
            log(exc.stack);
        }

        if (!res.headersSent) {
            res.send({
                success: false,
                msg: exc && exc.message ? exc.message : '未知错误',
            });
        }
    }

    _stringifyOneLayer(obj) {
        let str = '{ ';
        if (obj) {
            for (const prop in obj) {
                str += `${prop}: ${obj[prop]}, `;
            }
        }
        str += ' }';
        return str;
    }
}