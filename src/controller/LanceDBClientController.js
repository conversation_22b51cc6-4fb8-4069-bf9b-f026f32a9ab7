import TopController from "./TopController";
import LanceDBMapper from "../mapper/LanceDBMapper";

export default class LanceDBClientController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;
        app.get('/api/ldb-client/see-table-all-rows', async function(req, res) {
            try {
                const kbId = req.query.kbId;

                const lanceDBMapper = new LanceDBMapper(ctx);
                const dbDirPath = `${ctx.rootPath}/data/cs-mini-kb/kb/${kbId}`;
                const conn = await lanceDBMapper.connectSync(dbDirPath);
                const findList = await lanceDBMapper.getTableRows(conn, 'table_0', 'true');

                let str = '';
                str += `共找到 ${findList.length} 条\n`;
                for (const item of findList) {
                    str += `${item.id} | ${item.title}\n`;
                }
                res.send(`<html><body><pre>${str}</pre></body></html>`);
            } catch (exc) {
                self.replyException(res, exc);
            }
        });
    }

}