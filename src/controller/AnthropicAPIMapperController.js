import TopController from "./TopController";

/**
 * Anthropic的API转发控制器
 * 讲请求转发到代理商的接口
 */
export class AnthropicAPIMapperController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;
        const debug = false;

        const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
        const OPENROUTER_API_KEY = '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';

        const WILD_BASE_URL = 'https://api.gptsapi.net';
        const WILD_API_KEY = 'sk-8o4939e51df248747171ea36482cb96096b8527b838CMfVV';


        app.post('/api/anthropic-wild/v1/messages', async function (req, res) {
            // 代理商接口地址
            const reqUrl = `${WILD_BASE_URL}/v1/messages`;

            self.logReq(req, `/api/anthropic-wild/v1/messages`, JSON.stringify(req.body));
            const t_body = {
                ...req.body
            };
            delete t_body.system;
            delete t_body.messages;
            delete t_body.tools;
            if (debug) console.log(`【调试】${JSON.stringify(t_body)}`)

            // 获取请求参数
            let { messages, model, stream, max_tokens, temperature } = req.body;
            if (debug) console.log(`【调试】model: ${model}, stream: ${stream}`);

            // 转换为代理商的模型名称
            let newModel = model;

            // 进行接口转发
            const requestBody = {
                model: newModel,
                messages: messages,
                stream: stream || false,
                max_tokens: max_tokens || 4096,
                temperature: temperature || 0.7
            };

            try {
                const response = await fetch(reqUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=utf-8',
                        'x-api-key': `${WILD_API_KEY}`,
                        "Accept-Encoding": "gzip,deflate,compress",
                        // 'HTTP-Referer': process.env.HTTP_REFERER || 'http://localhost:3000',
                        // 'X-Title': process.env.X_TITLE || 'CS Bot All In One'
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    self.replyException(res, null, `代理接口错误: ${response.status} - ${errorText}`, req);
                    if (!res.headersSent) {
                        return res.status(response.status).json({ error: errorText });
                    }
                    return;
                }

                // 处理流式响应
                if (stream) {
                    if (!res.headersSent) {
                        res.setHeader('Content-Type', 'text/event-stream');
                        res.setHeader('Cache-Control', 'no-cache');
                        res.setHeader('Connection', 'keep-alive');
                        res.setHeader('Access-Control-Allow-Origin', '*');
                    }

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();

                    try {
                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) break;

                            const chunk = decoder.decode(value, { stream: true });
                            if (debug) console.log(`【调试】原始chunk: ${chunk}`)
                            res.write(chunk);
                        }
                    } catch (streamError) {
                        self.replyException(res, streamError, `流式响应错误: ${streamError.message}`, req);
                    } finally {
                        if (!res.destroyed && !res.writableEnded) {
                            res.end();
                        }
                    }
                } else {
                    // 处理非流式响应
                    const data = await response.json();
                    if (debug) console.log(`【调试】${JSON.stringify(data)}`)

                    if (!res.headersSent) {
                        res.json(data);
                    }
                }

                if (!res.headersSent) {
                    self.logRes(req, '/api/anthropic-wild/v1/messages', '转发成功');
                }
            } catch (error) {
                self.replyException(res, error, `请求转发失败: ${error.message || error}`, req);
                if (!res.headersSent) {
                    res.status(500).json({ error: '内部服务器错误' });
                }
            }
        });

        //【废弃】需要把OpenAI转为Anthropic标准，还没成功
        app.post('/api/anthropic-openrouter/v1/messages', async function (req, res) {
            // 代理商接口地址
            const reqUrl = `${OPENROUTER_BASE_URL}/chat/completions`;

            self.logReq(req, `/api/anthropic-openrouter/v1/messages`, JSON.stringify(req.body));

            // 获取请求参数
            let { messages, model, stream, max_tokens, temperature } = req.body;
            console.log(`【调试】model: ${model}, stream: ${stream}`);

            // 转换为代理商的模型名称
            let newModel = model;
            if (model.indexOf('claude-sonnet-4-') !== -1) {
                newModel = 'anthropic/claude-sonnet-4';
            }
            else if (model.indexOf('claude-3-5-haiku') !== -1) {
                newModel = 'anthropic/claude-3.5-haiku';
            }

            // 进行接口转发
            const requestBody = {
                model: newModel,
                messages: messages,
                stream: stream || false,
                max_tokens: max_tokens || 4096,
                temperature: temperature || 0.7
            };

            try {
                const response = await fetch(reqUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                        // 'HTTP-Referer': process.env.HTTP_REFERER || 'http://localhost:3000',
                        // 'X-Title': process.env.X_TITLE || 'CS Bot All In One'
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    self.replyException(res, null, `代理接口错误: ${response.status} - ${errorText}`, req);
                    if (!res.headersSent) {
                        return res.status(response.status).json({ error: errorText });
                    }
                    return;
                }

                // 处理流式响应
                if (stream) {
                    if (!res.headersSent) {
                        res.setHeader('Content-Type', 'text/event-stream');
                        res.setHeader('Cache-Control', 'no-cache');
                        res.setHeader('Connection', 'keep-alive');
                        res.setHeader('Access-Control-Allow-Origin', '*');
                    }

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();

                    try {
                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) break;

                            const chunk = decoder.decode(value, { stream: true });
                            if (debug) console.log(`【调试】原始chunk: ${chunk}`)

                            // 转换OpenAI格式到Anthropic格式
                            const anthropicChunk = self.convertOpenAIStreamReplyToAnthropicStreamReply(chunk);
                            if (debug) console.log(`【调试】转换后chunk: ${anthropicChunk}`)

                            if (!res.destroyed && !res.writableEnded && anthropicChunk) {
                                res.write(anthropicChunk);
                            }
                        }
                    } catch (streamError) {
                        self.replyException(res, streamError, `流式响应错误: ${streamError.message}`, req);
                    } finally {
                        if (!res.destroyed && !res.writableEnded) {
                            res.end();
                        }
                    }
                } else {
                    // 处理非流式响应
                    const data = await response.json();
                    if (debug) console.log(`【调试】${JSON.stringify(data)}`)

                    // 转换标准
                    const newData = {
                        content: [],
                        id: null,
                        model: model,
                        role: 'assistant',
                        stop_reason: "end_turn",
                        stop_sequence: null,
                        type: 'message',
                        usage: {
                            input_tokens: data.usage.prompt_tokens,
                            output_tokens: data.usage.completion_tokens
                        }
                    };
                    for (const choice of data.choices) {
                        newData.content.push({
                            text: choice.message.content,
                            type: 'text',
                        });
                    }

                    if (!res.headersSent) {
                        res.json(newData);
                    }
                }

                if (!res.headersSent) {
                    self.logRes(req, '/api/anthropic-openrouter/v1/messages', '转发成功');
                }
            } catch (error) {
                self.replyException(res, error, `请求转发失败: ${error.message || error}`, req);
                if (!res.headersSent) {
                    res.status(500).json({ error: '内部服务器错误' });
                }
            }
        });
    }

    /**
     * 把OpenAI标准流数据块转为Anthropic标准流数据库
     * @param chunk {String}
     */
    convertOpenAIStreamReplyToAnthropicStreamReply(chunk) {
        if (!chunk || chunk.trim() === '') return '';
        
        const lines = chunk.split('\n');
        let result = '';
        
        for (const line of lines) {
            if (line.startsWith('data: ')) {
                const data = line.substring(6).trim();
                
                if (data === '[DONE]') {
                    // OpenAI结束标记转换为Anthropic格式
                    result += 'event: message_stop\n';
                    result += 'data: {"type":"message_stop"}\n\n';
                    continue;
                }
                
                if (data === '') continue;
                
                try {
                    const openaiData = JSON.parse(data);
                    
                    // 转换OpenAI格式到Anthropic格式
                    if (openaiData.choices && openaiData.choices.length > 0) {
                        const choice = openaiData.choices[0];
                        
                        if (choice.delta && choice.delta.content) {
                            // 内容增量
                            const anthropicData = {
                                type: 'content_block_delta',
                                index: 0,
                                delta: {
                                    type: 'text_delta',
                                    text: choice.delta.content
                                }
                            };
                            
                            result += 'event: content_block_delta\n';
                            result += `data: ${JSON.stringify(anthropicData)}\n\n`;
                        }
                        
                        if (choice.finish_reason) {
                            // 结束原因
                            result += 'event: message_stop\n';
                            result += 'data: {"type":"message_stop"}\n\n';
                        }
                    }
                } catch (e) {
                    // 解析失败，跳过这行
                    continue;
                }
            } else if (line.trim() === '') {
                // 保持空行
                result += '\n';
            }
        }
        
        return result;
    }
}