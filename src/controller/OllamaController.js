import TopController from "./TopController";

export default class OllamaController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;

        app.post('/api/ollama/chat', async function(req, res) {
            self.logReq(req, '/api/ollama/chat');
            try {
                const model = req.body.model;
                const messages = req.body.messages;

                const ollamaMapper = ctx.shortTimeCacheService.getOllamaMapper();
                const r = await ollamaMapper.chatSync(model, messages);
                res.send({
                    success: true,
                    data: r,
                });
            } catch (exc) {
                self.replyException(res, exc);
            }
        });
    }

}