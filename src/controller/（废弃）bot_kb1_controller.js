import {getPartialStr} from "../util/str-util";

/**
 * 知识库B型接口
 * @param app
 * @param ctx
 */
export default function bot_kb1_controller(app, ctx) {

    // *** kbType 知识库类型，推荐度从左到右 ***
    // allm（AnythingLLM）

    const debug = true;

    /**
     * 聊天
     * kbType 知识库类型
     * kbId 知识库id, bindId 工作空间id, content 聊天内容
     */
    app.post('/api/kb1/chat', function(req, res) {
        try {
            const ip = req.ip;
            const kbType = req.body.kbType;
            const kbId = req.body.kbId;
            const bindId = req.body.bindId;
            const content = req.body.content;

            if (debug) log(`${ip} | /api/kb1/chat | kbType: ${kbType}, kbId: ${kbId}, bindId: ${bindId}, content: ${getPartialStr(content, 100)}`);

            if (kbType === 'allm') {
                ctx.shortTimeCacheService.getAnythingLLMMapper().tempThreadChat(bindId, content, 'query', (r) => {
                    if (debug) log(`kb1 聊天 完毕 ${getPartialStr(JSON.stringify(r), 100)}`);
                    if (r.sources != null && r.sources.length > 0) {
                        for (const source of r.sources) {
                            for (const prop in source) {
                                if (prop !== 'title') {
                                    delete source[prop];
                                }
                            }
                        }
                    }
                    res.send({ success: true, data: r });
                }, (err) => {
                    if (debug) log(`kb1 聊天 -> ${err.message}`);
                    res.send({ success: false, msg: err.message });
                });
            }
            else {
                res.send({ success: false, msg: `未能识别的kbType - ${kbType}` });
            }
        }
        catch (err) {
            log(err);
            res.send({ success: false, msg: `请求异常 -> ${err.message}` });
        }
    });

    /**
     * 创建知识库
     */
    app.post('/api/kb1/newKb', function(req, res) {
        try {
            const ip = req.ip;
            const kbType = req.body.kbType;
            const kbId = req.body.kbId;

            if (debug) log(`${ip} | /api/kb0/newKb | kbType: ${kbType}, kbId: ${kbId}`);

            res.send({ success: false, msg: "暂不支持此功能"});
        }
        catch (err) {
            log(err);
            res.send({ success: false, msg: `请求异常 -> ${err.message}` });
        }
    });

    /**
     * 修改知识库
     */
    app.post('/api/kb1/editKb', function(req, res) {
        try {
            const ip = req.ip;
            const kbType = req.body.kbType;
            const kbId = req.body.kbId;

            if (debug) log(`${ip} | /api/kb1/editKb | kbType: ${kbType}, kbId: ${kbId}`);

            res.send({ success: false, msg: "暂不支持此功能"});
        }
        catch (err) {
            log(err);
            res.send({ success: false, msg: `请求异常 -> ${err.message}` });
        }
    });

    /**
     * 删除知识库
     */
    app.post('/api/kb1/delKb', function(req, res) {
        try {
            const ip = req.ip;
            const kbType = req.body.kbType;
            const kbId = req.body.kbId;

            if (debug) log(`${ip} | /api/kb1/delKb | kbType: ${kbType}, kbId: ${kbId}`);

            res.send({ success: false, msg: "暂不支持此功能"});
        }
        catch (err) {
            log(err);
            res.send({ success: false, msg: `请求异常 -> ${err.message}` });
        }
    });

    /**
     * 设置知识碎片-RawText
     * kbType 知识库类型
     * kbId 知识库id, kfId 知识碎片id, content 知识碎片内容
     * @return {*} { success: true, data: <location> }
     */
    app.post('/api/kb1/setKfRawText', function(req, res) {
        try {
            const ip = req.ip;
            const kbType = req.body.kbType;
            const kbId = req.body.kbId;
            const kfId = req.body.kfId;
            const bindId = req.body.bindId;
            const title = req.body.title;
            const content = req.body.content;

            if (debug) log(`${ip} | /api/kb1/setKfRawText | kbType: ${kbType}, kbId: ${kbId}, kfId: ${kfId}, bindId: ${bindId}, title: ${title}, content: ${getPartialStr(content, 100)}`);

            if (kbType === 'allm') {
                ctx.shortTimeCacheService.getAnythingLLMMapper().setWorkspaceRawText(kbId, kfId, bindId, content, (r) => {
                    if (debug) log(`kb1 设置知识碎片-RawText 完毕 ${JSON.stringify(r)}`);
                    res.send({ success: true, data: r });
                }, (err) => {
                    if (debug) log(`kb1 设置知识碎片-RawText -> ${err.message}`);
                    res.send({ success: false, msg: err.message });
                });
            }
            else {
                res.send({ success: false, msg: `未能识别的kbType - ${kbType}` });
            }
        }
        catch (err) {
            log(err);
            res.send({ success: false, msg: `请求异常 -> ${err.message}` });
        }
    });

    /**
     * 删除知识碎片
     * kbType 知识库类型
     * kbId 知识库id, location 知识碎片位置
     */
    app.post('/api/kb1/delKf', function(req, res) {
        try {
            const ip = req.ip;
            const kbType = req.body.kbType;
            const kbId = req.body.kbId;
            const kfId = req.body.kfId;
            const bindId = req.body.bindId;

            if (debug) log(`${ip} | /api/kb1/delKf | kbType: ${kbType}, kbId: ${kbId}, kfId: ${kfId}, bindId: ${bindId}`);

            if (kbType === 'allm') {
                ctx.shortTimeCacheService.getAnythingLLMMapper().deleteWorkspaceRawText(kbId, kfId, bindId, (r) => {
                    if (debug) log(`kb1 删除知识碎片-RawText 完毕`);
                    res.send({ success: true });
                }, (err) => {
                    if (debug) log(`kb1 删除知识碎片-RawText -> ${err.message}`);
                    res.send({ success: false, msg: err.message });
                });
            }
            else {
                res.send({ success: false, msg: `未能识别的kbType - ${kbType}` });
            }
        }
        catch (err) {
            log(err);
            res.send({ success: false, msg: `请求异常 -> ${err.message}` });
        }
    });

    /**
     * 删除知识碎片
     * kbType 知识库类型
     * kbId 知识库id, location 知识碎片位置
     */
    app.post('/api/kb1/delKfByLocation', function(req, res) {
        try {
            const ip = req.ip;
            const kbType = req.body.kbType;
            const kbId = req.body.kbId;
            const kfId = req.body.kfId;
            const bindId = req.body.bindId;
            const location = req.body.location;

            if (debug) log(`${ip} | /api/kb1/delKfByLocation | kbType: ${kbType}, kbId: ${kbId}, kfId: ${kfId}, bindId: ${bindId}, location: ${location}`);

            if (kbType === 'allm') {
                ctx.shortTimeCacheService.getAnythingLLMMapper().deleteWorkspaceRawTextByLocation(kbId, kfId, bindId, location, (r) => {
                    if (debug) log(`kb1 删除知识碎片-RawText 完毕`);
                    res.send({ success: true });
                }, (err) => {
                    if (debug) log(`kb1 删除知识碎片-RawText -> ${err.message}`);
                    res.send({ success: false, msg: err.message });
                });
            }
            else {
                res.send({ success: false, msg: `未能识别的kbType - ${kbType}` });
            }
        }
        catch (err) {
            log(err);
            res.send({ success: false, msg: `请求异常 -> ${err.message}` });
        }
    });
}