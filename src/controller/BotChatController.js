import TopController from "./TopController";

/**
 * 聊天接口
 * @param app
 * @param ctx
 */
export default class BotChatController extends TopController {

  constructor(ctx, app) {
    super(ctx);
    const self = this;
    const debug = true;

    /**
     * 聊天
     */
    app.post('/api/chat', async function (req, res) {
      try {
        const model = req.body.model;
        const messages = req.body.messages;

        self.logReq(req, '/api/chat');

        // 设置响应头
        res.set({
          'Content-Type': 'text/event-stream; charset=utf-8',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        });
        res.status(200);

        ctx.shortTimeCacheService.getChatModel().chat(model, messages, (r) => {
          res.send({success: true, data: r});
        }, (err) => {
          self.replyException(res, err, 'a');
        });
      } catch (err) {
        self.replyException(res, err);
      }
    });
    
    /**
     * 聊天（流式输出）
     */
    app.post('/api/chat-stream', async function (req, res) {
      try {
        const model = req.body.model;
        const messages = req.body.messages;

        self.logReq(req, '/api/chat-stream');

        // 设置响应头
        res.set({
          'Content-Type': 'text/event-stream; charset=utf-8',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        });
        res.status(200);

        ctx.shortTimeCacheService.getChatModel().chatStream(model, messages, (chunk) => {
          res.write(chunk);
        }, () => {
          res.end();
        }, (err) => {
          self.replyException(res, err, 'a');
        });
      } catch (err) {
        self.replyException(res, err);
      }
    });
  }

}