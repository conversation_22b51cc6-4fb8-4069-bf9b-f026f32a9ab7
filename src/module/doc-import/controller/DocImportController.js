import TopController from "../../../controller/TopController";
import {newCUIdA} from "../../../util/id-util";
import {TextinOcrMapper} from "../../../mapper/TextinOcrMapper";
import {toYearDateDirName} from "../../../util/time-util";

export default class DocImportController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;

        const { fs, path, multer, crypto } = ctx.imports;

        /**
         * 接受上传文档并转为md格式内容
         */
        {
            // 使用memoryStorage先解析所有字段，增加文件大小限制
            const upload = multer({
                storage: multer.memoryStorage(),
                limits: {
                    fileSize: 500 * 1024 * 1024, // 500MB限制
                    fieldSize: 1024 * 1024 // 1MB字段大小限制
                }
            });

            app.post('/api/doc-import/doc-to-md', (req, res, next) => {
                upload.single('file')(req, res, (err) => {
                    if (err) {
                        console.error('Multer错误:', err);
                        if (err.code === 'LIMIT_FILE_SIZE') {
                            return res.status(400).json({
                                success: false,
                                error: '文件大小超过限制(10MB)'
                            });
                        }
                        return res.status(400).json({
                            success: false,
                            error: '文件上传错误: ' + err.message
                        });
                    }
                    next();
                });
            }, async function (req, res) {
                const loginService = self.ctx.shortTimeCacheService.getLoginService()
                // 获取参数
                const token = req.body.token;
                const kbId = req.body.kbId;

                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/doc-import/doc-to-md', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)

                // 加入行为日志
                self.ctx.shortTimeCacheService.getUserActionLogService().push('doc-import', `${req.ip} ${dsl.user.realname} 上传文档并处理`);

                try {
                    log(`[di.doc-to-md] kbId: ${req.body.kbId} (${req.file.size} bytes) ...`)

                    // 检查文件是否上传成功
                    if (!req.file) {
                        return res.status(400).json({
                            success: false,
                            error: '没有接收到文件'
                        });
                    }

                    if (!kbId) {
                        return res.status(400).send('缺少必要参数: kbId');
                    }

                    const dirPath = `${ctx.rootPath}/data/doc-import/${kbId}/${toYearDateDirName(new Date())}`;
                    // 确定最终目标路径
                    const originalName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');
                    const docPath = path.join(dirPath, originalName);
                    const newMdPath = docPath.replace(path.extname(docPath), '.md');

                    // 确保目录存在
                    if (!fs.existsSync(dirPath)) {
                        fs.mkdirSync(dirPath, { recursive: true });
                    }

                    // 将文件从内存写入磁盘
                    log(`[di.doc-to-md] write file to ${docPath} ...`)

                    fs.writeFileSync(docPath, req.file.buffer);

                    /*** 转换文档 ***/
                    let markdown;
                    const textinOcrMapper = new TextinOcrMapper(ctx, {});

                    /*** 【正式】 ***/
                    if (fs.existsSync(newMdPath)) {
                        log(`[di.doc-to-md] read exist markdown file ... (${originalName})`)
                        markdown = fs.readFileSync(newMdPath, 'utf8');
                    }
                    else {
                        log(`[di.doc-to-md] build new markdown file ... (${originalName})`)
                        markdown = await textinOcrMapper
                            .convertDocToMd(docPath, {
                                attachment: {
                                    saveDirPath: `./public/data/doc-import/${kbId}/attachment/${toYearDateDirName(new Date())}`,
                                    replacementParentUrl: `/attachment-base-url/data/doc-import/${kbId}/attachment/${toYearDateDirName(new Date())}`,
                                }
                            });
                        fs.writeFileSync(newMdPath, markdown);
                    }

                    /*** 【调试】 ***/
                    // //使用已处理后文档
                    // let markdown = fs.readFileSync(`./data/doc-import/${kbId}/debug-proc.md`, 'utf8');
                    // // 下载附件到本地
                    // markdown = await textinOcrMapper.downloadAttachmentToLocalAndReplace(markdown,
                    //     `./public/data/doc-import/attachment/${toYearDateDirName(new Date())}`,
                    //     `/attachment-base-url/data/doc-import/attachment/${toYearDateDirName(new Date())}`)
                    // fs.writeFileSync(`./data/doc-import/${kbId}/debug-proc.md`, markdown);

                    res.json({
                        success: true,
                        data: {
                            markdown,
                        },
                    });

                } catch (error) {
                    console.error('文件上传处理错误:', error);
                    res.status(500).json({
                        success: false,
                        error: '文件上传处理失败: ' + error.message
                    });
                }
            });
        }
    }

}