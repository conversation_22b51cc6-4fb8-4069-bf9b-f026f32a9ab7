import DocImportController from "../controller/DocImportController";

export class DocImportService {
    constructor(ctx) {
        this.ctx = ctx;
    }

    start(pars) {
        const { app } = pars;

        new DocImportController(this.ctx, app);
    }

    // getKbList(dsl, sac, userId) {
    //     this._getDSS()
    // }

    _getDSS() {
        return this.ctx.shortTimeCacheService.getDataSourceService();
    }
}