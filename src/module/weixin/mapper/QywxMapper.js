import { reqGetSync, reqPostJsonSync } from "../../../util/http-util";
import axios from "axios";
/**
 * 企业微信Mapper
 */
export default class QywxMapper {
    constructor(ctx) {
        this.ctx = ctx;
        this.baseUrl = 'https://qyapi.weixin.qq.com';
    }

    /**
     * 发送应用消息
     * 参考：https://developer.work.weixin.qq.com/document/path/90236
     * @param {*} accessToken 
     * @param {*} message { touser, msgtype, agentid, content }
     * @returns {Promise<QywxSendAppMessageResult>}
     */
    async sendAppMessage(accessToken, message) {
        const url = `${this.baseUrl}/cgi-bin/message/send?access_token=${accessToken}`;
        const r = await reqPostJsonSync(url, message);
        if (r.errcode !== 0) log(`sendAppMessage返回结果：${JSON.stringify(r)}`);
        return r;
    }

    /**
     * 获取access_token
     * @param {*} corpId 
     * @param {*} corpSecret 
     * @returns {Promise<QywxGetAccessTokenResult>}
     */
    async getAccessToken(corpId, corpSecret) {
        const url = `${this.baseUrl}/cgi-bin/gettoken?corpid=${corpId}&corpsecret=${corpSecret}`;
        const r = await reqGetSync(url);
        if (r.errcode !== 0) log(`getAccessToken返回结果：${JSON.stringify(r)}`);
        return r;
    }

    /**
     * 下载媒体文件并返回ArrayBuffer
     * @param {*} accessToken 
     * @param {*} mediaId 
     * @returns {Promise<ArrayBuffer>}
     */
    async downloadMediaToBuffer(accessToken, mediaId) {
        const url = `${this.baseUrl}/cgi-bin/media/get?access_token=${accessToken}&media_id=${mediaId}`;
        try {
            const response = await axios({
                method: 'get',
                url: url,
                responseType: 'arraybuffer'
            });
            if (response.headers['error-msg'] != null && response.headers['error-msg'].length > 0) {
                throw new Error(`${response.headers['error-code']} -> ${response.headers['error-msg']}`);
                // log(`${response.headers['error-code']} -> ${response.headers['error-msg']}`);
            }
            // console.log(response.headers['error-code'])
            // console.log(response)
            return response.data;
        } catch (error) {
            console.error('下载媒体文件失败:', error);
            throw error;
        }
    }
}