/**
 * 
 * @typedef {Object} QywxResult
 * @property {Number} errcode - 出错返回码，为0表示成功，非0表示调用失败
 * @property {String} errmsg - 返回码提示语
 */

/**
 * 
 * @typedef {Object} QywxGetAccessTokenResult
 * @property {Number} errcode - 出错返回码，为0表示成功，非0表示调用失败
 * @property {String} errmsg - 返回码提示语
 * @property {String} access_token - 获取到的凭证，最长为512字节
 * @property {Number} expires_in - 凭证的有效时间（秒）
 */

/**
 * 
 * @typedef {Object} QywxSendAppMessageResult
 * @property {Number} errcode - 出错返回码，为0表示成功，非0表示调用失败
 * @property {String} errmsg - 返回码提示语
 * @property {String} invaliduser - 不合法的userid，多个用'|'分隔
 * @property {String} invalidparty - 不合法的partyid，多个用'|'分隔
 * @property {String} invalidtag - 不合法的tagid，多个用'|'分隔
 * @property {String} unlicenseduser - 没有基础接口许可的userid，多个用'|'分隔
 * @property {String} msgid - 消息id
 * @property {String} response_code - 仅消息类型为"按钮交互型"，返回用户操作按钮的response_code
 */