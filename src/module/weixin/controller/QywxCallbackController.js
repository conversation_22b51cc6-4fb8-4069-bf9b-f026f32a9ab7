import TopController from "../../../controller/TopController";
/**
 * 企业微信回调-API接受消息
 */
export default class QywxCallbackController extends TopController {
    constructor(ctx, app, cfg) {
        super(ctx);
        const self = this;

        self._debug = true;
        self.cfg = cfg;

        const { crypto } = ctx.imports;

        // let token = 'Rbiudq7FeJYaHeguhiu79Lu2'; // 企业微信中配置的Token
        // let encodingAESKey = '6sZP8ZfzXKaUh5nGTxlR1so7jyITg5OSoZz9o4ES6qk'; // 企业微信中配置的EncodingAESKey
        // let corpId = 'wx2403e0282a67d98d'; // 企业微信的CorpId
        // let secret = 'kr8a2K-VG8gismENnoS3CK1UUJ1w8Ax3mOrTb8vL-_c'; // 企业微信应用的Secret
        // let agentId = '1000031'; // 企业微信应用的AgentId

        const reqId = cfg.reqId;
        const token = cfg.token;
        const encodingAESKey = cfg.encodingAESKey;
        const corpId = cfg.corpId;
        const secret = cfg.secret;
        const agentId = cfg.agentId;

        // 解密函数 - 修改为正确处理企业微信消息的方式
        function decryptMsg(text, encodingAESKey) {
            // 添加=号补位
            const aesKey = Buffer.from(encodingAESKey + '=', 'base64');
            const iv = aesKey.slice(0, 16);
            const decipher = crypto.createDecipheriv('aes-256-cbc', aesKey, iv);
            decipher.setAutoPadding(false);
            
            let decrypted;
            try {
                decrypted = Buffer.concat([
                    decipher.update(Buffer.from(text, 'base64')),
                    decipher.final()
                ]);
            } catch (e) {
                console.error('解密失败:', e);
                return '';
            }
            
            // 去除补位字符
            let pad = decrypted[decrypted.length - 1];
            if (pad < 1 || pad > 32) {
                pad = 0;
            }
            decrypted = decrypted.slice(0, decrypted.length - pad);
            
            // 去除16位随机字符串、4字节网络字节序和corpid
            const content = decrypted.slice(16);
            const msgLen = content.readUInt32BE(0);
            const msg = content.slice(4, 4 + msgLen).toString('utf8');
            
            return msg;
        }

        // 计算签名
        function calculateSignature(token, timestamp, nonce, encrypt) {
            const array = [token, timestamp, nonce];
            if (encrypt) {
                array.push(encrypt);
            }
            array.sort(); // 字典排序
            const str = array.join(''); // 拼接
            const sha1 = crypto.createHash('sha1');
            sha1.update(str);
            return sha1.digest('hex');
        }

        /**
         * 企业微信-接收消息-验证消息
         */
        app.get(`/api/qywx/${reqId}/send-app-msg`, async function (req, res) {
            self.logReq(req, '/api/qywx/send-app-msg (GET)');
            try {
                const { msg_signature, timestamp, nonce, echostr } = req.query;

                log('【企业微信】接收参数：', JSON.stringify(req.query));

                // 验证签名
                const signature = calculateSignature(token, timestamp, nonce, echostr);

                if (signature === msg_signature) {
                    // 解密echostr
                    const decryptedEchostr = decryptMsg(echostr, encodingAESKey);
                    log('解密文本：', decryptedEchostr);
                    
                    // 直接返回解密后的明文，不要添加任何额外字符
                    res.send(decryptedEchostr);
                } else {
                    log('签名验证失败');
                    log('计算的签名:', signature);
                    log('接收的签名:', msg_signature);
                    res.status(403).send('Invalid signature');
                }
            } catch (exc) {
                console.error('验证URL异常:', exc);
                self.replyException(res, exc);
            }
        });
        
        /**
         * 企业微信-接收消息
         */
        app.post(`/api/qywx/${reqId}/send-app-msg`, async function (req, res) {
            self.logReq(req, '/api/qywx/send-app-msg (POST)');
            try {
                const { msg_signature, timestamp, nonce } = req.query;
                if (self._debug) log('【企业微信】接收参数：', JSON.stringify(req.query));
                
                let rawData = '';
                req.on('data', (chunk) => {
                    rawData += chunk;
                });

                await new Promise((resolve, reject) => {
                    req.on('end', () => {
                        resolve();
                    });
                    req.on('error', reject);
                });

                // log('接收到的原始XML:', rawData);
                
                // 解析XML数据
                const parseXml = (xml) => {
                    return new Promise((resolve, reject) => {
                        const { parseString } = require('xml2js');
                        parseString(xml, { explicitArray: false }, (err, result) => {
                            if (err) reject(err);
                            else resolve(result);
                        });
                    });
                };
                
                const xmlData = await parseXml(rawData);
                // log('解析后的XML数据:', JSON.stringify(xmlData));
                
                // 获取加密消息
                const encryptedMsg = xmlData?.xml?.Encrypt;
                
                if (!encryptedMsg) {
                    log('未找到加密消息');
                    return res.status(400).send('Missing encrypted message');
                }

                // 验证签名
                const signature = calculateSignature(token, timestamp, nonce, encryptedMsg);

                if (signature === msg_signature) {
                    // 解密消息
                    const decryptedMsg = decryptMsg(encryptedMsg, encodingAESKey);
                    // log('接收到的消息1:', decryptedMsg);
                    const msg = (await parseXml(decryptedMsg)).xml;
                    if (self._debug) log('消息内容:', JSON.stringify(msg))

                    // 处理消息
                    // const { ToUserName, FromUserName, CreateTime, MsgType, Content } = msg;
                    if (self.onReceiveMessage) {
                        await self.onReceiveMessage(msg, req, res);
                    }

                    res.json({
                        status: 'ok',
                    });
                } else {
                    log('签名验证失败');
                    log('计算的签名:', signature);
                    log('接收的签名:', msg_signature);
                    res.status(403).send('Invalid signature');
                }
            } catch (exc) {
                console.error('处理消息异常:', exc);
                self.replyException(res, exc);
            }
        });
    }

    async onReceiveMessage(msg, req, res) {
    }

    /**
     * 发送企业微信应用消息用来回复用户
     * @param {*} corpId 
     * @param {*} secret 
     * @param {*} agentid 
     * @param {*} touser 
     * @param {*} content 
     * @returns 
     */
    async sendAppMessageText(corpId, secret, agentid, touser, content) {
        const self = this;
        const result = await self.ctx.shortTimeCacheService.getQywxService().sendAppMessage(corpId, secret, {
            agentid: agentid,
            touser: touser,
            msgtype: 'text',
            text: {
                content: content
            }
        });
        return result;
    }
}