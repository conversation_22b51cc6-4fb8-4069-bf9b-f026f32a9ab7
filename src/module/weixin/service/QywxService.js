import QywxMapper from "../mapper/QywxMapper";


/**
 * 企业微信服务
 */
export default class QywxService {
    constructor(ctx) {
        this.ctx = ctx;
        this.qywxMapper = new QywxMapper(ctx);
    }

    /**
     * 发送应用消息
     * @param {*} corpId 
     * @param {*} corpSecret 
     * @param {*} textContent 
     * @param {*} agentid 
     * @param {*} touser 
     * @returns 
     */
    async sendAppMessageText(corpId, corpSecret, textContent, agentid, touser) {
        const r = await this.qywxMapper.getAccessToken(corpId, corpSecret);
        if (r.errcode !== 0) {
            throw new Error(r.errmsg);
        }
        return this.qywxMapper.sendAppMessage(r.access_token, {
            agentid: agentid,
            touser: touser,
            msgtype: 'text',
            text: {
                content: textContent
            }
        });
    }

    /**
     * 发送应用消息
     * @param {*} corpId 
     * @param {*} corpSecret 
     * @param {*} message 
     * @returns {Promise<QywxSendAppMessageResult>}
     */
    async sendAppMessage(corpId, corpSecret, message) {
        const r = await this.qywxMapper.getAccessToken(corpId, corpSecret);
        if (r.errcode !== 0) {
            throw new Error(r.errmsg);
        }
        return this.qywxMapper.sendAppMessage(r.access_token, message);
    }

    /**
     * 下载媒体文件并转换从amr转为wav
     * @param {*} corpId 
     * @param {*} corpSecret 
     * @param {*} mediaId 
     * @returns {Promise<ArrayBuffer>}
     */
    async downloadMediaToBuffer(corpId, corpSecret, mediaId) {
        const r = await this.qywxMapper.getAccessToken(corpId, corpSecret);
        if (r.errcode !== 0) {
            throw new Error(r.errmsg);
        }
        return this.qywxMapper.downloadMediaToBuffer(r.access_token, mediaId);
    }
}
