import CSAUI6JTDataSourceMapper from "../../cs-data-source/mapper/CSAUI6JTDataSourceMapper";

export class ProjectService {

    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     *
     * @param dsl
     * @param params
     */
    async getProjects(dsl, params) {
        const list = await this._getDSS().getExtAppDataList(dsl, {
            appKey: 'cac_cwsc',
            noSelfLimit: true,
            values: {
                user_id: dsl.user.id,
            }
        });
        // log(JSON.stringify(list))

        return list.map((n) => {
            const sObj = JSON.parse(n.search);
            return {
                id: n.id,
                name: sObj.name,
            };
        });
    }

    /**
     *
     * @param dsl
     * @param params
     * @returns {Promise<void>}
     */
    async getProjectDetails(dsl, params) {
        const form = await this._getDSS().getExtAppDataForm(dsl, {
            appKey: 'cac_cwsc',
            id: params.id,
        });

        // log(JSON.stringify(form))
        const cObj = JSON.parse(form.content);
        return {
            id: form.id,
            ...cObj,
        };
    }

    /**
     *
     * @param dsl
     * @param params { updType: 1, form { id, name } }
     * @returns {Promise<void>}
     */
    async saveProject(dsl, params) {
        const searchObj = {
            name: params.form.name,
            article_type: params.form.article_type,
            user_id: dsl.user.id,
            user: dsl.user.realname,
        }

        const newForm = {
            id: params.id,
            search: JSON.stringify(searchObj),
            content: JSON.stringify({
                ...searchObj,
            }),
            appKey: 'cac_cwsc',
        };
        params.form = newForm;

        const data = await this._getDSS().saveExtAppDataForm(dsl, params);
        return data;
    }

    /**
     *
     * @param dsl
     * @param params id, fields
     * @returns {Promise<void>}
     */
    async saveProjectFields(dsl, params) {
        const searchFields = {};
        const contentFields = {};

        for (const prop in params.fields) {
            if (prop === 'name' || prop === 'article_type') {
                searchFields[prop] = params.fields[prop];
            }
            contentFields[prop] = params.fields[prop];
        }

        const data = await this._getDSS().saveExtAppDataPartFields(dsl, {
            id: params.id,
            appKey: 'cac_cwsc',
            searchFields, contentFields,
        });
        return data;
    }

    /**
     *
     * @param dsl
     * @param params id
     */
    async deleteProject(dsl, params) {
        await this._getDSS().deleteExtAppDataForm(dsl, {
            appKey: 'cac_cwsc',
            id: params.id,
        })
    }

    _getDSS() {
        return this.ctx.shortTimeCacheService.getDataSourceService();
    }
}