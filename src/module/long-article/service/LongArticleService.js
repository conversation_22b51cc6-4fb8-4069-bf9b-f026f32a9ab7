import LongArticleProjectController from "../controller/LongArticleProjectController";
import {LongArticleManager} from "../model/LongArticleManager";
import {LongArticleManagerController} from "../controller/LongArticleManagerController";
import {LongArticleReferenceController} from "../controller/LongArticleReferenceController";

/**
 * 长文生成
 */
export class LongArticleService {

    constructor(ctx) {
        this.ctx = ctx;
    }

    start(pars) {
        const { app } = pars;

        new LongArticleProjectController(this.ctx, app);
        new LongArticleManagerController(this.ctx, app);
        new LongArticleReferenceController(this.ctx, app);

        this.longArticleManager = new LongArticleManager(this.ctx, this);
    }
}