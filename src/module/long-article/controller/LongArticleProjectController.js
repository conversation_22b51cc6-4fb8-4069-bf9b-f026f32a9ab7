import TopController from "../../../controller/TopController";
import {ProjectService} from "../service/ProjectService";

export default class LongArticleProjectController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;

        /**
         * @param req.body.params
         */
        app.post('/api/la/getProjects', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const projectService = new ProjectService(ctx);

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/getProjects', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await projectService.getProjects(dsl, req.body.params)
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params id
         */
        app.post('/api/la/getProjectDetails', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const projectService = new ProjectService(ctx);

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/getProjectDetails', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await projectService.getProjectDetails(dsl, req.body.params)
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params
         */
        app.post('/api/la/saveProject', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const projectService = new ProjectService(ctx);

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/saveProject', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await projectService.saveProject(dsl, req.body.params)
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params
         */
        app.post('/api/la/saveProjectFields', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const projectService = new ProjectService(ctx);

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/saveProjectFields', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await projectService.saveProjectFields(dsl, req.body.params)
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params
         */
        app.post('/api/la/deleteProject', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const projectService = new ProjectService(ctx);

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/deleteProject', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await projectService.deleteProject(dsl, req.body.params)
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });
    }
}