import TopController from "../../../controller/TopController";
import {ProjectService} from "../service/ProjectService";
import {LongArticleStorage} from "../mapper/LongArticleStorage";

export class LongArticleManagerController extends TopController {

    constructor(ctx, app) {
        super(ctx);
        const self = this;

        /**
         * 分配任务
         * @param req.body.params type, model, project_id, project_req, outline_req
         */
        app.post('/api/la/assignTask', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const longArticleManager = ctx.longArticleService.longArticleManager;

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/assignTask', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await longArticleManager.assignTask({
                    ...req.body.params,
                    sac: dsl.user.sac,
                    user_id: dsl.user.id,
                    user: dsl.user.realname,
                }, {
                    onData(str) {
                    }
                });
                res.send({success: true, data: r})
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * 监控任务状态
         * @param req.body project_id
         */
        app.post('/api/la/hookTaskState', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const longArticleManager = ctx.longArticleService.longArticleManager;

            const project_id = req.body.project_id;

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/la/hookTaskState', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                res.set({
                    'Content-Type': 'text/event-stream; charset=utf-8',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                });
                res.status(200);
                // ...
                log(`[lamc] hookTaskState start ...`)
                await longArticleManager.hookTaskState({
                    user_id: dsl.user.id,
                    project_id: project_id
                }, (str) => {
                    res.write(str);
                });
                log(`[lamc] hookTaskState end`)
                res.end();
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         * 取消任务状态监控
         * @param req.body
         */
        app.post('/api/la/cancelUserHookTaskState', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const longArticleManager = ctx.longArticleService.longArticleManager;

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/cancelUserHookTaskState', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await longArticleManager.cancelUserHookTaskState({
                    user_id: dsl.user.id,
                });
                res.send({success: true, data: r})
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * 获取任务结果
         * @param req.body.params project_id
         */
        app.post('/api/la/getTaskResult', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const longArticleManager = ctx.longArticleService.longArticleManager;

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/getTaskResult', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await longArticleManager.getTaskResult({
                    user_id: dsl.user.id,
                    project_id: req.body.params.project_id,
                });
                res.send({success: true, data: r})
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * 修改大纲
         * @param req.body.params project_id, outline
         */
        app.post('/api/la/saveOutline', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const longArticleStorage = new LongArticleStorage(ctx);

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/saveOutline', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await longArticleStorage.saveOutline(dsl.user.id, req.body.params.project_id, req.body.params.outline);
                res.send({success: true, data: r})
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * 加载最新长文
         * @param req.body.params project_id
         */
        app.post('/api/la/loadLatestLongArticle', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const longArticleStorage = new LongArticleStorage(ctx);

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/loadLatestLongArticle', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await longArticleStorage.loadLatestLongArticle(dsl.user.id, req.body.params.project_id);
                res.send({success: true, data: r})
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * 下载最新长文
         * @param req.query.project_id
         */
        app.get('/api/la/loadLatestLongArticleToWord', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const longArticleStorage = new LongArticleStorage(ctx);

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/loadLatestLongArticleToWord', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)

                // 获取Word文件路径
                const { project_id } = req.query;
                const wordFilePath = await longArticleStorage.loadLatestLongArticleToWord(dsl.user.id, project_id);

                if (!wordFilePath) {
                    return res.status(404).send({success: false, message: '文件不存在'});
                }

                // 读取文件内容
                const { fs, path } = ctx.imports;
                const fileName = path.basename(wordFilePath).replace('article_', '生成的长文-');
                const fileContent = fs.readFileSync(wordFilePath);

                // 设置下载响应头
                res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
                // 对中文文件名进行URL编码
                const encodedFileName = encodeURIComponent(fileName);
                res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFileName}`);
                res.send(fileContent);

            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });


    }
}