import TopController from "../../../controller/TopController";
import {toYearDateDirName} from "../../../util/time-util";
import {TextinOcrMapper} from "../../../mapper/TextinOcrMapper";
import {ProjectService} from "../service/ProjectService";
import {LongArticleStorage} from "../mapper/LongArticleStorage";

/**
 * 长文参考资料
 */
export class LongArticleReferenceController extends TopController {

    constructor(ctx, app) {
        super(ctx);
        const self = this;

        const {fs, path, multer, crypto, mammoth} = ctx.imports;

        /**
         * 接受上传文档并转为md格式内容
         * @param req.body.token
         * @param req.body.projectId
         * @param req.body.understandingType
         * @param req.body.file
         * @param req.body.originalName
         */
        {
            // 使用memoryStorage先解析所有字段，增加文件大小限制
            const upload = multer({
                storage: multer.memoryStorage(),
                limits: {
                    fileSize: 100 * 1024 * 1024, // 100MB限制
                    fieldSize: 1024 * 1024 // 1MB字段大小限制
                }
            });

            app.post('/api/la/ref-doc-to-md', (req, res, next) => {
                upload.single('file')(req, res, (err) => {
                    if (err) {
                        console.error('Multer错误:', err);
                        if (err.code === 'LIMIT_FILE_SIZE') {
                            return res.status(400).json({
                                success: false,
                                error: '文件大小超过限制(10MB)'
                            });
                        }
                        return res.status(400).json({
                            success: false,
                            error: '文件上传错误: ' + err.message
                        });
                    }
                    next();
                });
            }, async function (req, res) {
                const loginService = self.ctx.shortTimeCacheService.getLoginService()
                // 获取参数
                const token = req.body.token;
                const projectId = req.body.projectId;
                const understandingType = req.body.understandingType;

                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/ref-doc-to-md', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)

                // 加入行为日志
                // self.ctx.shortTimeCacheService.getUserActionLogService().push('doc-import', `${req.ip} ${dsl.user.realname} 上传文档并处理`);

                try {
                    // 检查文件是否上传成功
                    if (!req.file) {
                        return res.status(400).json({
                            success: false,
                            error: '没有接收到文件'
                        });
                    }

                    if (!projectId) {
                        return res.status(400).send('缺少必要参数: projectId');
                    }

                    const dirPath = `${ctx.rootPath}/data/long-article/${dsl.user.id}/${projectId}/refs`;
                    // 确定最终目标路径
                    const originalName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');
                    const docPath = path.join(dirPath, originalName);
                    const newMdPath = docPath.replace(path.extname(docPath), '.md');

                    // 确保目录存在
                    if (!fs.existsSync(dirPath)) {
                        fs.mkdirSync(dirPath, {recursive: true});
                    }

                    // 将源文件从内存写入磁盘
                    fs.writeFileSync(docPath, req.file.buffer);

                    // 理解文档并存储
                    // 纯文本理解
                    if (understandingType === '0') {
                        if (originalName.endsWith(".docx")) {
                            await new Promise((resolve, reject) => {
                                mammoth.convertToMarkdown({path: docPath})
                                    .then(function (result) {
                                        var markdown = result.value;
                                        // 清理图片
                                        markdown = self.clearImageInMarkdown(markdown)
                                        fs.writeFileSync(newMdPath, markdown);
                                        log(`理解文件“${originalName}”完毕，生成摘要“${newMdPath}”。`);

                                        resolve();
                                    })
                                    .catch(function (error) {
                                        console.error(error);

                                        reject(error);
                                    });
                            });
                        } else if (originalName.endsWith(".md") || originalName.endsWith(".txt")) {
                            fs.writeFileSync(newMdPath, req.file.buffer);
                            log(`理解文件“${originalName}”完毕，生成摘要“${newMdPath}”。`);
                        } else {
                            console.error(`暂不支持此格式的纯文本理解 -> ${originalName}`);
                            res.status(500).json({
                                success: false,
                                msg: `暂不支持此格式的纯文本理解 -> ${originalName}`
                            });
                            return;
                        }
                    }
                    // 纯图片理解
                    else if (understandingType === '1') {
                        if (originalName.endsWith(".png") || originalName.endsWith(".jpg") || originalName.endsWith(".jpeg")) {
                            const model = 'zhipu_glm-4v-plus-0111';
                            const dataUrl = self.convertBufferToDataURL(req.file.buffer, originalName);
                            const messages = [];
                            messages.push({
                                role: 'system', content: `
                                # 任务说明
                                - 把你看到的图片用文字的形式描述下生成摘要返回给我
                                - 不要有任何解释，只要返回内容就行
                                `
                            })

                            // log(`调试：${JSON.stringify(messages)}`)
                            // log(`调试：${JSON.stringify([{
                            //     name: originalName,
                            //     type: 'image',
                            //     pushType: 'b64',
                            //     content: dataUrl,
                            // }])}`)

                            const r = await self.ctx.shortTimeCacheService.getChatModel().chatSync(model, messages, {
                                attachments: [{
                                    name: originalName,
                                    type: 'image',
                                    pushType: 'b64',
                                    content: dataUrl,
                                }]
                            });
                            fs.writeFileSync(newMdPath, `（下面是此图片内容的文字描述）\n${r.reply}`);
                            log(`理解文件“${originalName}”完毕，生成摘要“${newMdPath}”。`);
                        } else {
                            console.error(`暂不支持此格式的纯图片理解 -> ${originalName}`);
                            res.status(500).json({
                                success: false,
                                msg: `暂不支持此格式的纯图片理解 -> ${originalName}`
                            });
                            return;
                        }
                    } else {
                        console.error(`不支持的理解类型 -> ${understandingType}`);
                        res.status(500).json({
                            success: false,
                            msg: `不支持的理解类型 -> ${understandingType}`
                        });
                        return;
                    }

                    res.json({
                        success: true,
                    });

                } catch (error) {
                    console.error('文件上传处理错误:', error);
                    res.status(500).json({
                        success: false,
                        msg: '文件上传处理失败: ' + error.message
                    });
                }
            });
        }


        /**
         * @param req.body.params project_id
         */
        app.post('/api/la/loadReferenceList', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const longArticleStorage = new LongArticleStorage(ctx);

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/loadReferenceList', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await longArticleStorage.loadReferenceList(dsl.user.id, req.body.params.project_id)
                res.send({success: true, data: r})
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params project_id, name
         */
        app.post('/api/la/deleteReference', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const longArticleStorage = new LongArticleStorage(ctx);
            const {project_id, name} = req.body.params;

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/la/deleteReference', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await longArticleStorage.deleteReference(dsl.user.id, project_id, name)
                res.send({success: true, data: r})
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });
    }

    /**
     * 把文件内容转为DataURL格式
     * @param buffer {Buffer} 文件内容
     * @param fileName {String} 文件名
     * @returns {String}
     */
    convertBufferToDataURL(buffer, fileName) {
        if (!buffer || !Buffer.isBuffer(buffer)) {
            throw new Error('参数必须是有效的Buffer对象');
        }

        // 根据文件扩展名确定MIME类型
        const extension = fileName.split('.').pop().toLowerCase();
        let mimeType;

        switch (extension) {
            case 'jpg':
            case 'jpeg':
                mimeType = 'image/jpeg';
                break;
            case 'png':
                mimeType = 'image/png';
                break;
            case 'gif':
                mimeType = 'image/gif';
                break;
            case 'pdf':
                mimeType = 'application/pdf';
                break;
            case 'docx':
                mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                break;
            case 'xlsx':
                mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                break;
            case 'txt':
                mimeType = 'text/plain';
                break;
            case 'md':
                mimeType = 'text/markdown';
                break;
            default:
                mimeType = 'application/octet-stream';
        }

        // 将Buffer转换为Base64字符串
        const base64String = buffer.toString('base64');

        // 返回DataURL格式
        return `data:${mimeType};base64,${base64String}`;
    }

    /**
     * 清理markdown中的base64图片
     * @param {string} markdownContent markdown内容
     * @returns {string} 清理后的markdown内容
     */
    clearImageInMarkdown(markdownContent) {
        if (!markdownContent || typeof markdownContent !== 'string') {
            return markdownContent;
        }

        // 移除包含base64数据的图片标记
        // 匹配格式: ![alt text](data:image/...;base64,...)
        const base64ImageRegex = /!\[.*?\]\(data:image\/[^;]+;base64,[^)]+\)/g;

        // 移除这些图片标记
        let cleanedContent = markdownContent.replace(base64ImageRegex, '');

        // 同时移除可能的多余空行
        cleanedContent = cleanedContent.replace(/\n{3,}/g, '\n\n');

        return cleanedContent.trim();
    }
}