import {convertMarkdownToWord} from "../../../util/markdown-word-util";

export class LongArticleStorage {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async saveOutline(user_id, project_id, outline = '') {
        const { fs, path } = this.ctx.imports;

        outline = outline.replace(/<think>[^<]*<\/think>[\n\r\s]*/g, '');

        let filePath = this.ctx.rootPath + `/data/long-article/${user_id}/${project_id}/outline.md`;

        if (!fs.existsSync(filePath)) {
            const dirPath = path.dirname(filePath);
            fs.mkdirSync(dirPath, {recursive: true});
        }

        fs.writeFileSync(filePath, outline);
    }

    async loadOutline(user_id, project_id) {
        const { fs, path } = this.ctx.imports;
        let filePath = this.ctx.rootPath + `/data/long-article/${user_id}/${project_id}/outline.md`;

        if (fs.existsSync(filePath)) {
            return fs.readFileSync(filePath, 'utf8');
        }

        return '';
    }

    /**
     * 加载最新长文
     * 名称样例：article_xxx.md
     * xxx表示生成时间，加载目录中最新生成的
     * @param user_id
     * @param project_id
     * @param opts buildWord: false
     * @return {Promise<void>}
     */
    async loadLatestLongArticle(user_id, project_id, opts = {}) {
        const { fs, path } = this.ctx.imports;

        let dirPath = this.ctx.rootPath + `/data/long-article/${user_id}/${project_id}`;

        if (fs.existsSync(dirPath)) {
            // 读取目录中的所有文件
            const files = fs.readdirSync(dirPath);

            // 过滤出符合 article_xxx.md 格式的文件
            const articleFiles = files.filter(file => /^article_\d+\.md$/.test(file));

            if (articleFiles.length === 0) {
                return null;
            }

            // 提取时间戳并排序，找到最新的文件
            const latestFile = articleFiles
                .map(file => {
                    const timestamp = file.match(/^article_(\d+)\.md$/)[1];
                    return { file, timestamp: parseInt(timestamp) };
                })
                .sort((a, b) => b.timestamp - a.timestamp)[0];

            // 读取最新文件的内容
            const filePath = path.join(dirPath, latestFile.file);
            const markdownContent = fs.readFileSync(filePath, 'utf8');

            // 生成word文档
            if (opts.buildWord) {
                let name = path.basename(filePath, path.extname(filePath));
                const wordFilePath = path.join(dirPath, `${name}.doc`);
                const wordContent = await convertMarkdownToWord(markdownContent);
                fs.writeFileSync(wordFilePath, wordContent);
                return wordFilePath;
            }

            return markdownContent;
        }

        return null;
    }

    /**
     * 获取最新长文并生成word格式文件
     * @param user_id
     * @param project_id
     * @return {Promise<String>} 返回word文件路径
     */
    async loadLatestLongArticleToWord(user_id, project_id) {
        const wordFilePath = await this.loadLatestLongArticle(user_id, project_id, {
            buildWord: true,
        });
        return wordFilePath;
    }

    async saveArticle(user_id, project_id, key, content) {
        const { fs, path } = this.ctx.imports;

        content = content.replace(/<think>[^<]*<\/think>[\n\r\s]*/g, '');

        let filePath = this.ctx.rootPath + `/data/long-article/${user_id}/${project_id}/article_${key}.md`;

        if (!fs.existsSync(filePath)) {
            const dirPath = path.dirname(filePath);
            fs.mkdirSync(dirPath, {recursive: true});
        }

        fs.writeFileSync(filePath, content);
    }

    async appendArticle(user_id, project_id, key, content) {
        const { fs, path } = this.ctx.imports;

        content = content.replace(/<think>[^<]*<\/think>[\n\r\s]*/g, '');

        let filePath = this.ctx.rootPath + `/data/long-article/${user_id}/${project_id}/article_${key}.md`;

        if (!fs.existsSync(filePath)) {
            const dirPath = path.dirname(filePath);
            fs.mkdirSync(dirPath, {recursive: true});
        }

        fs.appendFileSync(filePath, content);
    }

    /**
     * 加载参考资料
     * @param user_id
     * @param project_id
     * @param opts
     * @return {Promise<void>}
     */
    async loadReferenceList(user_id, project_id, opts = {}) {
        const { fs, path } = this.ctx.imports;
        let dirPath = this.ctx.rootPath + `/data/long-article/${user_id}/${project_id}/refs`;
        let list = [];

        if (fs.existsSync(dirPath)) {
            const files = fs.readdirSync(dirPath);
            const mdFiles = [];
            const otherFiles = [];

            for (const file of files) {
                if (file.endsWith(".md")) {
                    mdFiles.push(file);
                }
                else {
                    otherFiles.push(file);
                }
            }

            for (const mdFileName of mdFiles) {
                let rawFileName = mdFileName;

                for (const otherFileName of otherFiles) {
                    if (otherFileName.startsWith(mdFileName.substring(0, mdFileName.length - 3))) {
                        rawFileName = otherFileName;
                        break;
                    }
                }

                let content;
                if (opts.loadContent) {
                    let mdFilePath = path.join(dirPath, mdFileName);
                    content = fs.readFileSync(mdFilePath, 'utf8');
                }

                list.push({
                    name: rawFileName,
                    content,
                    state: 1,
                });
            }
        }

        return list;
    }

    /**
     *
     * @param user_id
     * @param project_id
     * @param name
     * @return {Promise<void>}
     */
    async deleteReference(user_id, project_id, name) {
        const { fs, path } = this.ctx.imports;
        let dirPath = this.ctx.rootPath + `/data/long-article/${user_id}/${project_id}/refs`;

        if (fs.existsSync(dirPath)) {
            const files = fs.readdirSync(dirPath);

            for (const file of files) {
                if (file === name) {
                    fs.unlinkSync(path.join(dirPath, name))
                    const ext = path.extname(name);
                    if (ext !== '.md') {
                        fs.unlinkSync(path.join(dirPath, name.replace(ext, '.md')))
                    }
                    break;
                }
            }
        }
    }
}