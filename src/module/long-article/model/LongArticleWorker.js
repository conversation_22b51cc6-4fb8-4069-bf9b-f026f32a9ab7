import {newCUIdA} from "../../../util/id-util";
import {LongArticleAgent} from "./LongArticleAgent";
import {LongArticleStorage} from "../mapper/LongArticleStorage";
import {toDateTimeStr} from "../../../util/time-util";

/**
 * 长文生成工作者
 */
export class LongArticleWorker {

    constructor(ctx) {
        this.ctx = ctx;
        this._longArticleStorage = new LongArticleStorage(ctx);
        this._agent = new LongArticleAgent(ctx);

        this._lastActivityTime = Date.now();
        this._stateInfo = {
            // 状态键：PENDING 待生成、GENERATING-OUTLINE 生成大纲中、GENERATING-ARTICLE 生成长文中、FAILED 已失败
            // GENERATE-OUTLINE-END 大纲生成完毕、ALL-DONE 生成完毕
            // CANCEL-HOOK 取消监控
            key: 'PENDING',
        };
        this._taskInfo;

        this._hookTaskStateList = [];
    }

    /**
     * 收到任务
     * @param taskInfo 任务信息 type, user_id, user, model, project_id, project_req 项目需求, outline_req 大纲要求, refs 参考资料
     * @param stateInfo 状态信息
     */
    async receiveTask(taskInfo, opts = {}) {
        const self = this;
        log(`[law] receiveTask ...`);

        if (self._stateInfo.key === 'PENDING' || self._stateInfo.key === 'ALL-DONE') {
        }
        else {
            throw new Error(`任务正在进行中，不可开启新任务（当前状态：${self._stateInfo.key}）`)
        }

        self.cancelHookTaskState();
        self._lastActivityTime = Date.now();
        self._taskInfo = taskInfo;
        // 只生成大纲
        if (taskInfo.type === 'only-outline') {
            log(`[law] build only outline ...`);
            (async function () {
                self._stateInfo.key = 'GENERATING-OUTLINE';
                if (opts.onData) opts.onData(`[LA][STATE]${self._stateInfo.key}`);
                self._procHookTaskStateList(`[LA][STATE]${self._stateInfo.key}`);

                self._agent.buildOutline({
                    ...taskInfo,
                }, {
                    onData(str) {
                        self._lastActivityTime = Date.now();

                        if (opts.onData) opts.onData(str);
                        self._procHookTaskStateList(str);
                    },
                    onDone(str, onDoneOpts = {}) {
                        self._lastActivityTime = Date.now();

                        self._stateInfo.key = 'ALL-DONE'
                        if (opts.onData) opts.onData(`[LA][STATE]${self._stateInfo.key}`);
                        self._procHookTaskStateList(`[LA][STATE]${self._stateInfo.key}`);

                        self._longArticleStorage.saveOutline(taskInfo.user_id, taskInfo.project_id, str);

                        if (opts.onDone) {
                            opts.onDone(str);
                        }

                        // 记录使用量
                        self.ctx.shortTimeCacheService.getUsageLogService().push('long-article', {
                            use: '生成大纲',
                            sac: taskInfo.sac,
                            user_id: taskInfo.user_id,
                            user: taskInfo.user,
                            input_words: onDoneOpts.inputWords,
                            output_words: onDoneOpts.outputWords,
                            model: taskInfo.model,
                        })
                    },
                    onError(err) {
                        self._stateInfo.key = 'FAILED';
                    }
                });
            })();
        }
        // 只生成长文
        else if (taskInfo.type === 'only-article') {
            log(`[law] build only article ...`);
            (async function () {
                self._stateInfo.key = 'GENERATING-ARTICLE';
                if (opts.onData) opts.onData(`[LA][STATE]${self._stateInfo.key}`);
                self._procHookTaskStateList(`[LA][STATE]${self._stateInfo.key}`);

                let outline = await self._longArticleStorage.loadOutline(taskInfo.user_id, taskInfo.project_id);
                let key = toDateTimeStr(new Date()).replace(/[-]/g, '')
                    .replace(/\s+/g, '').replace(/[:]/g, '');

                self._agent.buildArticle({
                    ...taskInfo,
                    outline
                }, {
                    onData(str) {
                        self._lastActivityTime = Date.now();

                        if (opts.onData) opts.onData(str);
                        self._procHookTaskStateList(str);
                    },
                    onDone(str, onDoneOpts = {}) {
                        self._lastActivityTime = Date.now();

                        self._stateInfo.key = 'ALL-DONE'
                        if (opts.onData) opts.onData(`[LA][STATE]${self._stateInfo.key}`);
                        self._procHookTaskStateList(`[LA][STATE]${self._stateInfo.key}`);

                        self._longArticleStorage.saveArticle(taskInfo.user_id, taskInfo.project_id, key, str);

                        if (opts.onDone) {
                            opts.onDone(str);
                        }

                        // 记录使用量
                        self.ctx.shortTimeCacheService.getUsageLogService().push('long-article', {
                            use: '生成长文',
                            sac: taskInfo.sac,
                            user_id: taskInfo.user_id,
                            user: taskInfo.user,
                            input_words: onDoneOpts.inputWords,
                            output_words: onDoneOpts.outputWords,
                            model: taskInfo.model,
                        })
                    },
                    onError(err) {
                        self._stateInfo.key = 'FAILED';
                    }
                });
            })();
        }
        // 完整生成
        else {
            log(`[law] build outline and article ...`);

            (async function () {
                self._stateInfo.key = 'GENERATING-OUTLINE';
                if (opts.onData) opts.onData(`[LA][STATE]${self._stateInfo.key}`);
                self._procHookTaskStateList(`[LA][STATE]${self._stateInfo.key}`);

                // await new Promise((resolve) => {
                //     setTimeout(() => {
                //         resolve();
                //     }, 500);
                // })

                let outline;
                let key = toDateTimeStr(new Date()).replace(/[-]/g, '')
                    .replace(/\s+/g, '').replace(/[:]/g, '');

                await new Promise((resolve) => {
                    self._agent.buildOutline({
                        ...taskInfo,
                    }, {
                        onData(str) {
                            self._lastActivityTime = Date.now();

                            if (opts.onData) opts.onData(str);
                            self._procHookTaskStateList(str);
                        },
                        onDone(str, onDoneOpts = {}) {
                            self._lastActivityTime = Date.now();
                            self._longArticleStorage.saveOutline(taskInfo.user_id, taskInfo.project_id, str);

                            outline = str;

                            // 记录使用量
                            self.ctx.shortTimeCacheService.getUsageLogService().push('long-article', {
                                use: '生成大纲',
                                sac: taskInfo.sac,
                                user_id: taskInfo.user_id,
                                user: taskInfo.user,
                                input_words: onDoneOpts.inputWords,
                                output_words: onDoneOpts.outputWords,
                                model: taskInfo.model,
                            })

                            resolve();
                        },
                        onError(err) {
                            self._stateInfo.key = 'FAILED';
                        }
                    });
                });

                // await new Promise((resolve) => {
                //     setTimeout(() => {
                //         resolve();
                //     }, 500);
                // })

                self._stateInfo.key = 'GENERATING-ARTICLE';
                if (opts.onData) opts.onData(`[LA][STATE]${self._stateInfo.key}`);
                self._procHookTaskStateList(`[LA][STATE]${self._stateInfo.key}`);

                // await new Promise((resolve) => {
                //     setTimeout(() => {
                //         resolve();
                //     }, 500);
                // })

                self._agent.buildArticle({
                    ...taskInfo,
                    outline,
                }, {
                    onData(str) {
                        self._lastActivityTime = Date.now();

                        if (opts.onData) opts.onData(str);
                        self._procHookTaskStateList(str);
                    },
                    onDone(str, onDoneOpts = {}) {
                        self._lastActivityTime = Date.now();
                        self._longArticleStorage.saveArticle(taskInfo.user_id, taskInfo.project_id, key, str);

                        self._stateInfo.key = 'ALL-DONE'
                        if (opts.onData) opts.onData(`[LA][STATE]${self._stateInfo.key}`);
                        self._procHookTaskStateList(`[LA][STATE]${self._stateInfo.key}`);

                        if (opts.onDone) {
                            opts.onDone(str);
                        }

                        // 记录使用量
                        self.ctx.shortTimeCacheService.getUsageLogService().push('long-article', {
                            use: '生成长文',
                            sac: taskInfo.sac,
                            user_id: taskInfo.user_id,
                            user: taskInfo.user,
                            input_words: onDoneOpts.inputWords,
                            output_words: onDoneOpts.outputWords,
                            model: taskInfo.model,
                        })
                    },
                    onError(err) {
                        self._stateInfo.key = 'FAILED';
                    }
                });
            })();

        }
    }

    /**
     *
     * @param onData
     * @return {Promise<void>}
     */
    async hookTaskState(onData) {
        const self = this;
        log(`[law] hookTaskState ...`);

        if (self._stateInfo.key === 'PENDING' || self._stateInfo.key === 'ALL-DONE') {
            onData(`[LA][STATE]${self._stateInfo.key}`);
        }
        else {
            self._hookTaskStateList.push({
                onData
            });
            self._procHookTaskStateList(`[LA][STATE]${self._stateInfo.key}`);
        }
    }

    async cancelHookTaskState() {
        for (const item of this._hookTaskStateList) {
            if (item && item.onData) {
                try {
                    item.onData('[LA][STATE]CANCEL-HOOK');
                } catch (exc) {
                }
            }
        }

        this._hookTaskStateList.splice(0);
    }

    _procHookTaskStateList(str) {
        for (const item of this._hookTaskStateList) {
            if (item && item.onData) {
                item.onData(str);
            }
        }
    }

    // _calRefsWords(refs) {
    //     let words = 0;
    //
    //     if (refs && refs.length > 0) {
    //         for (const ref of refs) {
    //             words += ref.content.length;
    //         }
    //     }
    //
    //     return words;
    // }
}