import {LongArticleWorker} from "./LongArticleWorker";
import {newCUIdA} from "../../../util/id-util";
import {LongArticleStorage} from "../mapper/LongArticleStorage";

/**
 * 长文生成经理
 */
export class LongArticleManager {

    constructor(ctx, parent) {
        this.ctx = ctx;
        this.parent = parent;

        /**
         *
         * @type {{[key: String]: LongArticleWorker}}
         * @private
         */
        this._workers = {};
    }

    /**
     * 分配任务
     * @param task type, sac, user_id, user, model, project_id, project_req 项目需求, outline_req 大纲要求
     * @param task.type 任务类型：only-outline 只生成大纲，only-article 只生成长文
     * @return {String} 任务ID
     */
    async assignTask(task, opts = {}) {
        const self = this;
        const longArticleStorage = new LongArticleStorage(this.ctx);

        let key = `${task.user_id}|${task.project_id}`;
        let worker = this._workers[key];
        // 检查客户是否有对应的工作者，没有就创建一个
        if (worker == null) {
            worker = new LongArticleWorker(this.ctx);
            this._workers[key] = worker;
        }
        // 创建任务ID
        if (task.id == null) task.id = newCUIdA();
        // 加载参考资料
        const refs = await longArticleStorage.loadReferenceList(task.user_id, task.project_id,
            { loadContent: true })

        // 工作者收到任务
        await worker.receiveTask({
            ...task,
            refs,
        }, {
            onData(str) {
                if (opts.onData) opts.onData(str);
            },
            onDone(str) {
                if (opts.onDone) opts.onDone(str);
            }
        });

        return task.id;
    }

    /**
     *
     * @param pars user_id, project_id
     * @param onData
     * @return {Promise<void>}
     */
    async hookTaskState(pars, onData) {
        const user_id = pars.user_id;
        const project_id = pars.project_id;
        let key = `${user_id}|${project_id}`;
        let worker = this._workers[key];
        if (worker != null) {
            await new Promise((resolve, reject) => {
                worker.hookTaskState((str) => {
                    onData(str);
                    if (str === '[LA][STATE]PENDING' || str === '[LA][STATE]ALL-DONE' || str === '[LA][STATE]CANCEL-HOOK') {
                        resolve();
                    }
                });
            });
        }
        else {
            onData('[LA][STATE]PENDING');
        }
    }

    /**
     *
     * @param pars user_id
     * @return {Promise<void>}
     */
    async cancelUserHookTaskState(pars) {
        for (const key in this._workers) {
            if (key.startsWith(`${pars.user_id}|`)) {
                const worker = this._workers[key];
                await worker.cancelHookTaskState();
            }
        }
    }

    /**
     * 获取任务结果
     * @param pars user_id, project_id
     * @return {Promise<void>}
     */
    async getTaskResult(pars) {
        const user_id = pars.user_id;
        const project_id = pars.project_id;
        const longArticleStorage = new LongArticleStorage(this.ctx);
        const outline = await longArticleStorage.loadOutline(user_id, project_id);
        return {
            outline,
        }
    }
}