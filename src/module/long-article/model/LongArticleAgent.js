export class LongArticleAgent {

    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     * 生成大纲
     * @param pars model 模型, article_type 长文类型，project_req 项目需求，outline_req 大纲要求, refs 参考资料
     * @param opts onData, onDone
     * @return {Promise<void>}
     */
    async buildOutline(pars, opts = {}) {
        const self = this;

        let model = pars.model;
        let article_type = pars.article_type ?? "";
        let project_req = pars.project_req ?? "";
        let outline_req = pars.outline_req ?? "";

        let requmentPrompt = ``;
        if (outline_req && outline_req.length > 0) {
            requmentPrompt = `#### 大纲要求
${outline_req}`;
        }

        let hasRefs = false;
        let refsPrompt = '';
        if (pars.refs && pars.refs.length > 0) {
            hasRefs = true;
            refsPrompt = '### 参考资料\n';
            for (const ref of pars.refs) {
                refsPrompt += `- 文件“${ref.name}”的内容\n`;
                refsPrompt += '```\n';
                refsPrompt += `${ref.content}\n`;
                refsPrompt += '```';
            }
            log(`【LAA】输入${pars.refs.length}份参考资料（${refsPrompt.substring(0, 100)}...）`);
        }

        let systemPrompt = `
### 任务说明
#### 角色要求
- 你现在是一个${article_type}编写专家，你的任务是为${article_type}生成大纲


${requmentPrompt}


#### 大纲格式
- 使用markdown编写大纲，举例：
${"```"}
#### 1. XXX
1.1 XXX
1.2 XXX

#### 2. XXX
2.1 XXX
2.2 XXX
${"```"}


#### 回复要求
- 不要有任何解释，只要回复内容
`;

        let buildContent = '';
        const messages = [];
        messages.push({role: 'system', content: systemPrompt});
        if (hasRefs) {
            messages.push({role: 'user', content: `${refsPrompt}`});
            messages.push({role: 'assistant', content: '好的，已阅读了参考资料'});
            messages.push({
                role: 'user', content: `请根据参考资料和下面的用户需求来生成：
            ${project_req}`
            });
        } else {
            messages.push({role: 'user', content: `${project_req}`});
        }
        log(`【LAA】messages长度：${messages.length}`)

        let inputWords = project_req.length + outline_req.length + refsPrompt.length;

        const chatModel = this._getChatModel();
        chatModel.chatStream1(model, messages, (chunk) => {
            buildContent += chunk;
            if (opts.onData) {
                opts.onData(chunk);
            }
        }, () => {
            if (opts.onDone) {
                opts.onDone(buildContent, {
                    inputWords,
                    outputWords: buildContent.length,
                });
            }
        }, (err) => {
            if (opts.onData) {
                if (typeof err === 'string') {
                    opts.onData(`[LA][ERROR]${err}`);
                } else if (err && err.message) {
                    opts.onData(`[LA][ERROR]${err.message}`);
                } else {
                    opts.onData(`[LA][ERROR]`);
                }
            }
            if (opts.onError) {
                optss.onError(err);
            }
        }, {});
    }

    /**
     * 生成长文
     * @param pars model 模型, article_type 长文类型，project_req 项目需求, outline 大纲
     * @param opts onData, onDone
     * @return {Promise<void>}
     */
    async buildArticle(pars, opts = {}) {

        let model = pars.model;
        let article_type = pars.article_type ?? "";
        let project_req = pars.project_req ?? "";
        let inputWords = 0;

        let hasRefs = false;
        let refsPrompt = '';
        if (pars.refs && pars.refs.length > 0) {
            hasRefs = true;
            refsPrompt = '### 参考资料\n';
            for (const ref of pars.refs) {
                refsPrompt += `- 文件“${ref.name}”的内容\n`;
                refsPrompt += '```\n';
                refsPrompt += `${ref.content}\n`;
                refsPrompt += '```';
            }
            log(`【LA】输入${pars.refs.length}份参考资料（${refsPrompt.substring(0, 100)}...）`);
        }

        // 拆分成章节列表
        const chapters = this._splitOutline(pars.outline);

        let systemPrompt = `
### 任务说明
#### 角色要求
- 你现在是一个${article_type}编写专家，你将根据输入的章节来编写具体内容
- 输入的内容是拆分大纲后的某一个章节，你根据此内容来生成具体内容

#### 生成${article_type}需求
- ${project_req}
`;

        let buildContent = '';
        const chatModel = this._getChatModel();

        for (const chapter of chapters) {
            inputWords += (project_req.length + chapter.length);

            let isErr = false;
            const messages = [];
            messages.push({role: 'system', content: systemPrompt});
            if (hasRefs) {
                messages.push({role: 'user', content: `${refsPrompt}`});
                messages.push({role: 'assistant', content: '好的，已阅读了参考资料'});
                messages.push({role: 'user', content: chapter});
            } else {
                messages.push({role: 'user', content: chapter});
            }

            await new Promise((resolve) => {
                chatModel.chatStream1(model, messages, (chunk) => {
                    buildContent += chunk;
                    if (opts.onData) {
                        opts.onData(chunk);
                    }
                }, () => {
                    resolve();
                }, (err) => {
                    isErr = true;
                    if (opts.onData) {
                        if (typeof err === 'string') {
                            opts.onData(`[LA][ERROR]${err}`);
                        } else if (err && err.message) {
                            opts.onData(`[LA][ERROR]${err.message}`);
                        } else {
                            opts.onData(`[LA][ERROR]`);
                        }
                    }
                    if (opts.onError) {
                        optss.onError(err);
                    }
                    resolve();
                }, {});
            });

            if (isErr) break;
        }

        if (opts.onDone) {
            opts.onDone(buildContent, {
                inputWords,
                outputWords: buildContent.length,
            });
        }
    }

    /**
     * 拆分大纲
     * @param outline
     * @private
     */
    _splitOutline(outline) {
        let list = [];
        // 把大纲字符串解析成一行行的数组
        let sections = outline.split('\n');

        let index = 0;
        let findSection = false;
        let t_add_section = '';
        for (const sectionLine of sections) {
            if (sectionLine.startsWith('#### ')) {
                findSection = true;

                if (t_add_section.length > 0) {
                    list.push(t_add_section);
                }
                t_add_section = '';
            }

            if (findSection) {
                t_add_section += sectionLine + '\n';
            }
        }

        if (t_add_section.length > 0) {
            list.push(t_add_section);
        }

        return list;
    }

    _getChatModel() {
        return this.ctx.shortTimeCacheService.getChatModel();
    }
}