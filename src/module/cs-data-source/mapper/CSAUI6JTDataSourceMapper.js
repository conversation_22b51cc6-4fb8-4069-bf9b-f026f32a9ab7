import {reqPostJsonSync} from "../../../util/http-util";

export default class CSAUI6JTDataSourceMapper {
    constructor() {
        this._debug = true;
    }

    async req(ds, cmd, pars) {
        if (ds.url == null) {
            log(`ds.url is null -> ${JSON.stringify(ds)}`)
            return {
                success: false,
                msg: `ds.url is null`
            }
        }
        else {
            const url = `${ds.url}${cmd}`
            if (this._debug) log(`【ds-mapper】请求 ${url} ...`)
            return await reqPostJsonSync(`${url}`, {
                ...pars
            })
        }
    }

    async loginByUn(ds, un) {
        const result = await this.req(ds, '/open-api/user/getLoginTokenByUn', {
            sac: ds.sac,
            skey: ds.skey,
            un,
        })

        if (result.success === true) {
            return result.data
        }
        else {
            return result
        }
    }

    async loginByUnPwd(ds, un, pwd) {
        const result = await this.req(ds, '/open-api/user/getLoginTokenByUnPwd', {
            sac: ds.sac,
            skey: ds.skey,
            un,
            pwd,
        })

        if (result.success === true) {
            return result.data
        }
        else {
            return result
        }
    }

    /**
     *
     * @param ds
     * @param token
     * @param params userId, roleName
     * @returns {Promise<*>}
     */
    async isUserInRole(ds, token, params) {
        const result = await this.req(ds, '/open-api/user/isUserInRole', {
            loginToken: token,
            sac: ds.sac,
            ...params,
        })
        if (result.success === false) throw new Error(result.msg);
        return result.data
    }

    /**
     *
     * @param ds
     * @param token
     * @param params realname, roleName
     * @returns {Promise<*>}
     */
    async isUserInRoleByRN(ds, token, params) {
        const result = await this.req(ds, '/open-api/user/isUserInRoleByRN', {
            loginToken: token,
            sac: ds.sac,
            ...params,
        })
        if (result.success === false) throw new Error(result.msg);
        return result.data
    }

    /**
     *
     * @param ds
     * @param token
     * @param params appKey, values, noSelfLimit ...
     * @returns {Promise<*>}
     */
    async getExtAppDataList(ds, token, params) {
        const result = await this.req(ds, '/open-api/extappdata/getList', {
            loginToken: token,
            sac: ds.sac,
            ...params,
        })
        if (result.success === false) throw new Error(result.msg);
        return result.data
    }

    /**
     *
     * @param ds
     * @param token
     * @param params id, appKey, searchField0Name, ...
     * @returns {Promise<*>}
     */
    async getExtAppDataForm(ds, token, params) {
        if (params.id == null || params.id.length === 0) {
            log(1)
            if (params.searchField0Name == null || params.searchField0Name.length === 0) {
                log(2)
                params.updType = "0";
            }
        }

        const result = await this.req(ds, '/open-api/extappdata/getForm', {
            loginToken: token,
            sac: ds.sac,
            ...params,
        })
        if (result.success === false) throw new Error(result.msg);
        return result.data
    }

    /**
     *
     * @param ds
     * @param token
     * @param params { updType: 1, form { appKey, id, search, content } }
     * @returns {Promise<void>}
     */
    async saveExtAppDataForm(ds, token, params) {
        // log(`【调试】sac: ${ds.sac}, params: ${JSON.stringify(params)}`)
        if (params.actionName == null) params.actionName = 'save';
        const result = await this.req(ds, '/open-api/extappdata/saveForm', {
            loginToken: token,
            sac: ds.sac,
            ...params,
        })
        if (result.success === false) throw new Error(result.msg);
        return result.data;
    }

    /**
     *
     * @param ds
     * @param token
     * @param params appKey, id, searchFields, contentFields
     * @returns {Promise<void>}
     */
    async saveExtAppDataPartFields(ds, token, params) {
        if (params.actionName == null) params.actionName = 'save';
        const result = await this.req(ds, '/open-api/extappdata/savePartFields', {
            loginToken: token,
            sac: ds.sac,
            ...params,
        })
        if (result.success === false) throw new Error(result.msg);
        return result.data;
    }

    /**
     *
     * @param ds
     * @param token
     * @param params appKey, id
     * @returns {Promise<*>}
     */
    async deleteExtAppDataForm(ds, token, params) {
        if (params.actionName == null) params.actionName = 'save';
        const result = await this.req(ds, '/open-api/extappdata/deleteForm', {
            loginToken: token,
            sac: ds.sac,
            ...params,
        })
        if (result.success === false) throw new Error(result.msg);
        return result.data;
    }

    /**
     * 获取应用中心数据
     * @param ds
     * @param token
     * @returns {Promise<*>}
     */
    async getAppCenterList(ds, token) {
        const result = await this.req(ds, '/open-api/extappdata/getList', {
            loginToken: token,
            sac: ds.sac,
            appKey: 'cac_app_center',
            noSelfLimit: true,
        })
        return result.data
    }

    /**
     * 获取AI工具列表
     * @param ds
     * @param token
     * @returns {Promise<void>}
     */
    async getAIToolList(ds, token) {
        const result = await this.req(ds, '/open-api/extappdata/getList', {
            loginToken: token,
            sac: ds.sac,
            appKey: 'cac_ai_tools',
            noSelfLimit: true,
        })
        return result.data
    }

    /** 全局配置 **/
    async getSetting(ds, token, key) {
        const result = await this.req(ds, '/open-api/extappdata/getList', {
            loginToken: token,
            sac: ds.sac,
            appKey: 'cac_setting',
            showMoreContentFields: ['data'],
            showMoreContentUseJsonExtract: true,
            values: {
                key: key
            },
            designs: {
                key: {
                    matchMode: 0
                }
            },
            noSelfLimit: true,
        })
        const list = result.data
        if (list && list.length > 0) {
            for (const item of list) {
                return eval(item.more.data)
            }
        }
        return null
    }

    async setSetting(ds, token, key, value) {
        const result = await this.req(ds, '/open-api/extappdata/getList', {
            loginToken: token,
            sac: ds.sac,
            appKey: 'cac_setting',
            values: {
                key: key
            },
            designs: {
                key: {
                    matchMode: 0
                }
            },
            noSelfLimit: true,
        })
        const list = result.data
        if (list && list.length > 0) {
            await this.req(ds, '/open-api/extappdata/savePartFields', {
                loginToken: token,
                sac: ds.sac,
                appKey: 'cac_setting',
                id: list[0].id,
                contentFields: {
                    data: value
                }
            })
        }
        else {
            await this.req(ds, '/open-api/extappdata/saveForm', {
                loginToken: token,
                sac: ds.sac,
                updType: 0,
                form: {
                    appKey: 'cac_setting',
                    search: JSON.stringify({
                        key: key,
                    }),
                    content: JSON.stringify({
                        key: key,
                        data: value
                    })
                }
            })
        }
    }

    /** 用户配置 **/
    async getUserData(ds, token, key, opts = {}) {
        let appKey = 'cac_user_setting';

        if (opts.appKey) {
            appKey = opts.appKey
        }

        const result = await this.req(ds, '/open-api/extappdata/getList', {
            loginToken: token,
            sac: ds.sac,
            appKey: appKey,
            showMoreContentFields: ['data'],
            showMoreContentUseJsonExtract: true,
            values: {
                create_user_id: ds.user.id,
                key: key
            },
            designs: {
                create_user_id: {
                    matchMode: 0, isBuildInField: true
                },
                key: {
                    matchMode: 0
                }
            },
            noSelfLimit: true,
        })
        const list = result.data
        if (list && list.length > 0) {
            for (const item of list) {
                return eval(item.more.data)
            }
        }
        return null
    }

    async setUserData(ds, token, key, value, opts = {}) {
        let appKey = 'cac_user_setting';

        if (opts.appKey) {
            appKey = opts.appKey
        }

        const result = await this.req(ds, '/open-api/extappdata/getList', {
            loginToken: token,
            sac: ds.sac,
            appKey: appKey,
            values: {
                create_user_id: ds.user.id,
                key: key
            },
            designs: {
                create_user_id: {
                    matchMode: 0, isBuildInField: true
                },
                key: {
                    matchMode: 0
                }
            },
            noSelfLimit: true,
        })
        const list = result.data
        if (list && list.length > 0) {
            await this.req(ds, '/open-api/extappdata/savePartFields', {
                loginToken: token,
                sac: ds.sac,
                appKey: appKey,
                id: list[0].id,
                contentFields: {
                    data: value
                }
            })
        }
        else {
            await this.req(ds, '/open-api/extappdata/saveForm', {
                loginToken: token,
                sac: ds.sac,
                updType: 0,
                form: {
                    appKey: appKey,
                    search: JSON.stringify({
                        key: key,
                    }),
                    content: JSON.stringify({
                        key: key,
                        data: value
                    })
                }
            })
        }
    }
}