import TopController from "../../../controller/TopController";

export default class DataSourceController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;

        /**
         * @param req.body.params
         */
        app.post('/api/cds/get-ext-app-data-list', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const dataSourceService = self.ctx.shortTimeCacheService.getDataSourceService()

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/cds/get-ext-app-data-list', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await dataSourceService.getExtAppDataList(dsl, req.body.params)
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params
         */
        app.post('/api/cds/get-ext-app-data-form', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const dataSourceService = self.ctx.shortTimeCacheService.getDataSourceService()

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/cds/get-ext-app-data-form', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await dataSourceService.getExtAppDataForm(dsl, req.body.params)
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params
         */
        app.post('/api/cds/save-ext-app-data-form', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const dataSourceService = self.ctx.shortTimeCacheService.getDataSourceService()

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/cds/save-ext-app-data-form', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await dataSourceService.saveExtAppDataForm(dsl, req.body.params)
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params appKey, id
         */
        app.post('/api/cds/delete-ext-app-data-form', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const dataSourceService = self.ctx.shortTimeCacheService.getDataSourceService()

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/cds/delete-ext-app-data-form', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await dataSourceService.deleteExtAppDataForm(dsl, req.body.params)
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params
         */
        app.post('/api/cds/get-app-center-list', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const dataSourceService = self.ctx.shortTimeCacheService.getDataSourceService()

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/cds/get-app-center-list', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await dataSourceService.getAppCenterList(dsl)
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params
         */
        app.post('/api/cds/get-ai-tool-list', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const dataSourceService = self.ctx.shortTimeCacheService.getDataSourceService()

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/cds/get-ai-tool-list', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await dataSourceService.getAIToolList(dsl)
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params key
         */
        app.post('/api/cds/get-setting', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const dataSourceService = self.ctx.shortTimeCacheService.getDataSourceService()
            const key = req.body.params.key

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/cds/get-setting', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await dataSourceService.getSetting(dsl, key)
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params
         */
        app.post('/api/cds/set-setting', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const dataSourceService = self.ctx.shortTimeCacheService.getDataSourceService()
            const key = req.body.params.key
            const value = req.body.params.value

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cds/set-setting', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                await dataSourceService.setSetting(dsl, key, value)
                res.send({ success: true });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         * @param req.body.params
         */
        app.post('/api/cds/get-user-data', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const dataSourceService = self.ctx.shortTimeCacheService.getDataSourceService()
            const key = req.body.params.key
            const opts = req.body.params.opts

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res)
                self.logReq(req, '/api/cds/get-user-data', null, validateLoginResult)
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const r = await dataSourceService.getUserData(dsl, key, {
                    ...opts
                })
                res.send({ success: true, data: r })
            } catch (exc) {
                self.replyException(res, exc, null, req)
            }
        });

        /**
         * @param req.body.params
         */
        app.post('/api/cds/set-user-data', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            const dataSourceService = self.ctx.shortTimeCacheService.getDataSourceService()
            const key = req.body.params.key
            const value = req.body.params.value
            const opts = req.body.params.opts

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cds/set-user-data', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                await dataSourceService.setUserData(dsl, key, value, {
                    ...opts
                })
                res.send({ success: true });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });
    }
}