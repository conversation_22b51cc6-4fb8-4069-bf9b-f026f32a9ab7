import CSAUI6JTDataSourceMapper from "../mapper/CSAUI6JTDataSourceMapper";

export default class DataSourceService {
    constructor(ctx) {
        this.ctx = ctx;
        this._dataSourceMapper = new CSAUI6JTDataSourceMapper()

        this._loginCache = {}
    }

    async _getLoginToken(dsl) {
        const key = `${dsl.sac}|${dsl.user.username}`
        const cache = this._loginCache[key]
        if (cache) {
            cache.use_time = Date.now()
            return cache.token
        }
        else {
            const result = await this.loginByUn(dsl, dsl.user.username)
            return result.token
        }
    }

    async _setLoginCache(key, value) {
        value.use_time = Date.now()
        value.crt_time = Date.now()
        this._loginCache[key] = value
    }

    async loginByUn(ds, un) {
        const loginResult = await this._dataSourceMapper.loginByUn(ds, un)
        if (loginResult.success) {
            // 加入缓存
            const key = `${ds.sac}|${un}`
            await this._setLoginCache(key, {
                token: loginResult.token
            })
        }

        return loginResult
    }

    async loginByUnPwd(ds, un, pwd) {
        const loginResult = await this._dataSourceMapper.loginByUnPwd(ds, un, pwd)
        if (loginResult.success) {
            // 加入缓存
            const key = `${ds.sac}|${un}`
            await this._setLoginCache(key, {
                token: loginResult.token
            })
        }

        return loginResult
    }

    /**
     *
     * @param dsl
     * @param params userId, roleName
     * @returns {Promise<*>}
     */
    async isUserInRole(dsl, params) {
        const token = await this._getLoginToken(dsl)
        const data = await this._dataSourceMapper.isUserInRole(dsl, token, params)
        return data
    }

    /**
     *
     * @param dsl
     * @param params realname, roleName
     * @returns {Promise<*>}
     */
    async isUserInRoleByRN(dsl, params) {
        const token = await this._getLoginToken(dsl)
        const data = await this._dataSourceMapper.isUserInRoleByRN(dsl, token, params)
        return data
    }

    async getExtAppDataList(dsl, params) {
        const token = await this._getLoginToken(dsl)
        const data = await this._dataSourceMapper.getExtAppDataList(dsl, token, params)
        return data
    }

    async getExtAppDataForm(dsl, params) {
        const token = await this._getLoginToken(dsl)
        const data = await this._dataSourceMapper.getExtAppDataForm(dsl, token, params)
        return data
    }

    /**
     *
     * @param dsl
     * @param params updType, form: { appKey, id, search, content }
     * @returns {Promise<void>}
     */
    async saveExtAppDataForm(dsl, params) {
        const token = await this._getLoginToken(dsl)
        const data = await this._dataSourceMapper.saveExtAppDataForm(dsl, token, params)
        return data
    }

    async saveExtAppDataPartFields(dsl, params) {
        const token = await this._getLoginToken(dsl)
        const data = await this._dataSourceMapper.saveExtAppDataPartFields(dsl, token, params)
        return data
    }

    async deleteExtAppDataForm(dsl, params) {
        const token = await this._getLoginToken(dsl)
        const data = await this._dataSourceMapper.deleteExtAppDataForm(dsl, token, params)
        return data
    }

    async getAppCenterList(dsl) {
        const token = await this._getLoginToken(dsl)
        const data = await this._dataSourceMapper.getAppCenterList(dsl, token)
        const list = []
        // console.log(JSON.stringify(dsl.user))
        for (const i in data) {
            const item = data[i]
            const sObj = JSON.parse(item.search)

            let sac_match = true;
            let match = false;

            // 检查授权域（为空表示无限制）
            if (sObj.can_see_sac && sObj.can_see_sac.length > 0 && sObj.can_see_sac !== '||') {
                if (sObj.can_see_sac.indexOf(`|${dsl.sac}|`) === -1) {
                    sac_match = false;
                }
            }
            // *** 下面只要一个条件成立就行 ***
            // 检查角色
            if (match !== true) {
                if (sObj.can_see_role && sObj.can_see_role.length > 0 && sObj.can_see_role !== '||') {
                    let find = false;
                    for (const role of dsl.user.roles) {
                        if (sObj.can_see_role.indexOf(`|${role}|`) !== -1) {
                            find = true;
                            break;
                        }
                    }
                    if (find) {
                        match = true;
                    }
                }
            }
            // 检查用户
            if (match !== true) {
                if (sObj.can_see_rn && sObj.can_see_rn.length > 0 && sObj.can_see_rn !== '||') {
                    if (sObj.can_see_rn.indexOf(`|${dsl.user.realname}|`) !== -1) {
                        match = true;
                    }
                }
            }

            if (sac_match && match) {
                list.push({
                    id: item.id,
                    ...sObj
                })
            }
        }
        return list
    }

    async getAIToolList(dsl) {
        const token = await this._getLoginToken(dsl)
        const data = await this._dataSourceMapper.getAIToolList(dsl, token)
        const list = []
        for (const i in data) {
            const item = data[i]
            const sObj = JSON.parse(item.search)

            if (sObj.disabled === true) {
            }
            else {
                list.push({
                    id: item.id,
                    ...sObj
                })
            }
        }
        return list
    }

    /*** 授权域 ***/

    async getSetting(dsl, key) {
        const token = await this._getLoginToken(dsl)
        return await this._dataSourceMapper.getSetting(dsl, token, key)
    }

    async setSetting(dsl, key, value) {
        const token = await this._getLoginToken(dsl)
        await this._dataSourceMapper.setSetting(dsl, token, key, value)
    }

    /*** 用户数据 ***/

    async getUserData(dsl, key, opts = {}) {
        const token = await this._getLoginToken(dsl)
        return await this._dataSourceMapper.getUserData(dsl, token, key, opts)
    }

    async setUserData(dsl, key, value, opts = {}) {
        const token = await this._getLoginToken(dsl)
        await this._dataSourceMapper.setUserData(dsl, token, key, value, opts)
    }
}