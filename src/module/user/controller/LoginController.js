import TopController from "../../../controller/TopController";
import CSDESA from "../../../util/CSDESA";

export default class LoginController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;
        this._debug = false;
        
        /**
         * 是否登录
         * @param {String} req.body.token 令牌
         */
        app.post('/api/user/isLogin', async function(req, res) {
            // self.logReq(req, '/api/user/isLogin');
            try {
                const r = await self.ctx.shortTimeCacheService.getLoginService()
                .isLogin(req.body.token, req, res);
                res.send({ success: true, data: r});
            } catch (exc) {
                self.replyException(res, exc);
            }
        });

        /**
         * 登录
         * @param {String} req.body.sac 授权号
         * @param {String} req.body.username 用户名
         * @param {String} req.body.password 密码
         */
        app.post('/api/user/login', async function(req, res) {
            self.logReq(req, '/api/user/login');
            try {
                let r;

                if (req.body.token_348bdbde && req.body.token_348bdbde.length > 0) {
                    if (self._debug) log('token_348bdbde: ', req.body.token_348bdbde);
                    const csdesa = new CSDESA();
                    const token_str = csdesa.decrypt('!a3d@cde', req.body.token_348bdbde);
                    const t_arr = token_str.split('|');
                    const username = t_arr[0];
                    const password = t_arr[1];
                    const sac = t_arr[2];

                    if (self._debug) log('token_str: ', token_str);

                    r = await self.ctx.shortTimeCacheService.getLoginService()
                        .login(sac, username, password, req, res);
                }
                else if (req.body.skey && req.body.skey.length > 0) {
                    r = await self.ctx.shortTimeCacheService.getLoginService()
                    .loginByUn(req.body.sac, req.body.username, req.body.skey, req, res);
                }
                else {
                    r = await self.ctx.shortTimeCacheService.getLoginService()
                    .login(req.body.sac, req.body.username, req.body.password, req, res);
                }

                res.send(r);
            } catch (exc) {
                self.replyException(res, exc);
            }
        });

        /**
         * 登出
         * @param {String} req.body.sac 授权号
         * @param {String} req.body.username 用户名
         */
        app.post('/api/user/logout', async function(req, res) {
            self.logReq(req, '/api/user/logout');
            try {
                const r = await self.ctx.shortTimeCacheService.getLoginService()
                .logout(req.body.sac, req.body.username, req, res);
                res.send({ success: true, data: r});
            } catch (exc) {
                self.replyException(res, exc);
            }

        });
    }
}