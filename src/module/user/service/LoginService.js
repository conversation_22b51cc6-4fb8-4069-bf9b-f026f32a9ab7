import CSDESA from "../../../util/CSDESA";
/**
 * 登录服务
 */
export default class LoginService {
    constructor(ctx, cfg) {
        this.ctx = ctx;
        this.cfg = cfg;
        this._token_key = '!bd3#25a';

        this._loginCache = {};
    }

    async _getLoginCache(key) {
        const cache = this._loginCache[key]
        if (cache) {
            cache.use_time = Date.now()
            return cache
        }
    }

    async _setLoginCache(key, value) {
        value.use_time = Date.now()
        value.crt_time = Date.now()
        this._loginCache[key] = value
    }

    async _removeLoginCache(key) {
        delete this._loginCache[key]
    }

    buildToken(sac, user) {
        const tokenObj = {
            sac: sac,
            id: user.id,
            un: user.username,
            ts: Date.now(),
        };
        const csdesa = new CSDESA();
        const token = csdesa.encrypt(this._token_key, JSON.stringify(tokenObj));
        return token;
    }

    parseToken(token) {
        const csdesa = new CSDESA();
        const json = csdesa.decrypt(this._token_key, token);
        return JSON.parse(json);
    }

    _convertUserLoginInfoToLoginUser(sac, uli) {
        const lu = {
            sac: sac,
            id: uli.id,
            username: uli.username,
            realname: uli.realname,
            // 角色
            roles: [],
            // 权限
            perms: [],
        };

        if (uli.roleList) {
            uli.roleList.forEach((n) => {
                lu.roles.push(n.name)
            })
        }

        if (uli.authorities) {
            uli.authorities.forEach((n) => {
                lu.perms.push(n.id)
            })
        }

        return lu;
    }

    /**
     * 验证令牌
     * @param {String} token 令牌
     * @param {Object} req 请求
     * @param {Object} res 响应
     * @returns {IsLoginResult} 令牌是否有效
     */
    async isLogin(token, req, res) {
        const debug = false;

        try {
            if (debug) log(`【验证令牌】1 | ${token}`)
            if (token && token.length > 0) {
                const csdesa = new CSDESA()
                const tokenTxt = csdesa.decrypt(this._token_key, token)
                if (debug) log(`【验证令牌】2 | ${tokenTxt}`)
                const tokenObj = JSON.parse(tokenTxt)
                if (debug) log(`【验证令牌】3`)

                // 通过缓存验证
                const cache = await this._getLoginCache(`${tokenObj.sac}|${tokenObj.un}`)
                if (cache && cache.user) {
                    return {
                        isLogin: true,
                        user: cache.user
                    }
                }

                // 通过数据源登录
                const ds = this.getDataSource(tokenObj.sac)
                if (ds.url) {
                    const loginResult = await this.ctx.shortTimeCacheService.getDataSourceService()
                        .loginByUn(ds, tokenObj.un)
                    if (loginResult.success === true) {
                        // log(`【数据源登录验证】${tokenObj.un}@${tokenObj.sac} 成功`)
                        const user = this._convertUserLoginInfoToLoginUser(tokenObj.sac, loginResult.user)
                        // 缓存
                        await this._setLoginCache(`${user.sac}|${user.username}`, {
                            user: user
                        })

                        return {
                            isLogin: true,
                            user: user
                        }
                    }
                    /* 允许继续使用配置文件里的登录 */
                    // else {
                    //     log(`【数据源登录验证】${tokenObj.un}@${tokenObj.sac} 失败 -> ${loginResult.msg}`);
                    //     return {
                    //         isLogin: false,
                    //     }
                    // }
                }

                // 通过配置文件登录
                let findUser = null;
                if (findUser = this._findUserByUsernameInCfg(tokenObj.sac, tokenObj.un)) {
                }
                else {
                    return {
                        isLogin: false,
                    }
                }
                const user = {
                    sac: tokenObj.sac,
                    id: findUser.id,
                    username: findUser.username,
                    realname: findUser.realname,
                    weixin: findUser.weixin,
                    roles: findUser.roles,
                }
                // 缓存
                await this._setLoginCache(`${user.sac}|${user.username}`, {
                    user: user
                })

                return {
                    isLogin: true,
                    user: user
                }
            }
        }
        catch (exc) {
            log(`【验证令牌】异常 -> ${exc.message}`)
        }

        return {
            isLogin: false,
        }
    }

    /**
     * 登录
     * @param {*} sac 
     * @param {*} username 
     * @param {*} password 
     * @returns {LoginResult} 登录结果
     */
    async login(sac, username, password, req, res) {
        const result = {
            success: false,
            msg: '登录失败',
            code: 'LOGIN_FAILED',
        };

        // 通过数据源登录
        const ds = this.getDataSource(sac)
        if (ds.url) {
            log(`【数据源登录】${username}@${sac} / ${password} ...`)
            const loginResult = await this.ctx.shortTimeCacheService.getDataSourceService().loginByUnPwd(ds, username, password)
            if (loginResult.success === true) {
                const token = this.buildToken(sac, loginResult.user)
                log(`【数据源登录】${username}@${sac} / ${password} 成功`);
                const user = this._convertUserLoginInfoToLoginUser(sac, loginResult.user)
                // 缓存
                await this._setLoginCache(`${sac}|${username}`, {
                    user: user
                })

                return {
                    success: true,
                    token,
                    user: user
                }
            }
            else {
                log(`【数据源登录】${username}@${sac} / ${password} 失败 -> ${loginResult.msg}`);
                return {
                    success: false,
                    msg: loginResult.msg,
                    code: 'USER_UN_PWD_ERROR'
                }
            }
        }

        // 通过配置文件
        let findUser = null;
        if (findUser = this._findUserByUsernameInCfg(sac, username)) {
            if (findUser.username === username && findUser.password === password) {
                // 生成令牌
                const token = this.buildToken(sac, findUser);
                const user = {
                    sac: sac,
                    id: findUser.id,
                    username: findUser.username,
                    realname: findUser.realname,
                    roles: findUser.roles,
                }

                result.success = true;
                result.msg = null;
                result.code = null;
                result.token = token;
                result.user = user

                // 缓存
                await this._setLoginCache(`${sac}|${username}`, {
                    user: user
                })

                log(`【登录成功】${username}@${sac} / ${password}`);
                return result;
            }
            else {
                result.success = false;
                result.msg = '账号或密码错误';
                result.code = 'USER_UN_PWD_ERROR';
                log(`【登录失败】${username}@${sac} / ${password} -> 密码错误`);
            }
        }
        else {
            result.success = false;
            result.msg = '账号或密码错误';
            result.code = 'USER_UN_PWD_ERROR';
            log(`【登录失败】${username}@${sac} / ${password} -> 没能根据用户名找到用户`);
        }

        return result;
    }

    /**
     * 通过用户名直接登录
     * @param {*} sac 
     * @param {*} username 
     * @param {*} skey 
     * @param {*} req 
     * @param {*} res 
     * @returns {LoginResult} 登录结果
     */
    async loginByUn(sac, username, skey, req, res) {
        if (skey && skey.length > 0 && this.cfg.user_db[sac].skey === skey) {
            // 通过数据源登录
            const ds = this.getDataSource(sac)
            if (ds.url) {
                log(`【数据源登录】${username}@${sac} ...`)
                const loginResult = await this.ctx.shortTimeCacheService.getDataSourceService().loginByUn(ds, username)
                if (loginResult.success === true) {
                    const token = this.buildToken(sac, loginResult.user)
                    log(`【数据源登录】${username}@${sac} 成功`);
                    const user = this._convertUserLoginInfoToLoginUser(sac, loginResult.user)
                    // 缓存
                    await this._setLoginCache(`${sac}|${username}`, {
                        user: user
                    })

                    return {
                        success: true,
                        token,
                        user: user
                    }
                }
                else {
                    log(`【数据源登录】${username}@${sac} 失败 -> ${loginResult.msg}`);
                    return {
                        success: false,
                        msg: loginResult.msg,
                        code: 'USER_UN_PWD_ERROR'
                    }
                }
            }

            // 通过配置文件
            let findUser = this._findUserByUsernameInCfg(sac, username);
            return this.login(sac, username, findUser.password, req, res);
        }
        return {
            success: false,
            msg: '秘钥错误',
            code: 'USER_SKEY_ERROR'
        };
    }

    /**
     * 通过微信直接登录
     * @param {*} sac
     * @param {*} weixin
     * @param skey
     * @param req
     * @param res
     * @returns {LoginResult} 登录结果
     */
    async loginByWx(sac, weixin, skey, req, res) {
        if (skey && skey.length > 0 && this.cfg.user_db[sac].skey === skey) {
            let findUser = this._findUserByWeixinInCfg(sac, weixin);
            if (findUser) {
                return this.login(sac, findUser.username, findUser.password, req, res);
            }
            else {
                return {
                    success: false,
                    msg: '未能根据微信号找到用户，请联系管理员挂钩',
                    code: 'USER_WX_ERROR'
                };
            }
        }
        else {
            return {
                success: false,
                msg: '秘钥错误',
                code: 'USER_SKEY_ERROR'
            };
        }
    }

    /**
     * 登出
     * @param {*} sac 
     * @param {*} username 
     * @param {*} req 
     * @param {*} res 
     */
    async logout(sac, username, req, res) {
        await this._removeLoginCache(`${sac}|${username}`)
    }

    /**
     * 判定用户是否拥有角色
     * @param user
     * @param roleName
     * @returns {Promise<void>}
     */
    async hasRole(user, roleName) {
        // 通过数据源
        const ds = this.getDataSource(user.sac)
        if (ds.url) {
            const dsl = this.getDataSourceLogin(user);
            const r = await this.ctx.shortTimeCacheService.getDataSourceService().isUserInRoleByRN(dsl, {
                realname: user.realname,
                roleName,
            })
            if (r === true) {
                return true;
            }
        }
        // 通过配置文件
        const findUser = this._findUserByUsernameInCfg(user.sac, user.username);
        if (findUser) {
            if (findUser.roles) {
                for (const role of findUser.roles) {
                    if (role === roleName) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    getDataSourceLogin(user) {
        const dsl = this.getDataSource(user.sac);
        dsl.user = user;
        return dsl;
    }

    /**
     * 获取数据源配置
     * @param sac
     * @returns {{url: *, sac: *}|null}
     */
    getDataSource(sac) {
        if (this.cfg && this.cfg.user_db && this.cfg.user_db[sac]) {
            const dsUrl = this.cfg.user_db[sac].dataSourceUrl
            const dsType = this.cfg.user_db[sac].dataSourceType || 'csaui6jt'
            const dsSac = this.cfg.user_db[sac].dataSourceSac || sac
            const skey = this.cfg.user_db[sac].skey
            return {
                url: dsUrl,
                type: dsType,
                sac: dsSac,
                skey,
            }
        }
        return null;
    }

    /**
     * 从配置中查找用户
     * @param {String} sac 
     * @param {String} username 
     * @returns {Object} 用户
     */
    _findUserByUsernameInCfg(sac, username) {
        if (this.cfg && this.cfg.user_db && this.cfg.user_db[sac] && this.cfg.user_db[sac].users) {
            const users = this.cfg.user_db[sac].users;
            for (const user of users) {
                // 生成用户ID
                if (!user.id) {
                    user.id = user.username;
                }
                if (user.username === username) {
                    return user;
                }
            }
        }
        return null;
    }

    /**
     * 从配置中查找用户
     * @param {String} sac 
     * @param {String} weixin 
     * @returns {Object} 用户
     */
    _findUserByWeixinInCfg(sac, weixin) {
        if (this.cfg && this.cfg.user_db && this.cfg.user_db[sac] && this.cfg.user_db[sac].users) {
            const users = this.cfg.user_db[sac].users;
            for (const user of users) {
                // 生成用户ID
                if (!user.id) {
                    user.id = user.username;
                }
                if (user.weixin === weixin) {
                    return user;
                }
            }
        }
        return null;
    }
}