import {reqPostJsonSync} from "../../../util/http-util";

/**
 * 导入数据到知识库工具
 */
export class ImportDataToKBUtil {

    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     * 导入知识碎片
     * @param url
     * @param sac
     * @param inputPars
     * @param kfList {[{ title, content }]}
     */
    async importKf(url, sac, inputPars) {
        log(`导入知识碎片 ${inputPars.title} ...`)
        await reqPostJsonSync(url, {
            sac,
            appKey: 'zhai_zssp',
            pluginName: 'kb_new_kf_form',
            inputPars: inputPars
        })
        log(`导入完毕！`)
    }
}