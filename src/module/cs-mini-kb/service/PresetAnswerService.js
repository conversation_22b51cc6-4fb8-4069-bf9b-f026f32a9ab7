import {calculateCosineSimilarity} from "../../../util/vector-util";

/**
 * 预设回答服务
 */
export class PresetAnswerService {
    constructor(ctx, parent) {
        this.ctx = ctx;
        this.parent = parent;
    }

    /**
     * 搜索预设回答
     * @param kbId
     * @param text 用户提问
     * @param limit 筛选条数限定
     * @param opts minScore 最低分数（例：0.85）
     * @returns {Promise<void>}
     */
    async search(kbId, text, limit, opts = {}) {
        let embedModel = null;

        // 加载知识库参数
        const kbForm = this.ctx.shortTimeCacheService.getKbMapper().load(kbId);
        if (kbForm) {
            embedModel = kbForm.embed_model;
        }

        const self = this;
        const conn = await this.parent._getConn(kbId);
        const textV = await this.parent._embedSync(text, embedModel);
        const finalList = [];

        {
            const findListByTitle = await this.parent.lanceDBMapper.vectorSearchSync(conn, self.parent._table_yshd,
                'title_vector', textV, limit);
            log(`根据标题共找到 ${findListByTitle.length} 条`);
            let i = 0;
            for (const item of findListByTitle) {
                const newItem = {
                    id: item.id,
                    title: item.title,
                    content: item.content,
                    score: calculateCosineSimilarity(item.title_vector, textV)
                };
                if (opts.minScore == null || newItem.score >= opts.minScore) {
                    log(`(${++i}) ${newItem.id} | ${newItem.title} | ${newItem.score}`);
                    finalList.push(newItem);
                }
                else {
                    log(`(${++i}) （忽略）${newItem.id} | ${newItem.title} | ${newItem.score}`);
                }
            }
        }

        return finalList;
    }
}