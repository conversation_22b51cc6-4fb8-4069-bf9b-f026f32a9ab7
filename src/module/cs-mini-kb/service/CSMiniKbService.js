import LanceDBMapper from "../../../mapper/LanceDBMapper";
import {calculateCosineSimilarity} from "../../../util/vector-util";
import {getPartialStr} from "../../../util/str-util";
import {OllamaAIReplyStreamChunkHelper, MoonshotAIReplyStreamChunkHelper, AIReplyStreamChunkHelper1} from "../../../util/ai-reply-stream-util";
import {PresetAnswerService} from "./PresetAnswerService";
import {KbController} from "../controller/KbController";

/**
 * CS迷你知识库服务
 */
export default class CSMiniKbService {

    constructor(ctx, cfg) {
        this.ctx = ctx;
        this.cfg = cfg;

        this.presetAnswerService = new PresetAnswerService(ctx, this);

        this.lanceDBMapper = new LanceDBMapper(ctx);
        this._table_0 = "table_0";
        this._table_yshd = "table_yshd";
        this._debug_show_query_result = true;
    }

    start(pars) {
        const { app } = pars;

        new KbController(this.ctx, app);
    }

    getNotFindReply(kbId) {
        let notFindReply = "对不起，没能找到相关知识";

        // 加载知识库参数
        const kbForm = this.ctx.shortTimeCacheService.getKbMapper().load(kbId);
        if (kbForm && kbForm.not_find_reply && kbForm.not_find_reply.length > 0) {
            notFindReply = kbForm.not_find_reply;
        }

        return notFindReply;
    }

    _buildMessages(kbId, kw, findList, opts) {
        let notFindReply = this.getNotFindReply(kbId);

        let knowledge = "";//"下面是找到的知识：\n\n";
        for (const item of findList) {
            knowledge += `知识标题：${item.title}\n`;
            if (opts.procConType === '1') {
                knowledge += `知识内容：\n\`\`\`${item.content}\`\`\`\n`;
            } else {
                knowledge += `知识内容：\n${item.content}\n`;
            }
            knowledge += `\n\n`;
        }

        // 提问增强
        if (kw.indexOf("流程") !== -1 && kw.indexOf("流程图") === -1) {
            kw = kw + "（或流程图）";
        }

        const messages = [
            // {
            //     role: 'user',
            //     content: ``
            //         + `忘记你已有的知识，仅使用<QA></QA>标记中的知识进行回答。\n`
            //         + `<QA>${knowledge}</QA>\n`
            //         + `避免提及你是从<QA></QA>标记中获取的知识，只需要回复答案。\n`
            //         //+ `如果问题和上面的知识不相关就回复“对不起，没能找到相关知识”。\n`
            //         + `问题："""${kw}"""`
            // },
            {
                role: 'user',
                content: ``
                    + `### 任务说明\n`
                    + `#### 角色设定\n`
                    + `1. 作为一个智能助手，你的任务是根据提供的知识库来回答问题。\n`
                    + `\n\n\n`
                    + `#### 要求与限制\n`
                    + `1. 避免提及你是从提供的知识库获取的知识，只需要回复答案。\n`
                    + `2. 如果问题和提供的知识库中的内容不相关就回复“${notFindReply}”。\n`
                    + `3. 输出内容的风格要求友好、亲切。\n`
                    + `\n\n\n`
                    + `#### 提供的知识库\n`
                    + "```\n"
                    + `${knowledge}\n`
                    + "```\n"
                    + `\n\n\n`
                    + `问题："""${kw}"""`
            },
        ];

        return messages;
    }

    /**
     * 聊天
     * @param kbId
     * @param kw
     * @returns {Promise<*>} { message: { role, content }, done: true }
     */
    async chatSync(kbId, kw, opts) {
        if (opts == null) opts = {};
        if (opts.chatModelPlatform == null) opts.chatModelPlatform = this.cfg.chatModelPlatform;
        if (opts.chatModel == null) opts.chatModel = this.cfg.chatModel;

        const self = this;
        const debug = true;

        log(`请求 searchSync ${kbId} ${kw} ... `);
        const findList = await this.searchSync(kbId, kw, 6, {exclude_kw: opts.exclude_kw});

        const messages = this._buildMessages(kbId, kw, findList, opts);

        const messagesStr = JSON.stringify(messages);
        log(`消息内容字数约：${messagesStr.length}`);
        log(`${getPartialStr(messagesStr, 500)}`);

        log(`使用模型“${opts.chatModel}”回答...（全量输出）`);
        let chatResult = await this._getChatModel().chat1(opts.chatModel, messages, {
            
        });
        return chatResult.reply;
    }

    /**
     * 聊天（流输出）
     * @param kbId
     * @param kw
     * @returns {Promise<*>} { message: { role, content }, done: true }
     */
    async chatStreamSync(kbId, kw, onData, onEnd, onError, opts = {}) {
        if (opts == null) opts = {};
        if (opts.chatModelPlatform == null) opts.chatModelPlatform = this.cfg.chatModelPlatform;
        if (opts.chatModel == null) opts.chatModel = this.cfg.chatModel;

        const self = this;
        const debug = true;

        log(`请求 searchSync ${kbId} ${kw} ... `);
        const findList = await this.searchSync(kbId, kw, 6, {exclude_kw: opts.exclude_kw});

        const messages = this._buildMessages(kbId, kw, findList, opts);

        const messagesStr = JSON.stringify(messages);
        log(`消息内容字数约：${messagesStr.length}`);
        log(`${getPartialStr(messagesStr, 500)}`);

        log(`使用模型“${opts.chatModel}”回答...（流式输出）`);

        let replyChunk;
        let aiReplyStreamChunkHelper;
        if (opts.chatModel.startsWith('ollama_')) {
            aiReplyStreamChunkHelper = new OllamaAIReplyStreamChunkHelper({
                onReasoningChange(val) {
                },
                onReplyChange(val) {
                    replyChunk = val;
                }
            });
        }
        else if (opts.chatModel.startsWith('moonshot-')) {
            aiReplyStreamChunkHelper = new MoonshotAIReplyStreamChunkHelper({
                onReasoningChange(val) {
                },
                onReplyChange(val) {
                    replyChunk = val;
                }
            });
        }
        else {
            aiReplyStreamChunkHelper = new AIReplyStreamChunkHelper1({
                onReasoningChange(val) {
                },
                onReplyChange(val) {
                    replyChunk = val;
                }
            });
        }

        await this._getChatModel().chatStream1(opts.chatModel, messages, onData, onEnd, onError, {
        });
    }

    /**
     * 搜索
     * @param kbId
     * @param query 用户提问（会转为向量去查询）
     * @param kwList 关键词列表
     * @param limit 筛选条数限定
     * @returns {Promise<[]>}
     */
    async searchSync1(kbId, query, kwList, limit, opts) {
        if (opts == null) opts = {};
        let embedModel = null;

        // 加载知识库参数
        const kbForm = this.ctx.shortTimeCacheService.getKbMapper().load(kbId);
        if (kbForm) {
            embedModel = kbForm.embed_model;
            if (opts.exclude_kw == null) opts.exclude_kw = kbForm.exclude_kw;
        }
        log(`要排除的关键词: ${opts.exclude_kw}`);

        try {
            const self = this;
            const conn = await this._getConn(kbId);
            const kwV = await this._embedSync(query, embedModel);
            // log('生成向量', kwV);

            const finalList = [];

            // 向量查询
            {
                const findListByContent = await this.lanceDBMapper.vectorSearchSync(conn, self._table_0,
                    'content_vector', kwV, limit);
                log(`根据内容共找到 ${findListByContent.length} 条`);
                for (const item of findListByContent) {
                    const newItem = {
                        id: item.id,
                        title: item.title,
                        content: item.content,
                        score: calculateCosineSimilarity(item.content_vector, kwV)
                    };
                    if (self._debug_show_query_result) log(`${item.id} | ${item.title} | ${newItem.score}`);
                    finalList.push(newItem);
                }

                const findListByTitle = await this.lanceDBMapper.vectorSearchSync(conn, self._table_0,
                    'title_vector', kwV, limit);
                log(`根据标题共找到 ${findListByTitle.length} 条`);
                for (const item of findListByTitle) {
                    const newItem = {
                        id: item.id,
                        title: item.title,
                        content: item.content,
                        score: calculateCosineSimilarity(item.title_vector, kwV)
                    };
                    if (self._debug_show_query_result) log(`${item.id} | ${item.title} | ${newItem.score}`);
                    // 在finallList中找到这条，然后平均分下，如果没找到就添加
                    const finded = finalList.find(x => x.id === item.id);
                    if (finded) {
                        finded.score = (finded.score + newItem.score) / 2;
                    } else {
                        finalList.push(newItem);
                    }
                }
            }

            // 关键词查询
            {
                log(`输入的关键词：${kwList.toString()}`);
                if (opts.exclude_kw != null && opts.exclude_kw.length > 0) {
                    kwList = kwList.filter(x => opts.exclude_kw.indexOf(x) === -1);
                    log(`排除后的关键词：${kwList.toString()}`);
                }

                if (kwList.length > 0) {
                    // 在标题中查关键词
                    {
                        const findList = await this.lanceDBMapper.searchMultiKwSync(conn, self._table_0,
                            'title', kwList, limit);
                        log(`根据标题关键词共找到 ${findList.length} 条（${kwList.toString()}）`);
                        for (const item of findList) {
                            const newItem = {
                                id: item.id,
                                title: item.title,
                                content: item.content,
                                score: calculateCosineSimilarity(item.title_vector, kwV)
                            };
                            // 在finallList中找到这条，然后平均分下，如果没找到就添加
                            const finded = finalList.find(x => x.id === item.id);
                            if (finded) {
                                finded.score = (finded.score + newItem.score) / 2;
                                if (self._debug_show_query_result) log(`（已存在）${item.id} | ${item.title} | ${newItem.score}`);
                            } else {
                                if (newItem.score >= 0.5) {
                                    finalList.push(newItem);
                                    if (self._debug_show_query_result) log(`${item.id} | ${item.title} | ${newItem.score}`);
                                }
                                else {
                                    if (self._debug_show_query_result) log(`（忽略）${item.id} | ${item.title} | ${newItem.score}`);
                                }
                            }
                        }
                    }
                    // 在内容中查关键词
                    {
                        const findList = await this.lanceDBMapper.searchMultiKwSync(conn, self._table_0,
                            'content', kwList, limit);
                        log(`根据内容关键词共找到 ${findList.length} 条（${kwList.toString()}）`);
                        for (const item of findList) {
                            const newItem = {
                                id: item.id,
                                title: item.title,
                                content: item.content,
                                score: calculateCosineSimilarity(item.content_vector, kwV)
                            };
                            // 在finallList中找到这条，然后平均分下，如果没找到就添加
                            const finded = finalList.find(x => x.id === item.id);
                            if (finded) {
                                finded.score = (finded.score + newItem.score) / 2;
                                if (self._debug_show_query_result) log(`（已存在）${item.id} | ${item.title} | ${newItem.score}`);
                            } else {
                                if (newItem.score >= 0.3) {
                                    finalList.push(newItem);
                                    if (self._debug_show_query_result) log(`${item.id} | ${item.title} | ${newItem.score}`);
                                }
                                else {
                                    if (self._debug_show_query_result) log(`（忽略）${item.id} | ${item.title} | ${newItem.score}`);
                                }
                            }
                        }
                    }
                }
            }

            // 对 finalList 进行排序，根据score倒叙
            finalList.sort(function (b, a) {
                if (a.score > b.score) {
                    return 1;
                } else if (a.score < b.score) {
                    return -1;
                }
                return 0;
            });

            log(`最终合成后共 ${finalList.length} 条`);
            for (const item of finalList) {
                if (self._debug_show_query_result) log(`${item.id} | ${item.title} | ${item.score}`);
            }

            return finalList;
        } catch (exc) {
            log(`searchSync异常 -> ${exc.message}`);
            throw exc;
        }
    }

    /**
     * 搜索
     * @param kbId
     * @param kw 关键词/问题
     * @param limit 筛选条数限定
     * @returns {Promise<[]>}
     */
    async searchSync(kbId, kw, limit, opts) {
        if (opts == null) opts = {};
        let embedModel = null;

        // 加载知识库参数
        const kbForm = this.ctx.shortTimeCacheService.getKbMapper().load(kbId);
        if (kbForm) {
            embedModel = kbForm.embed_model;
            if (opts.exclude_kw == null) opts.exclude_kw = kbForm.exclude_kw;
        }
        log(`要排除的关键词: ${opts.exclude_kw}`);

        try {
            const self = this;
            const conn = await this._getConn(kbId);
            const kwV = await this._embedSync(kw, embedModel);
            // log('生成向量', kwV);

            const finalList = [];

            // 向量查询
            {
                const findListByContent = await this.lanceDBMapper.vectorSearchSync(conn, self._table_0,
                    'content_vector', kwV, limit);
                log(`根据内容共找到 ${findListByContent.length} 条`);
                for (const item of findListByContent) {
                    const newItem = {
                        id: item.id,
                        title: item.title,
                        content: item.content,
                        score: calculateCosineSimilarity(item.content_vector, kwV)
                    };
                    log(`${item.id} | ${item.title} | ${newItem.score}`);
                    finalList.push(newItem);
                }

                const findListByTitle = await this.lanceDBMapper.vectorSearchSync(conn, self._table_0,
                    'title_vector', kwV, limit);
                log(`根据标题共找到 ${findListByTitle.length} 条`);
                for (const item of findListByTitle) {
                    const newItem = {
                        id: item.id,
                        title: item.title,
                        content: item.content,
                        score: calculateCosineSimilarity(item.title_vector, kwV)
                    };
                    log(`${item.id} | ${item.title} | ${newItem.score}`);
                    // 在finallList中找到这条，然后平均分下，如果没找到就添加
                    const finded = finalList.find(x => x.id === item.id);
                    if (finded) {
                        finded.score = (finded.score + newItem.score) / 2;
                    } else {
                        finalList.push(newItem);
                    }
                }
            }

            // 关键词查询
            {
                let kwList = [];
                let inputMessages = [
                    {
                        role: 'system', content: `角色定义：关键词生成助手\n\n`
                            + `工作要求：1. 根据输入内容生成关键词集合。2. 提炼关键词要注意多样化，例：大人票多少钱 -> 票，钱。`
                            + `3. 要有举一反三的意识，例如：大人票多少钱 -> 票，门票，票价，钱，价格，金子，价值。`
                            + `4. 尽量提炼出最短关键词，一个字也可以，最好不要超过两个字长度；5. 关键词尽可能提炼的多一些。\n\n`
                            + `回复要求：不要任何解释，就返回json格式的内容，例：["苹果","生梨"]，如果没有关键词就返回空数组，例：[]`
                    },
                    {role: 'user', content: kw},
                ];

                log(`使用模型“${self.cfg.kwModel}”生成关键词...`);
                let chatResult = await this._getChatModel().chat1(self.cfg.kwModel, inputMessages, opts);
                let reply_kw = chatResult.reply;
                log(`获取的回复：${reply_kw}`);
                kwList = JSON.parse(reply_kw);
                if (opts.exclude_kw != null && opts.exclude_kw.length > 0) {
                    kwList = kwList.filter(x => opts.exclude_kw.indexOf(x) === -1);
                    log(`排除后的关键词：${kwList.toString()}`);
                }

                if (kwList.length > 0) {
                    // 在标题中查关键词
                    {
                        const findList = await this.lanceDBMapper.searchMultiKwSync(conn, self._table_0,
                            'title', kwList, limit);
                        log(`根据标题关键词共找到 ${findList.length} 条（${kwList.toString()}）`);
                        for (const item of findList) {
                            const newItem = {
                                id: item.id,
                                title: item.title,
                                content: item.content,
                                score: calculateCosineSimilarity(item.title_vector, kwV)
                            };
                            log(`${item.id} | ${item.title} | ${newItem.score}`);
                            // 在finallList中找到这条，然后平均分下，如果没找到就添加
                            const finded = finalList.find(x => x.id === item.id);
                            if (finded) {
                                finded.score = (finded.score + newItem.score) / 2;
                            } else {
                                finalList.push(newItem);
                            }
                        }
                    }
                    // 在内容中查关键词
                    {
                        const findList = await this.lanceDBMapper.searchMultiKwSync(conn, self._table_0,
                            'content', kwList, limit);
                        log(`根据内容关键词共找到 ${findList.length} 条（${kwList.toString()}）`);
                        for (const item of findList) {
                            const newItem = {
                                id: item.id,
                                title: item.title,
                                content: item.content,
                                score: calculateCosineSimilarity(item.content_vector, kwV)
                            };
                            log(`${item.id} | ${item.title} | ${newItem.score}`);
                            // 在finallList中找到这条，然后平均分下，如果没找到就添加
                            const finded = finalList.find(x => x.id === item.id);
                            if (finded) {
                                finded.score = (finded.score + newItem.score) / 2;
                            } else {
                                finalList.push(newItem);
                            }
                        }
                    }
                }
            }

            // 对 finalList 进行排序，根据score倒叙
            finalList.sort(function (b, a) {
                if (a.score > b.score) {
                    return 1;
                } else if (a.score < b.score) {
                    return -1;
                }
                return 0;
            });

            log(`最终合成后共 ${finalList.length} 条`);
            for (const item of finalList) {
                log(`${item.id} | ${item.title} | ${item.score}`);
            }

            return finalList;
        } catch (exc) {
            log(`searchSync异常 -> ${exc.message}`);
            throw exc;
        }
    }

    /**
     * 创建知识库
     * @param kbId
     * @returns {Promise<void>}
     */
    async createKBSync(kbId) {
        log(`创建知识库 ${kbId} ...`);
        const { fs } = this.ctx.imports;

        const self = this;
        let kbDirPath;

        // 创建目录
        {
            kbDirPath = self._getKbDirPath(kbId);
            // 检查目录是否存在，不存在则创建
            if (!fs.existsSync(kbDirPath)) {
                try {
                    // 使用{ recursive: true }来确保即使父目录不存在也能创建
                    fs.mkdirSync(kbDirPath, {recursive: true});
                    log(`知识库目录创建成功 ${kbId} ${kbDirPath}`);
                } catch (error) {
                    throw new Error(`创建知识库目录时出错 ${kbId} ${kbDirPath} -> ${error.message}`);
                }
            } else {
                log(`知识库目录已存在 ${kbId} ${kbDirPath}`);
            }
        }

        // 创建空表
        try {
            const conn = await this.lanceDBMapper.connectSync(kbDirPath);

            // 向量长度（每个模型不一样）
            let vectorListSize = 1536;
            // 大库表
            await self.lanceDBMapper.createEmptyTableSync(conn, self._table_0, [
                // 唯一ID
                {name: "id", type: "string"},
                // 标题
                {name: "title", type: "string"},
                // 内容
                {name: "content", type: "string"},
                // 是否为预设回答
                {name: "is_yshd", type: "string"},
                // 标题向量
                {name: "title_vector", type: "float32-array", listSize: vectorListSize},
                // 内容向量
                {name: "content_vector", type: "float32-array", listSize: vectorListSize},
            ]);
            // 小库表（预设回答）
            await self.lanceDBMapper.createEmptyTableSync(conn, self._table_yshd, [
                // 唯一ID
                {name: "id", type: "string"},
                // 标题
                {name: "title", type: "string"},
                // 内容
                {name: "content", type: "string"},
                // 标题向量
                {name: "title_vector", type: "float32-array", listSize: vectorListSize},
                // 内容向量
                {name: "content_vector", type: "float32-array", listSize: vectorListSize},
            ]);
            log(`知识库空表创建成功 ${kbId} ${kbDirPath}`);
        } catch (exc) {
            log(`知识库空表创建失败 ${kbId} ${kbDirPath} -> ${exc.message}`);
        }
    }

    /**
     * 更新知识碎片
     * @param kbId
     * @param row 数据，例：{ id, title, content, is_yshd: '1'|'0' }
     * @returns {Promise<void>}
     */
    async setKfSync(kbId, row) {
        log(`更新知识碎片 ${kbId} ...`);
        let embedModel = null;

        // 加载知识库参数
        const kbForm = this.ctx.shortTimeCacheService.getKbMapper().load(kbId);
        if (kbForm) {
            embedModel = kbForm.embed_model;
        }

        const self = this;
        const conn = await this._getConn(kbId);
        // const findList = await this.lanceDBMapper.getTableRows(conn, self._table_0, `id = "${row.id}"`);
        const ollamaMapper = self.ctx.shortTimeCacheService.getOllamaMapper();

        // 删除老的
        await this.deleteKfSync(kbId, row.id);

        // 添加新的
        // const titleEmbedResult = await ollamaMapper.embedSync(null, row.title);
        // const contentEmbedResult = await ollamaMapper.embedSync(null, row.content);
        const titleEmbedResult = await this._embedSync(row.title, embedModel);
        log(`向量化-标题 ${embedModel} ...（${titleEmbedResult.length}）`);
        const contentEmbedResult = await this._embedSync(row.content, embedModel);
        log(`向量化-内容 ${embedModel} ...（${contentEmbedResult.length}）`);

        // log(JSON.stringify(titleEmbedResult));
        // log(JSON.stringify(contentEmbedResult))

        // 往大库添加
        await this.lanceDBMapper.addRowsToTable(conn, self._table_0, [
            {
                id: row.id,
                title: row.title,
                content: row.content,
                // title_vector: titleEmbedResult.embeddings[0],
                // content_vector: contentEmbedResult.embeddings[0],
                title_vector: titleEmbedResult,
                content_vector: contentEmbedResult,
                is_yshd: row.is_yshd,
            }
        ]);

        if (row.is_yshd === '1') {
            // 往小库添加
            await this.lanceDBMapper.addRowsToTable(conn, self._table_yshd, [
                {
                    id: row.id,
                    title: row.title,
                    content: row.content,
                    // title_vector: titleEmbedResult.embeddings[0],
                    // content_vector: contentEmbedResult.embeddings[0],
                    title_vector: titleEmbedResult,
                    content_vector: contentEmbedResult,
                }
            ]);
        }
    }

    /**
     * 删除知识碎片
     * @param kbId
     * @param id
     * @returns {Promise<void>}
     */
    async deleteKfSync(kbId, id) {
        const self = this;

        const conn = await this._getConn(kbId);
        await this.lanceDBMapper.deleteRowInTable(conn, self._table_0, `id = "${id}"`);
        await this.lanceDBMapper.deleteRowInTable(conn, self._table_yshd, `id = "${id}"`);
    }

    /**
     * 清空知识碎片
     * @param kbId
     * @returns {Promise<void>}
     */
    async clearKfSync(kbId) {
        const self = this;
        const conn = await this._getConn(kbId);
        await this.lanceDBMapper.clearRowsInTable(conn, self._table_0);
    }

    /**
     * 向量化内容
     * @param text
     * @returns {Promise<void>}
     * @private
     */
    async _embedSync(text, model) {
        const self = this;
        if (model == null) {
            model = self.cfg.embedModel;
        }
        log(`请求向量化 ${model} ...`);

        if (text.length > 7000) {
            throw new Error(`输入字数太多`);
        }

        const r = await self._getChatModel().embed(model, text);
        if (r.success === false) {
            throw new Error(r.msg)
        }

        if (r.embedding.length < 1536) {
            log(`补全向量数据 ...`)
            while(r.embedding.length < 1536) {
                r.embedding.push(0)
            }
        }

        return r.embedding;

        // if (this.cfg.embedModelPlatform === 'openai') {
        //     const openAIBrainWildMapper = self.ctx.shortTimeCacheService.getOpenAIBrainWildMapper();
        //     const chatRes = await openAIBrainWildMapper.embedSync(self.cfg.embedModel, text);
        //     return chatRes.data[0].embedding;
        // } else if (this.cfg.embedModelPlatform === 'ollama') {
        //     const ollamaMapper = self.ctx.shortTimeCacheService.getOllamaMapper();
        //     const embedRes = await ollamaMapper.embedSync(self.cfg.embedModel, text);
        //     const t = embedRes.embeddings[0];
        //     return t;
        // }
    }

    async _getConn(kbId) {
        return this.lanceDBMapper.connectSync(this._getKbDirPath(kbId));
    }

    _getKbDirPath(kbId) {
        const { path } = this.ctx.imports;
        return path.resolve().concat("/data/cs-mini-kb/kb/").concat(`${kbId}/`);
    }

    _getChatModel() {
        return this.ctx.shortTimeCacheService.getChatModel();
    }
}