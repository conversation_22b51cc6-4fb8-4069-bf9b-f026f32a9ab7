import {reqPostJsonSync} from "../../../util/http-util";
import CSAUI6JTDataSourceMapper from "../../cs-data-source/mapper/CSAUI6JTDataSourceMapper";
import {toDateTimeStr} from "../../../util/time-util";

export default class KbDataSourceService {
    constructor(ctx) {
        this.ctx = ctx;
        this._debug = true;
    }

    async req(ds, cmd, pars) {
        if (ds.url == null) {
            log(`ds.url is null -> ${JSON.stringify(ds)}`)
            return {
                success: false,
                msg: `ds.url is null`
            }
        }
        else {
            const url = `${ds.url}${cmd}`
            if (this._debug) log(`【ds-mapper】请求 ${url} ...`)
            return await reqPostJsonSync(`${url}`, {
                ...pars
            })
        }
    }

    /**
     * 获取我能访问的知识库列表
     * @param dsl
     * @param params
     * @returns {Promise<void>}
     */
    async getMyAccessKBList(dsl, params) {
        const list = await this.ctx.shortTimeCacheService.getDataSourceService().getExtAppDataList(dsl, {
            appKey: 'zhai_zsk',
            noSelfLimit: true,
            stateKey: 'sys-all',
            values: {
                access_users: dsl.user.id,
            },
            designs: {
                access_users: {
                    matchMode: 1
                }
            }
        });

        const list_final = list.map((n) => {
            var sObj = JSON.parse(n.search);
            return {
                id: n.id,
                name: sObj.name,
            }
        });

        const list_final1 = await this.getMyManageKBList(dsl, params);
        list_final1.forEach((n) => {
            if (list_final.find((a) => a.id === n) == null) {
                list_final.push(n);
            }
        })

        return list_final;
    }

    /**
     * 获取我管理的知识库列表
     * @param dsl
     * @param params
     * @returns {Promise<void>}
     */
    async getMyManageKBList(dsl, params) {
        const list = await this.ctx.shortTimeCacheService.getDataSourceService().getExtAppDataList(dsl, {
            appKey: 'zhai_zsk',
            noSelfLimit: true,
            stateKey: 'sys-all',
            values: {
                other_mgrs: dsl.user.id,
            },
            designs: {
                other_mgrs: {
                    matchMode: 1
                }
            }
        });

        const list1 = await this.ctx.shortTimeCacheService.getDataSourceService().getExtAppDataList(dsl, {
            appKey: 'zhai_zsk',
            noSelfLimit: true,
            stateKey: 'sys-all',
            values: {
                create_user_id: dsl.user.id,
            },
            designs: {
                create_user_id: {
                    isBuildInField: true
                }
            }
        });
        list1.forEach((n) => {
            if (list.find((a) => a.id === n) == null) {
                list.push(n);
            }
        })

        return list.map((n) => {
            var sObj = JSON.parse(n.search);
            return {
                id: n.id,
                name: sObj.name,
            }
        });
    }

    /**
     * 推送问答历史
     * @param ds
     * @param token
     * @param params kbId, question, answer, ip, datetime
     */
    async pushQAHistory(dsl, params) {
        if (params.datetime == null) {
            params.datetime = toDateTimeStr(new Date());
        }

        const searchObj = {
            kb_id: params.kbId,
            question: params.question,
            ip: params.ip,
            datetime: params.datetime,
        };

        const contentObj = {
            ...searchObj,
            answer: params.answer,
        };

        await this.ctx.shortTimeCacheService.getDataSourceService().saveExtAppDataForm(dsl, {
            updType: 0,
            form: {
                appKey: 'zhai_lswd',
                search: JSON.stringify(searchObj),
                content: JSON.stringify(contentObj),
            }
        });
    }
}