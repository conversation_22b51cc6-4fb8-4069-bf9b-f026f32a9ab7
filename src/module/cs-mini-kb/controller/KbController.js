import TopController from "../../../controller/TopController";

export class KbController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;

        /**
         *
         * @param {*} req.body.kbId 知识库id
         */
        app.post('/api/cmk/kb/get-form', async function (req, res) {
            const kbId = req.body.kbId;

            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cmk/kb/get-form', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                const kbForm = self.ctx.shortTimeCacheService.getKbMapper().load(kbId);
                res.send({ success: true, data: kbForm });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         * 获取我能访问的知识库列表
         */
        app.post('/api/cmk/kb/get-my-access-kb-list', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cmk/kb/get-my-access-kb-list', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const data = await self.ctx.shortTimeCacheService.getKbDataSourceService().getMyAccessKBList(dsl, {
                });
                res.send({ success: true, data });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         * 获取我管理的知识库列表
         */
        app.post('/api/cmk/kb/get-my-manage-kb-list', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cmk/kb/get-my-manage-kb-list', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                const data = await self.ctx.shortTimeCacheService.getKbDataSourceService().getMyManageKBList(dsl, {
                });
                res.send({ success: true, data });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         *
         * @param {*} req.body.kbId 知识库id
         * @param {*} req.body.question 问题
         * @param {*} req.body.answer 回答
         * @param {*} req.body.ip IP
         * @param {*} req.body.datetime 时间
         */
        app.post('/api/cmk/kb/push-qa-history', async function (req, res) {
            const loginService = self.ctx.shortTimeCacheService.getLoginService()
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cmk/kb/push-qa-history', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                const dsl = loginService.getDataSourceLogin(validateLoginResult.user)
                // ...
                await self.ctx.shortTimeCacheService.getKbDataSourceService().pushQAHistory(dsl, {
                    kbId: req.body.kbId,
                    question: req.body.question,
                    answer: req.body.answer,
                    ip: req.body.ip,
                    datetime: req.body.datetime,
                });
                res.send({ success: true });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });
    }
}