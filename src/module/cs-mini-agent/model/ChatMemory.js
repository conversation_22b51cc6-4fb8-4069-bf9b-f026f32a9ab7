/**
 * 聊天记忆
 */
export default class ChatMemory {
    constructor(ctx) {
        this.ctx = ctx;
        this.maxHistoryLength = 20;
        this.chatHistory = [];
    }

    getHistoryList() {
        return this.chatHistory;
    }

    addHistoryItem(item) {
        // 插入到第一个位置
        this.chatHistory.splice(0, null, item);
        // 超过最大长度，删除最旧的
        for (let i = 0; i < 10; i++) {
            if (this.chatHistory.length > this.maxHistoryLength) {
                this.chatHistory.shift();
            }
            else {
                break;
            }
        }
    }

    clearHistory() {
        this.chatHistory = [];
    }
}