import TopAgentModel from "../TopAgentModel";
import ChatModelAgentModel from "./ChatModelAgentModel";
import {UserCreateAgentMapper} from "../../mapper/UserCreateAgentMapper";

/**
 * 与UCA聊天（与用户自己建的智能体聊天）
 */
export class ChatUCAModel extends ChatModelAgentModel{
    constructor(ctx, opts) {
        super(ctx, opts);

        this.id = 'chat-uca-model';
        this.name = '与用户自定义模型对话';
        this.summary = '个性化，随意聊天，支持多种模型';
    }

    async _sendMessageBefore(message, opts = {}) {
        if (opts == null) opts = {};
        let model = opts.model;

        // sac id username realname roles
        const loginUser = opts.loginUser;
        const sessionId = opts.sessionId;

        // console.log(JSON.stringify(message))
        // console.log(JSON.stringify(opts))

        const messages = [];
        if (opts.chatHistory) {
            // 如果最后一条消息是用户发送的，则删除最后一条消息
            if (opts.chatHistory.length > 0) {
                if (opts.chatHistory[opts.chatHistory.length - 1].role === 'user') {
                    opts.chatHistory.pop();
                }
            }

            for (const i in opts.chatHistory) {
                const item = opts.chatHistory[i];
                messages.push({ role: item.role, content: item.content });
            }
            delete opts.chatHistory;
        }

        messages.push({ role: 'user', content: message });

        await this._setUserCustomPrompt(messages, loginUser.sac, loginUser.username, sessionId);

        log('Messages: ', JSON.stringify(messages));
        // log('ChatHistory: ', JSON.stringify(opts.chatHistory));

        return {
            model,
            messages,
        };
    }

    /**
     * 设置用户自定义提示词
     * @param messages
     * @param sac
     * @param username
     * @param agentId
     * @returns {Promise<void>}
     * @private
     */
    async _setUserCustomPrompt(messages, sac, username, agentId) {
        const userCreateAgentMapper = new UserCreateAgentMapper(this.ctx);
        const form = await userCreateAgentMapper.getMyAgentForm(sac, username, agentId);
        // console.log(`myAgentForm(${sac}|${username}|${agentId}): ${JSON.stringify(form)}`)
        if (form && form.systemPrompt && form.systemPrompt.length > 0) {
            let findSystemPrompt = false;

            for (const message of messages) {
                if (message.role === 'system') {
                    findSystemPrompt = true;
                    message.content = form.systemPrompt;
                    break;
                }
            }

            if (!findSystemPrompt) {
                messages.splice(0, 0, { role: 'system', content: form.systemPrompt });
            }
        }
    }
}