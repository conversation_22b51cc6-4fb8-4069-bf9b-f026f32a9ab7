import { parseErrorMsg } from "../../../../util/error-util.js";
import TopAgentModel from "../TopAgentModel.js";
/**
 * 聊天智能体（用于直接和各种模型对话）
 */
export default class ChatModelAgentModel extends TopAgentModel {
    constructor(ctx, opts) {
        super(ctx, opts);

        this.id = 'chat-model';
        this.name = '与模型对话';
        this.summary = '随意聊天，支持多种模型';
    }

    /**
     * 清理 opts
     * @param sendType
     * @param opts
     * @private
     */
    _clearOpts(sendType, opts) {
        // if (sendType === 'stream') {
        // }

        delete opts.model;
        delete opts.toolModel;
        delete opts.loginUser;
        delete opts.sessionId;
    }

    /**
     * 与智能体聊天
     * @param {String} message 消息
     * @param {*} opts model, chatHistory, attachments, tools, loginUser
     * @param {*} opts.chatHistory [{role: 'system' | 'user' | 'assistant', content: '消息内容'}]
     * @returns {Promise<Object>} 思考结果
     */
    async sendMessage(message, opts = {}) {
        const self = this;
        let debug = true;

        // if (debug && (opts.logLevel == null || opts.logLevel >= 3)) log(`opts: ${JSON.stringify(opts)}`)

        // 统计消耗
        let usageTotal = 0;
        let usageInput = 0;
        let usageOutput = 0;

        try {
            const loginUser = opts.loginUser;
            const { model, messages } = await this._sendMessageBefore(message, opts);

            let tools = opts.tools;
            let sessionId = opts.sessionId;

            let tool_model = model;
            let chat_model = model;

            // reply, id, time, usage, tool_calls: []
            let chatResult;
            let chatError;
            let newMessageId;

            if (opts.chatModel && opts.chatModel.length > 0) {
                chat_model = opts.chatModel;
            }
            if (opts.toolModel && opts.toolModel.length > 0) {
                tool_model = opts.toolModel;
            }
            if (debug && (opts.logLevel == null || opts.logLevel >= 3)) log(`tools: ${JSON.stringify(tools)}`)

            this._clearOpts('', opts);

            const chatOpts = {
                attachments: opts.attachments,
                tools: opts.tools,
            };

            if (tools && tools.length > 0) {
                if (debug && (opts.logLevel == null || opts.logLevel >= 1)) log(`请求模型识别工具调用“${tool_model}”...`);
                await this._buildSystemMessageBeforeChat(messages, 'tool', opts);
                chatResult = await this._getChatModel().chat(tool_model, messages, null, (err) => {
                    chatError = err;
                }, chatOpts);
                newMessageId = Date.now().toString();
                // 计算token消耗
                if (chatResult.usage) {
                    if (chatResult.usage.total) {
                        usageTotal += chatResult.usage.total;
                    }
                    if (chatResult.usage.input) {
                        usageInput += chatResult.usage.input;
                    }
                    if (chatResult.usage.output) {
                        usageOutput += chatResult.usage.output;
                    }
                }

                if (chatResult.tool_calls && chatResult.tool_calls.length > 0) {
                    let findTool = false;
                    if (debug && (opts.logLevel == null || opts.logLevel >= 2)) log(`【调用工具】共${chatResult.tool_calls.length}个 | ${JSON.stringify(chatResult.tool_calls)}`);

                    let msg_bot = `（下面内容不在对话中显示）\n`;
                    let msg_usr = '（下面内容不在对话中显示）\n';
                    for (let i in chatResult.tool_calls) {
                        const tool_call = chatResult.tool_calls[i];
                        let args = tool_call.args;
                        if (typeof (args) === 'string') {
                            args = JSON.parse(args);
                        }

                        if (!(await self._canUseTool(tool_call, loginUser, tools))) {
                            if (debug && (opts.logLevel == null || opts.logLevel >= 1)) log(`${loginUser.realname} 无权调用工具 ${tool_call.name}`);
                            return {
                                reply: `你没有此权限`,
                                id: Date.now().toString(),
                                time: Date.now(),
                                usage: {
                                    total: usageTotal,
                                    input: usageInput,
                                    output: usageOutput
                                },
                            };
                        }

                        const toolResult = await this.ctx.csMiniAgentService.agentService.execScript_callTool({
                            agent: this,
                            name: tool_call.name,
                            params: args,
                            tools,
                        }, {
                            debug,
                            loginUser,
                            sessionId,
                            message,
                        });

                        if (toolResult) {
                            findTool = true;
                            // 对象形态
                            if (typeof (toolResult) === 'object') {
                                // 直接回复内容
                                if (toolResult.reply && toolResult.reply.length > 0) {
                                    return {
                                        reply: toolResult.reply,
                                        id: Date.now().toString(),
                                        time: Date.now(),
                                        usage: {
                                            total: usageTotal,
                                            input: usageInput,
                                            output: usageOutput
                                        },
                                    };
                                }
                                else {
                                    return {
                                        success: false,
                                        reply: '没有实现的回复逻辑',
                                        id: Date.now().toString(),
                                        time: Date.now(),
                                        usage: {
                                            total: usageTotal,
                                            input: usageInput,
                                            output: usageOutput
                                        },
                                    };
                                }
                            }
                            // 字符串形态
                            else {
                                msg_bot += `调用工具“${tool_call.name}”\n`;

                                msg_usr += `### 下面是调用工具“${tool_call.name}”后返回的内容\n`;
                                msg_usr += toolResult;
                                msg_usr += '\n\n';
                            }
                        }
                    }

                    // 工具调用完毕，开始回答
                    if (findTool) {
                        messages.push({ role: 'assistant', content: msg_bot });
                        messages.push({ role: 'user', content: msg_usr });

                        if (debug && (opts.logLevel == null || opts.logLevel >= 1)) log(`请求模型进行回答a“${chat_model}”...`);
                        delete chatOpts.tools;
                        await this._buildSystemMessageBeforeChat(messages, 'chat', opts);
                        chatResult = await this._getChatModel().chat(chat_model, messages, null, (err) => {
                            chatError = err;
                        }, chatOpts);
                        chatResult.id = newMessageId;
                        // 计算token消耗
                        if (chatResult.usage) {
                            if (chatResult.usage.total) {
                                usageTotal += chatResult.usage.total;
                            }
                            if (chatResult.usage.input) {
                                usageInput += chatResult.usage.input;
                            }
                            if (chatResult.usage.output) {
                                usageOutput += chatResult.usage.output;
                            }
                        }
                    }
                }
            }
            else {
                if (debug && (opts.logLevel == null || opts.logLevel >= 1)) log(`请求模型进行回答b“${chat_model}”...`);
                await this._buildSystemMessageBeforeChat(messages, 'chat', opts);
                chatResult = await this._getChatModel().chat(chat_model, messages, null, (err) => {
                    chatError = err;
                }, chatOpts);
                newMessageId = Date.now().toString();
                // 计算token消耗
                if (chatResult.usage) {
                    if (chatResult.usage.total) {
                        usageTotal += chatResult.usage.total;
                    }
                    if (chatResult.usage.input) {
                        usageInput += chatResult.usage.input;
                    }
                    if (chatResult.usage.output) {
                        usageOutput += chatResult.usage.output;
                    }
                }
            }

            chatResult.id = newMessageId;
            chatResult.time = Date.now();

            if (chatError) {
                chatResult.success = false;
                chatResult.reply = parseErrorMsg(chatError);
            }

            await self._sendMessageEndBefore({
                chatError, chatResult,
            });

            return chatResult;
        } catch (exc) {
            return {
                success: false,
                reply: `出错了：${exc.message}`,
                id: Date.now().toString(),
                time: Date.now(),
                usage: {
                    total: usageTotal,
                    input: usageInput,
                    output: usageOutput
                },
            };
        }
    }

    /**
     *
     * @param {*} message
     * @param {*} onData
     * @param {*} onEnd
     * @param {*} onError
     * @param {*} opts model, chatHistory, attachments, tools, loginUser
     * @returns
     */
    async sendMessageStream(message, onData, onEnd, onError, opts = {}) {
        const self = this;
        let debug = true;

        let debug_opts = {
            ...opts
        };
        debug_opts.loginUser = null;

        log(`【调试】opts: ${JSON.stringify(debug_opts)}`)

        const loginUser = opts.loginUser;
        const { model, messages } = await this._sendMessageBefore(message, opts);
        let tools = opts.tools;


        let sessionId = opts.sessionId;
        // 需要确认任务是否完成
        let needConfirmTaskIfIsDone = false;

        let tool_model = model;
        let chat_model = model;

        // reply, id, time, usage, tool_calls: []
        let chatResult;
        let chatError;

        if (opts.chatModel && opts.chatModel.length > 0) {
            chat_model = opts.chatModel;
        }
        if (opts.toolModel && opts.toolModel.length > 0) {
            tool_model = opts.toolModel;
        }
        if (opts.needConfirmTaskIfIsDone) {
            needConfirmTaskIfIsDone = opts.needConfirmTaskIfIsDone;
        }

        this._clearOpts('', opts);

        const chatOpts = {
            temperature: opts.temperature,
            attachments: opts.attachments,
            tools: opts.tools,
        };

        let optsProps = 'opts属性：';
        if (opts) {
            for (const i in opts) {
                optsProps += i + '; ';
            }
        }

        while (true) {
            if (tools && tools.length > 0) {
                try {
                    if (debug && (opts.logLevel == null || opts.logLevel >= 1)) log(`请求模型识别工具调用【stream】“${tool_model}”...`);
                    chatOpts.tools = tools;
                    await this._buildSystemMessageBeforeChat(messages, 'tool', opts);
                    log(`【调试】${JSON.stringify(messages)}`)
                    chatResult = await this._getChatModel().chat(tool_model, messages, null, (err) => {
                        chatError = err;
                    }, chatOpts);

                    if (chatError) {
                        if (onError) onError(chatError);
                        return;
                    }

                    if (chatResult.tool_calls && chatResult.tool_calls.length > 0) {
                        let findTool = false;
                        if (debug && (opts.logLevel == null || opts.logLevel >= 2)) log(`【调用工具】共${chatResult.tool_calls.length}个 | ${JSON.stringify(chatResult.tool_calls)}`);

                        let msg_bot = `（下面内容不在对话中显示）\n`;
                        let msg_usr = '（下面内容不在对话中显示）\n';
                        for (let i in chatResult.tool_calls) {
                            const tool_call = chatResult.tool_calls[i];
                            let args = tool_call.args;
                            if (typeof (args) === 'string') {
                                args = JSON.parse(args);
                            }

                            if (!(await self._canUseTool(tool_call, loginUser, tools))) {
                                if (debug && (opts.logLevel == null || opts.logLevel >= 1)) log(`${loginUser.realname} 无权调用工具 ${tool_call.name}`);
                                const buffer = Buffer.from(`你没有此权限`, 'utf8');
                                if (onData) onData(buffer);
                                onEnd();
                                return null;
                            }

                            if (debug && (opts.logLevel == null || opts.logLevel >= 1)) log(`【调用工具】${tool_call.name} ...`);
                            const toolResult = await this.ctx.csMiniAgentService.agentService.execScript_callTool({
                                agent: this,
                                name: tool_call.name,
                                params: args,
                                tools,
                            }, {
                                debug,
                                loginUser,
                                sessionId,
                                message,
                                outputState: function (str) {
                                    const buffer = Buffer.from(
                                        `<cs-ai-reply-cmd style="display: none;">${JSON.stringify({ type: 'set-think-state', value: str })}</cs-ai-reply-cmd>`, 'utf8');
                                    if (onData) onData(buffer);
                                }
                            });

                            if (toolResult) {
                                findTool = true;
                                // 对象形态
                                if (typeof (toolResult) === 'object') {
                                    // 直接回复内容
                                    if (toolResult.reply && toolResult.reply.length > 0) {
                                        const buffer = Buffer.from(toolResult.reply, 'utf8');
                                        if (onData) onData(buffer);
                                        if (toolResult.continue) {
                                            // 继续对话
                                            msg_bot += `调用工具“${tool_call.name}”成功\n`;

                                            if (toolResult.reply && toolResult.reply.length > 0 && toolResult.replyToAgent !== false) {
                                                msg_usr += `### 下面是调用工具“${tool_call.name}”后返回的内容\n`;
                                                if (toolResult.replyToAgent && toolResult.replyToAgent.length > 0) {
                                                    msg_usr += toolResult.replyToAgent;
                                                }
                                                else {
                                                    msg_usr += toolResult.reply;
                                                }
                                                msg_usr += '\n\n';
                                            }
                                        }
                                        else {
                                            onEnd();
                                            return null;
                                        }
                                    }
                                    else {
                                        const buffer = Buffer.from('没有实现的回复逻辑', 'utf8');
                                        if (onData) onData(buffer);
                                        onEnd();
                                        return null;
                                    }
                                }
                                // 字符串形态
                                else {
                                    msg_bot += `调用工具“${tool_call.name}”成功\n`;

                                    msg_usr += `### 下面是调用工具“${tool_call.name}”后返回的内容\n`;
                                    msg_usr += toolResult;
                                    msg_usr += '\n\n';
                                }
                            }
                        }

                        // 工具调用完毕，开始回答
                        if (findTool) {
                            messages.push({ role: 'assistant', content: msg_bot });

                            // 确认任务状态
                            if (needConfirmTaskIfIsDone) {
                                let msg_usr1 = msg_usr + `


请确认当前任务状态：
- 如果任务完成就回复指令{<cs-ai-reply-cmd:task-done>}
- 如果任务未完成还要继续就回复指令{<cs-ai-reply-cmd:task-continue>}
- 如果未完成并且不知道该如何完成就回复指令{<cs-ai-reply-cmd:task-fail>}
- 不要有任何解释，只回复指令
                                    `
                                messages.push({ role: 'user', content: msg_usr1 });
                                if (debug && (opts.logLevel == null || opts.logLevel >= 2)) log(`请求模型确认任务状态【stream】“${tool_model}”...`);
                                delete chatOpts.tools;
                                await this._buildSystemMessageBeforeChat(messages, 'tool', opts);
                                chatResult = await this._getChatModel().chat(tool_model, messages, null, (err) => {
                                    chatError = err;
                                }, chatOpts);

                                if (debug && (opts.logLevel == null || opts.logLevel >= 2)) log(`确认任务状态回复内容：${chatResult.reply}`);
                                if (chatResult.reply === '{<cs-ai-reply-cmd:task-done>}') {
                                }
                                else if (chatResult.reply === '{<cs-ai-reply-cmd:task-continue>}') {
                                    continue;
                                }
                                else if (chatResult.reply === '{<cs-ai-reply-cmd:task-fail>}') {
                                }
                                messages.splice(messages.length - 1, 1);
                            }

                            messages.push({ role: 'user', content: msg_usr });

                            if (debug && (opts.logLevel == null || opts.logLevel >= 1)) log(`请求模型进行回答【stream】“${chat_model}”...`);
                            delete chatOpts.tools;
                            await this._buildSystemMessageBeforeChat(messages, 'chat', opts);
                            return await this._getChatModel().chatStream1(chat_model, messages, (chunk) => {
                                if (onData) onData(chunk);
                            }, onEnd, onError, chatOpts);
                        }
                        else {
                            if (debug && (opts.logLevel == null || opts.logLevel >= 2)) log('未能找到合适的工具！');
                            onData('未能找到合适的工具！');
                            onEnd();
                            return null;
                        }
                    }
                    else {
                        if (debug && (opts.logLevel == null || opts.logLevel >= 2)) log(`直接用流式回答...`);
                        const buffer = Buffer.from(chatResult.reply, 'utf8');
                        onData(buffer);
                        onEnd();
                        return null;
                    }
                } catch (exc) {
                    log(exc);
                    onData('内部逻辑发生异常！');
                    onEnd();
                    return null;
                }
            }
            else {
                if (debug && (opts.logLevel == null || opts.logLevel >= 1)) log(`请求模型进行回答【stream】“${chat_model}”...`);
                await this._buildSystemMessageBeforeChat(messages, 'chat', opts);
                return await this._getChatModel().chatStream1(chat_model, messages, onData, onEnd, onError, chatOpts);
            }
        }
    }

    /**
     * 能否使用此工具
     * @param tool
     * @param loginUser
     * @returns {boolean}
     * @private
     */
    async _canUseTool(callTool, loginUser, tools) {
        let findTool;
        for (const tool of tools) {
            if (tool.name === callTool.name) {
                findTool = tool;
                break;
            }
        }
        if (findTool && findTool.needRoles && findTool.needRoles.length > 0) {
            for (const role of findTool.needRoles) {
                if (await this.ctx.shortTimeCacheService.getLoginService().hasRole(loginUser, role)) {
                    return true;
                }
            }
            return false;
        }

        return true;
    }

    async _buildSystemMessageBeforeChat(messages, type, opts) {
        if (messages) {
            if (messages.length > 0 && messages[0].role === 'system') {
                messages[0].content = await this.ctx.csMiniAgentService.agentService.execScript_buildSystemMessageBeforeChat({
                    agent: this,
                    systemMessage: messages[0].content,
                    type,
                });
            }
        }
    }

    async _sendMessageBefore(message, opts = {}) {
        if (opts == null) opts = {};
        let model = opts.model;

        const messages = [];
        if (opts.chatHistory) {
            // 如果最后一条消息是用户发送的，则删除最后一条消息
            if (opts.chatHistory.length > 0) {
                if (opts.chatHistory[opts.chatHistory.length - 1].role === 'user') {
                    opts.chatHistory.pop();
                }
            }

            for (const i in opts.chatHistory) {
                const item = opts.chatHistory[i];
                messages.push({ role: item.role, content: item.content });
            }
            delete opts.chatHistory;
        }

        messages.push({ role: 'user', content: message });

        return {
            model,
            messages,
        };
    }

    async _sendMessageEndBefore(pars) {
        const { chatError, chatResult } = pars;

    }

    _getChatModel() {
        return this.ctx.shortTimeCacheService.getChatModel();
    }
}