import ChatModelAgentModel from "./ChatModelAgentModel";
import {toDateStr, toDateTimeStr, toTimeStr} from "../../../../util/time-util";

export class ChatGameAAgentModel extends ChatModelAgentModel {
    constructor(ctx, opts) {
        super(ctx, opts);

        this.id = 'chat-game-a';
        this.name = '聊天游戏A型';
        this.summary = 'AI扮演角色与玩家互动，通过交流找到答案';
    }

    async _sendMessageBefore(message, opts = {}) {
        const self = this;
        if (opts == null) opts = {};
        let model = opts.model;
        const ctx = this._getCtx();

        const messages = [];
        if (opts.chatHistory) {
            // 如果最后一条消息是用户发送的，则删除最后一条消息
            if (opts.chatHistory.length > 0) {
                if (opts.chatHistory[opts.chatHistory.length - 1].role === 'user') {
                    opts.chatHistory.pop();
                }
            }

            for (const i in opts.chatHistory) {
                const item = opts.chatHistory[i];
                messages.push({ role: item.role, content: item.content });
            }
            delete opts.chatHistory;
        }

        messages.splice(0, null, { role: 'system', content: this._getSystemPrompt(ctx, { message }) });
        messages.push({ role: 'user', content: message });

        opts.tools = this._getTools(ctx)

        // log('Messages: ', JSON.stringify(messages));
        // log('ChatHistory: ', JSON.stringify(opts.chatHistory));

        return {
            model,
            messages,
        };
    }

    _getCtx() {
        const self = this;
        return {
            timeUtil: {
                toDateTimeStr: toDateTimeStr,
                toDateStr: toDateStr,
                toTimeStr: toTimeStr,
            },
            getConfig() {
                return self.getSettings().config;
            },
            getService(name) {
                return self.ctx.shortTimeCacheService[`get${name}`]();
            },
        };
    }

    _getSystemPrompt(ctx, opts = {}) {
        let str = ``;

//         str += `
// ### 上下文
// #### 当前日期
// ${ctx.timeUtil.toDateStr(new Date())}
// #### 当前时间
// ${ctx.timeUtil.toTimeStr(new Date())}
// `;

        if (opts.message.startsWith('#评估调查笔记# ')) {
            str += `
### 任务说明
#### 角色要求
- 你是互动游戏店老板，你的店员将扮演各种角色来和玩家互动
- 你现在将评估客户（玩家）提交的调查笔记，你先根据笔记推断出肇事者是谁，在给笔记打个分数，满分是100分，如果推断错误就是0分
- 在评估调查笔记时尽可能的严格，要结合对话历史来判断

#### 回复要求
- 1、展示总分。2、列出评分明细。3、给出评语
`;
        }
        else {
            str += `
### 任务说明
#### 角色要求
- 你是互动游戏店老板，你的店员将扮演各种角色来和玩家互动，你负责操控你的店员告诉他们如何回复
- 你们的目的是让客户（玩家）玩的开心，配合引导客户走完游戏流程

#### 回复要求
- 不要有任何解释，就回复某个角色要说的话，返回形式
${"```"}
{角色名}：{角色说的话}
${"```"}
- 回复样例1
${"```"}
小明：xxx
${"```"}
`;
        }

        str += `
### 游戏说明
#### 游戏剧本介绍
- 名称：掉落的地球仪
- 概要：主角是某学校的学生，正好做好早操回到教师，当回到座位时手扶了下后桌，导致后桌晃动了一下，然后桌上的地球仪上的球体就掉落了下来，
咋看别人会以为是主角损坏了地球仪，但主角心里知道地球仪的质量不会差到桌子晃动一下就掉落的层度，肯定是之前就坏了然后被硬生生的装了回去。
- 当玩家艾特某人时表示对着某人说话，当艾特所有人时表示对着大家说


#### 游戏角色描述
##### 林小夏（地球仪的主人）
- 性格：内向敏感，容易激动，有轻微洁癖。
- 记忆/隐藏信息：1.昨天放学后她最后一个离开教室，但声称没碰过地球仪。2.她最近和班长陈明因班级事务有过争执。
- 关键线索：1.如果玩家质问“你昨天是不是最后一个走的？”，她会慌张否认。2.提到“陈明”时，她会突然沉默或转移话题。
##### 陈明（班长）
- 性格：表面正直，实则控制欲强，喜欢暗中操纵。
- 记忆/隐藏信息：1.昨天课后曾单独留在教室整理班费，但账目对不上。2.他讨厌林小夏，因为她曾公开质疑他的班长权威。
- 关键线索：1.如果玩家问“你昨天动过地球仪吗？”，他会反问：“你怀疑我？”（防御性强）。2.提到“班费”时，他会刻意强调“一切正常”。
##### 张浩（体育委员）
- 性格：大大咧咧，爱开玩笑，但观察力敏锐。
- 记忆/隐藏信息：1.昨天训练后回教室拿水壶时，看到陈明在摆弄地球仪。2.他以为陈明只是好奇，没多想。
- 关键线索：1.如果玩家问“昨天谁碰过地球仪？”，他会犹豫后说：“好像看到班长在玩……”。2.提到“林小夏”时，他会吐槽：“她最近老针对班长。”
##### 李老师（班主任）
- 性格：严肃但公正，讨厌班级纠纷。
- 记忆/隐藏信息：1.昨天放学后曾让陈明留下整理班费，但没注意地球仪。2.她知道林小夏和陈明有矛盾，但认为“只是小事”。
- 关键线索：1.如果玩家问“班费有问题吗？”，她会皱眉：“为什么问这个？”。2.提到“地球仪”时，她会说：“那个旧地球仪早该换了。”

`;

        str += `
#### 游戏剧情流程
${"```"}
玩家通过对话收集线索，最终推理出真相。

阶段1：冲突爆发
    场景：早操结束，玩家回到座位时不小心碰到林小夏的桌子，地球仪的球体掉落。
    林小夏（激动）：“你弄坏了我的地球仪！它昨天还好好的！”
    
    玩家选择：
    （道歉）：“对不起，我赔你一个。” → 林小夏不依不饶：“这不是钱的问题！”
    （质疑）：“我只是碰了下桌子，球怎么会掉？” → 林小夏愣住：“难道之前就坏了？”

阶段2：调查与对话
    玩家可自由询问其他角色，关键问题会影响他们的反应：
    
    询问张浩：
    “你昨天看到谁动过地球仪？” → 张浩透露看到班长在摆弄。
    
    质问陈明：
    “你是不是动过地球仪？” → 陈明态度强硬：“你有证据吗？”
    
    试探李老师：
    “班费最近有问题吗？” → 李老师警觉，暗示班费可能被挪用。

阶段3：真相揭露
    关键推理点：
    
    地球仪本身是旧的，球体早已松动。
    
    陈明昨天整理班费时，发现账目有问题，怕被发现，慌乱中碰掉了地球仪的球体，并勉强按回去。
    
    林小夏不知情，但她的敏感让陈明趁机嫁祸给玩家。

结局：
    如果玩家收集足够线索（如张浩的证言、班费异常），可当面对质陈明，迫使他承认。
    若失败，则玩家被冤枉赔钱，陈明继续掩盖班费问题。
${"```"}
`;

        return str;
    }

    _getTools(ctx) {
        return [
            // // 查询知识库
            // {
            //     name: 'searchKb',
            //     descr: '查询知识库，返回可能相关或相近的知识',
            //     params: {
            //         required: [],
            //         props: {
            //             query: {
            //                 type: 'string',
            //                 descr: '查询向量的源文本，根据用户对话意图生成一个用于查询的源文本',
            //             },
            //             kwList: {
            //                 type: 'string',
            //                 descr: `根据对话内容和用户查询意图生成的关键词列表，例：{["关键词1", "关键词2"]}\n\n`
            //                     + `关键词要求：\n`
            //                     + `1. 使用json格式\n`
            //                     + `2. 根据输入内容生成关键词集合。\n`
            //                     + `3. 提炼关键词要注意多样化，例：大人票多少钱 -> 票，钱。\n`
            //                     + `4. 要有举一反三的意识，例如：大人票多少钱 -> 票，门票，票价，钱，价格，金子，价值。\n`
            //                     + `5. 尽量提炼出最短关键词，一个字也可以，最好不要超过两个字长度；6. 关键词尽可能提炼的多一些。\n`
            //             }
            //         }
            //     },
            //     call: async function (params) {
            //         let result;
            //         return result;
            //     }
            // }
        ]
    }

}