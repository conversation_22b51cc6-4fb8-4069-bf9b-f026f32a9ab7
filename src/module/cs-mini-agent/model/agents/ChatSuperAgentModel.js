import TopAgentModel from "../TopAgentModel";
import {parseErrorMsg} from "../../../../util/error-util";

/**
 * 超级智能体
 */
export class ChatSuperAgentModel extends TopAgentModel {
    constructor(ctx, opts) {
        super(ctx, opts);

        this.id = 'chat-super';
        this.name = '超级智能体';
        this.summary = '比一般智能体有更高的智慧';
    }

    /**
     * 与智能体聊天
     * @param {String} message 消息
     * @param {*} opts model, chatHistory, attachments, tools, loginUser
     * @param {*} opts.chatHistory [{role: 'system' | 'user' | 'assistant', content: '消息内容'}]
     * @returns {Promise<Object>} 思考结果
     */
    async sendMessage(message, opts = {}) {
        const self = this;
        // log(`${JSON.stringify(opts.chatHistory)}`)

        const logLevel = opts.logLevel;
        const loginUser = opts.loginUser;
        let sessionId = opts.sessionId;
        const localCtx = {
            logLevel,
            onProgressChange: opts.onProgressChange,
        };

        const { model } = await this._sendMessageBefore(message, opts, localCtx);

        let tool_model = model;
        let chat_model = model;

        if (opts.chatModel && opts.chatModel.length > 0) {
            chat_model = opts.chatModel;
        }
        if (opts.toolModel && opts.toolModel.length > 0) {
            tool_model = opts.toolModel;
        }

        let t_msg = message;

        const chatHistory = [];

        if (opts.chatHistory && opts.chatHistory.length > 0) {
            if (opts.chatHistory.length >= 2) {
                chatHistory.push(...opts.chatHistory.slice(-2));
            }
            // // 根据对话历史生成提问
            // const gbchResult = await this._generateByChatHistory(tool_model, message, opts.chatHistory, opts, localCtx);
            // if (localCtx.logLevel == null || localCtx.logLevel >= 2) log(`【调试】生成用户问题：${gbchResult.reply}`)
            // if (gbchResult.reply === 'NO_CHANGE') {
            //     t_msg = message;
            // }
            // else {
            //     t_msg = gbchResult.reply
            // }
        }
        if (localCtx.logLevel == null || localCtx.logLevel >= 3) log(`【调试】对话历史：${JSON.stringify(chatHistory)}`)

        t_msg = `用户说：${t_msg}（任务开始）`
//         const gtlResult = await this._generateTodoList(tool_model, `${t_msg}`, chatHistory, opts, localCtx);
//         if (gtlResult.reply === 'NO_TODO_LIST') {
//         }
//         else {
//             if (localCtx.logLevel == null || localCtx.logLevel >= 2) log(`【调试】生成待办事项列表：${gtlResult.reply}`)
//             chatHistory.push({ role: 'user', content: t_msg });
//             chatHistory.push({ role: 'assistant', content: gtlResult.reply });
//             t_msg = `（下面是系统消息，不会在对话中显示）
// 继续任务`;
//         }

        while (true) {
            // ...
            const chatNodeResult = await this._chatNode(tool_model, `${t_msg}`, chatHistory, opts, localCtx);
            if (chatNodeResult.replyAndEnd) {
                return {
                    reply: chatNodeResult.replyAndEnd
                }
            }
            // ...
            const checkIfTaskIsEndResult = await this._checkIfTaskIsEnd(tool_model, chatHistory, opts, localCtx);
            if (checkIfTaskIsEndResult) {
                return this._lastChat(chat_model, chatHistory, opts, localCtx);
            }
            // ...
            t_msg = `（下面是系统消息，不会在对话中显示）
继续任务`;
        }
    }

    async _generateTodoList(model, message, chatHistory, opts = {}, localCtx = {}) {
        const tools = this._getTooles(localCtx);
        const chatOpts = {
            attachments: opts.attachments,
            tools: tools,
            logLevel: opts.logLevel,
        };

        const messages = [
            { role: 'system', content: `### 任务说明
#### 角色要求
- 你现在是安排任务的专家，你不亲自操作任务，但是你会生成一个带有打勾框的待办事项列表让别人执行

#### 回复要求
- 不要有任何解释，就回复内容
- 不要调用工具，只能回复文本内容
- 如果回复待办事项列表就必须用markdown格式回复
- 如果当前任务很简单，不需要生成待办事项列表，就回复：NO_TODO_LIST
` }
        ];
        if (chatHistory.length > 0) {
            messages.push(...chatHistory);
        }
        messages.push({ role: 'user', content: message });

        let chatError;
        const chatResult = await this._getChatModel().chat(model, messages, null, (err) => {
            chatError = err;
        }, chatOpts);

        if (chatError) {
            // chatResult.success = false;
            // chatResult.reply = parseErrorMsg(chatError);
            throw new Error(parseErrorMsg(chatError));
        }

        log(`生成ToDoList结果：${JSON.stringify(chatResult)}`)

        return chatResult;
    }

    async _generateByChatHistory(model, message, chatHistory, opts = {}, localCtx = {}) {
        const chatOpts = {
            attachments: opts.attachments,
            // tools: tools,
            logLevel: opts.logLevel,
        };

        const messages = [
            { role: 'system', content: `### 任务说明
#### 角色要求
- 判断输入内容是否和之前的对话有连续性

#### 回复要求
- 不要有任何解释，就回复：YES | NO
- 有连续性就回复：YES
- 没有连续性回复：NO
` }
        ];
        if (chatHistory.length > 0) {
            messages.push(...chatHistory);
        }
        messages.push({ role: 'user', content: message });

        let chatError;
        const chatResult = await this._getChatModel().chat(model, messages, null, (err) => {
            chatError = err;
        }, chatOpts);
        return chatResult;
    }

    async _chatNode(model, message, chatHistory, opts = {}, localCtx = {}) {
        const tools = this._getTooles(localCtx);
        const loginUser = opts.loginUser;
        let sessionId = opts.sessionId;
        const chatOpts = {
            attachments: opts.attachments,
            tools: tools,
            logLevel: localCtx.logLevel,
        };

        const messages = [
            { role: 'system', content: this.getShellSystemPrompt() },
        ];
        if (chatHistory.length > 0) {
            messages.push(...chatHistory);
        }
        messages.push({ role: 'user', content: message });


        let chatError;
        const chatResult = await this._getChatModel().chat(model, messages, null, (err) => {
            chatError = err;
        }, chatOpts);
        if (chatResult.tool_calls && chatResult.tool_calls.length > 0) {
            if (localCtx.logLevel == null || localCtx.logLevel >= 3) log(`发现工具调用共 ${chatResult.tool_calls.length} 个`)
            for (let i in chatResult.tool_calls) {
                const tool_call = chatResult.tool_calls[i];
                let args = tool_call.args;
                if (typeof (args) === 'string') {
                    args = JSON.parse(args);
                }

                const findTool = tools.find((tool) => {
                    return tool.name === tool_call.name;
                });
                if (localCtx.logLevel == null || localCtx.logLevel >= 2) log(`【调试】调用工具 ${findTool.name} ...`)
                const toolResult = await this.ctx.csMiniAgentService.agentService.execScript_callTool({
                    agent: this,
                    name: tool_call.name,
                    params: args,
                    tools,
                }, {
                    loginUser,
                    sessionId,
                });

                if (toolResult) {
                    if (tool_call.name === 'replyAndEnd') {
                        return {
                            replyAndEnd: toolResult.reply
                        };
                    }
                    chatHistory.push({ role: 'user', content: message });
                    chatHistory.push({ role: 'assistant', content: toolResult.reply });
                }
            }
        }

        return chatResult;
    }

    async _checkIfTaskIsEnd(model, chatHistory, opts = {}, localCtx = {}) {
        const chatOpts = {
            attachments: opts.attachments,
            // tools: tools,
            logLevel: opts.logLevel,
        };

        const messages = [
            { role: 'system', content: `
### 任务说明
#### 角色要求
- 你现在是任务审核专家，负责审核任务完成情况

#### 回复要求
- 如果觉得任务已完成了就只回复：TASK_DONE（不要有任何解释，就回复内容）
- 如果觉得任务没有完成就解释为何这么判断
` },
        ];
        if (chatHistory.length > 0) {
            messages.push(...chatHistory);
        }
        messages.push({ role: 'user', content: `（下面是系统消息，不会在对话中显示）
判断当前任务是否完成
` });

        let chatError;
        const chatResult = await this._getChatModel().chat(model, messages, null, (err) => {
            chatError = err;
        }, chatOpts);
        if (chatResult.reply === 'TASK_DONE') {
            if (localCtx.logLevel == null || localCtx.logLevel >= 2) log(`【调试】判定任务完成`)
            return true;
        }
        // else if (chatResult.reply === 'TASK_NOT_DONE') {
        //     if (localCtx.logLevel == null || localCtx.logLevel >= 3) log(`【调试】判定任务还没有完成`)
        //     return false;
        // }
        else {
            if (localCtx.logLevel == null || localCtx.logLevel >= 2) log(`【调试】判定任务是否完成：${chatResult.reply}`)
            chatHistory.push({ role: 'user', content: `（下面是系统消息，不会在对话中显示）
判断当前任务是否完成
` });
            chatHistory.push({ role: 'assistant', content: chatResult.reply });
            return false;
        }
    }

    async _lastChat(model, chatHistory, opts = {}, localCtx = {}) {
        const chatOpts = {
            attachments: opts.attachments,
            // tools: tools,
            logLevel: opts.logLevel,
        };

        const messages = [
            { role: 'system', content: `
### 任务说明
#### 角色要求
- 你现在是一个公司的老板，你可以指挥员工来帮助客户完成各种工作

${this.getShellSystemPrompt()}

` },
        ];
        if (chatHistory.length > 0) {
            messages.push(...chatHistory);
        }
        messages.push({ role: 'user', content: `（下面是系统消息，不会在对话中显示）
任务已完成
` });

        let chatError;
        const chatResult = await this._getChatModel().chat(model, messages, null, (err) => {
            chatError = err;
        }, chatOpts);
        return chatResult;
    }

    _getChatModel() {
        return this.ctx.shortTimeCacheService.getChatModel();
    }

    _getTooles(localCtx = {}) {
        const tools = [
            // 输出消息并且结束任务
            {
                name: 'replyAndEnd',
                descr: '输出消息并且结束任务',
                params: {
                    required: ['content'],
                    props: {
                        content: {
                            type: 'string',
                            descr: '输出内容',
                        },
                    }
                },
                call: async function (params) {
                    let result = { label: null, reply: null };

                    if (params.content.length > 20) {
                        if (localCtx.logLevel == null || localCtx.logLevel >= 3) log(`【调试】调用工具replyAndEnd: ${params.content}`);
                    }
                    else {
                        if (localCtx.logLevel == null || localCtx.logLevel >= 3) log(`【调试】调用工具replyAndEnd: ${(params.content ?? '').substring(0, 20)}...`);
                    }
                    result.reply = params.content;

                    return result;
                }
            },
            ...this.getShellTools(localCtx)
        ];

        return tools;
    }

    async _sendMessageBefore(message, opts = {}) {
        let model = opts.model;

        return {
            model,
        };
    }

    _convertAgentChatHistoryToAIChatHistory(agentChatHistory) {
        const list = [];
        if (agentChatHistory && agentChatHistory.length > 0) {
            for (const chatItem of agentChatHistory) {
                let role = chatItem.role;
                if (role === 'agent') {
                    role = 'assistant';
                }
                list.push({
                    role: role,
                    content: chatItem.content,
                });
            }
        }
        return list;
    }

    getShellSystemPrompt() {
        return `
### 任务说明
#### 角色要求
- 你现在是一个公司的老板，你可以指挥员工来帮助客户完成各种工作
- 每次只能指挥一个员工来干活

#### 员工信息
- 小王：负责你与客户之间的信息传达
`;
    }

    getShellTools(localCtx) {
        return [
            // 获取目录中子目录和文件
            {
                label: `发送消息给客户`,
                name: 'send_msg_to_client',
                descr: `让小王传达消息给客户`,
                params: {
                    required: ['msg'],
                    props: {
                        msg: {
                            type: 'string',
                            descr: '消息',
                        },
                    }
                },
                call: async function (params) {
                    let result = { label: null, reply: null };
                    const msg = params.msg;

                    result.reply = msg;

                    return result;
                }
            },
        ];
    }
}