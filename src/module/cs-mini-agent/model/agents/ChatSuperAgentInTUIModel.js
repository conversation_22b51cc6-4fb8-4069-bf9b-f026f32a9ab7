import {ChatSuperAgentModel} from "./ChatSuperAgentModel";
import os from "os";

/**
 * 寄宿在终端用户界面的超级智能体
 */
export class ChatSuperAgentInTUIModel extends ChatSuperAgentModel {
    constructor(ctx, opts) {
        super(ctx, opts);

        this.id = 'chat-super-in-tui';
        this.name = '在终端用户界面的超级智能体';
        this.summary = '比一般智能体有更高的智慧';
    }

    getShellSystemPrompt() {
        const { os } = this.ctx.imports;

        return `
### 任务说明
#### 角色描述
- 你现在是一个公司的老板，你可以指挥员工来帮助客户完成各种工作

#### 员工信息
- 小王：负责你与客户之间的信息传达

#### 客户的电脑信息
- 当前目录：${process.cwd()}（客户关注的目录）
- 主机名：${os.hostname()}
- 操作系统类型：${os.type()}
- 平台：${os.platform()}
- CPU 架构：${os.arch()}
`;
    }

    getShellTools(localCtx = {}) {
        const ctx = this.ctx;
        return [
            ...super.getShellTools(localCtx),
            // 获取目录中子目录和文件
            {
                label: `查询目录单层子项目`,
                name: 'getDirChildren',
                descr: `获取目录中的子目录和文件（只返回一层的），返回数组，字段：
- name 名称
- type 类型：目录/文件，dir表示类型为目录，file表示类型为文件
- size 文件大小`,
                params: {
                    required: ['path'],
                    props: {
                        path: {
                            type: 'string',
                            descr: '目录路径',
                        },
                    }
                },
                call: async function (params) {
                    let result = { label: null, reply: null };
                    const path = params.path;
                    const { fs } = ctx.imports;

                    localCtx.onProgressChange({
                        label: `查询目录子项：${path}`
                    });

                    if (localCtx.logLevel == null || localCtx.logLevel >= 3) log(`【调试】调用工具getDirChildren：${path}`)
                    const items = await fs.readdirSync(path, { withFileTypes: true });
                    // 映射目录项为所需格式的数组
                    const list = items.map(item => {
                        const itemPath = `${path}/${item.name}`;
                        let size = 0;
                        if (!item.isDirectory()) {
                            try {
                                const stats = fs.statSync(itemPath);
                                size = stats.size;
                            } catch (err) {
                                console.error(`获取文件大小失败: ${itemPath}`, err);
                            }
                        }
                        return {
                            name: item.name,
                            type: item.isDirectory() ? 'dir' : 'file',
                            size: size
                        };
                    });

                    result.reply = `（下面是系统消息，不会在对话中显示）
下面代码块中文本是目录“${path}”的子项
${'```json'}${JSON.stringify(list)}${'```'}
                    `

                    return result;
                }
            },
            // 读取文件字符串内容
            {
                label: `读取文件内容`,
                name: 'readFileStrCon',
                descr: '读取文件字符串内容',
                params: {
                    required: ['path'],
                    props: {
                        path: {
                            type: 'string',
                            descr: '文件路径',
                        },
                    }
                },
                call: async function (params) {
                    let result = { label: null, reply: null };
                    const path = params.path;
                    const { fs } = ctx.imports;

                    localCtx.onProgressChange({
                        label: `读取文件内容：${path}`
                    });

                    if (opts.logLevel == null || opts.logLevel >= 3) log(`【调试】调用工具readFileStrCon：${path}`)
                    const fileCon = fs.readFileSync(path, 'utf8');

                    result.label = `读取文件内容：${path}`;
                    result.reply = `（下面是系统消息，不会在对话中显示）
下面代码块中文本是文件“${path}”的内容：
${'```'}${fileCon}${'```'}
                    `;

                    return result;
                }
            },
        ]
    }
}