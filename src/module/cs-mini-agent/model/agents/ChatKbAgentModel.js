import ChatModelAgentModel from "./ChatModelAgentModel";
import {toDateStr, toDateTimeStr, toTimeStr} from "../../../../util/time-util";
/**
 * 知识库助手智能体（用于和知识库对话）
 */
export default class ChatKbAgentModel extends ChatModelAgentModel {
    constructor(ctx, opts) {
        super(ctx, opts);

        this.id = 'chat-kb';
        this.name = '与知识库对话';
        this.summary = '根据某个知识库回答问题';
    }

    async _sendMessageBefore(message, opts = {}) {
        const self = this;
        if (opts == null) opts = {};
        let model = opts.model;
        const ctx = this._getCtx();

        const messages = [];
        let findSystemPrompt = false;

        if (opts.chatHistory) {
            // 如果最后一条消息是用户发送的，则删除最后一条消息
            if (opts.chatHistory.length > 0) {
                if (opts.chatHistory[opts.chatHistory.length - 1].role === 'user') {
                    opts.chatHistory.pop();
                }
            }

            for (const i in opts.chatHistory) {
                const item = opts.chatHistory[i];
                if (item.role === 'system') {
                    findSystemPrompt = true;
                }
                messages.push({ role: item.role, content: item.content });
            }
            delete opts.chatHistory;
        }

        if (findSystemPrompt) {
            for (const messageItem of messages) {
                if (messageItem.role === 'system') {
                    messageItem.content = this._getSystemPrompt(ctx, { message });
                    break;
                }
            }
        }
        else {
            messages.splice(0, null, { role: 'system', content: this._getSystemPrompt(ctx) });
        }
        messages.push({ role: 'user', content: message });

        opts.tools = this._getTools(ctx)

        // log('Messages: ', JSON.stringify(messages));
        // log('ChatHistory: ', JSON.stringify(opts.chatHistory));

        return {
            model,
            messages,
        };
    }

    _getCtx() {
        const self = this;
        return {
            timeUtil: {
                toDateTimeStr: toDateTimeStr,
                toDateStr: toDateStr,
                toTimeStr: toTimeStr,
            },
            getConfig() {
                return self.getSettings().config;
            },
            getService(name) {
                return self.ctx.shortTimeCacheService[`get${name}`]();
            },
        };
    }

    _getSystemPrompt(ctx) {
        const kbId = ctx.getConfig().kbId;
        const csMiniKBService = ctx.getService('CSMiniKbService');
        const kbMapper = ctx.getService('KbMapper');
        const notFindReply = csMiniKBService.getNotFindReply(kbId);
        const kbForm = kbMapper.load(kbId);

        let str = ``;

        str += `### 上下文
#### 当前日期
${ctx.timeUtil.toDateStr(new Date())}
#### 当前时间
${ctx.timeUtil.toTimeStr(new Date())}
`;

        str += `### 任务说明
#### 角色要求
- 你是知识库助手，通过调用工具来查询知识后回答
- 尽量按原内容进行回答
- 内容中嵌入图片也要记得返回（图片是以地址形式放在内容中）
- 每次回答前都必须要先调用过searchKb工具才行，这样才能确保自己的回答是有知识根据的
`;
        if (notFindReply) {
            str += `- 如果没有在查询到的知识中找到合适的答案，就回复“${notFindReply}”（不要加工，就原样返回）
`;
        }

        if (kbForm && kbForm.first_sys_prompt && kbForm.first_sys_prompt.trim().length > 0) {
            str += `
### 任务补充说明
${kbForm.first_sys_prompt}
`
        }

        return str;
    }

    _getTools(ctx) {
        return [
            // 查询知识库
            {
                name: 'searchKb',
                descr: '查询知识库，返回可能相关或相近的知识',
                params: {
                    required: [],
                    props: {
                        query: {
                            type: 'string',
                            descr: '用户提问（原始内容）'
                            // descr: '查询向量的源文本，根据用户对话意图生成一个用于查询的源文本',
                        },
                        kwList: {
                            type: 'string',
                            descr: `根据对话内容和用户查询意图生成的关键词列表，例：{["关键词1", "关键词2"]}\n\n`
                                + `关键词要求：\n`
                                + `1. 使用json格式\n`
                                + `2. 根据输入内容生成关键词集合。\n`
                                + `3. 提炼关键词要注意多样化，例：大人票多少钱 -> 票，钱。\n`
                                + `4. 要有举一反三的意识，例如：大人票多少钱 -> 票，门票，票价，钱，价格，金子，价值。\n`
                                + `5. 尽量提炼出最短关键词，一个字也可以，最好不要超过两个字长度；6. 关键词尽可能提炼的多一些。\n`
                        }
                    }
                },
                call: async function (params, opts = {}) {
                    let query = params.query;
                    const kwListStr = params.kwList;
                    const csMiniKBService = ctx.getService('CSMiniKbService');
                    const kbMapper = ctx.getService('KbMapper');
                    const presetAnswerService = csMiniKBService.presetAnswerService;
                    let list = [];
                    const kbId = ctx.getConfig().kbId;

                    let useCodeWord = false; // 启用暗号
                    let allowSmallDB = true;
                    let allowBigDB = true;

                    const kbForm = kbMapper.load(kbId);
                    if (kbForm) {
                        useCodeWord = kbForm.use_codeword;
                    }

                    if (useCodeWord) {
                        log(`【调试】启用暗号！`)
                        if (opts.message) {
                            if (opts.message.indexOf('{no-s-db}') !== -1) {
                                allowSmallDB = false;
                                log(`【调试】关闭小库检索！`)
                            }
                            if (opts.message.indexOf('{no-b-db}') !== -1) {
                                allowBigDB = false;
                                log(`【调试】关闭大库检索！`)
                            }
                        }
                    }

                    if (allowSmallDB) {
                        log(`【查询知识库-小库】 ${ctx.getConfig().kbId} | 源文本：${query}`);
                        list = await presetAnswerService.search(ctx.getConfig().kbId, query, ctx.getConfig().kbListMaxSize, {
                            minScore: ctx.getConfig().yshdMinScore || 0.85,
                        });
                        if (list.length > 0) {
                            if (useCodeWord) {
                                return {
                                    reply: `${list[0].content}{src-s-db}`
                                };
                            }
                            else {
                                return {
                                    reply: `${list[0].content}`
                                };
                            }
                        }
                    }

                    if (allowBigDB) {
                        log(`【查询知识库-大库】 ${ctx.getConfig().kbId} | 源文本：${query}, 关键词：${kwListStr}`);
                        let parseKwListSuccess = false;
                        let kwList = [];
                        try {
                            kwList = JSON.parse(kwListStr);
                            parseKwListSuccess = true;
                        } catch (e) {
                            log('关键词解析失败', e.message);
                        }

                        list = await csMiniKBService
                            .searchSync1(ctx.getConfig().kbId, query, kwList, ctx.getConfig().kbListMaxSize);

                        if (list == null || list.length === 0) {
                            // 回复没有找到相关知识
                            return {
                                reply: csMiniKBService.getNotFindReply(ctx.getConfig().kbId)
                            };
                        }
                    }
                    else {
                        // 回复没有找到相关知识
                        return {
                            reply: csMiniKBService.getNotFindReply(ctx.getConfig().kbId)
                        };
                    }

                    // log('请求结果共', list.length, '条');

                    let str = '';
                    for (const item of list) {
                        str += `${item.title}\n`;
                        str += `${item.content}\n`
                        str += `\n`;
                    }
                    return str;
                }
            }
        ]
    }

}
