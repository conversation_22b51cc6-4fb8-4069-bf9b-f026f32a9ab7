/**
 * 智能体模型
 */
export default class TopAgentModel {
    constructor(ctx, opts) {
        this.ctx = ctx;

        if (opts) {
            for (const prop in opts) {
                this[prop] = opts[prop];
            }
        }
    }

    getParent() {
        return this.parent;
    }

    /**
     * 获取ID
     * @returns {String} 智能体ID
     */
    getId() {
        return this.id;
    }

    /**
     * 获取名称
     * @returns 
     */
    getName() {
        return this.name;
    }

    /**
     * 获取摘要
     * @returns 
     */
    getSummary() {
        return this.summary;
    }

    getSettings() {
        if (!this.settings) this.settings = {};
        return this.settings;
    }

    setSettings(settings) {
        if (!this.settings) this.settings = {};
        for (const prop in settings) {
            this.settings[prop] = settings[prop];
        }
    }

    getFrontSettings() {
        if (!this.frontSettings) this.frontSettings = {};
        return this.frontSettings;
    }

    setFrontSettings(frontSettings) {
        if (!this.frontSettings) this.frontSettings = {};
        for (const prop in frontSettings) {
            this.frontSettings[prop] = frontSettings[prop];
        }
    }

    /**
     * 向智能体发送消息

     * @param {String} message 消息
     * @param {*} opts 
     * @returns {Promise<Object>} 思考结果
     */
    async sendMessage(message, opts) {
        
    }
}