/**
 * 聊天成员
 * @typedef {Object} ChatMember
 * @property {String} id - 成员id
 * @property {String} name - 成员名称
 * @property {String} avatar - 成员头像
 * @property {String} role - 成员角色，取值：user、agent
 */

/**
 * 聊天消息
 * @typedef {Object} ChatHistoryItem
 * @property {String} id - 消息ID
 * @property {String} content - 消息内容
 * @property {Boolean} isMe - 是否是当前用户发送的消息
 * @property {String} role - 消息角色，取值：user、assistant、system
 * @property {Number} time - 消息发送时间（时间戳）
 * @property {String} senderId - 发送人id
 */

/**
 * 聊天历史
 * @typedef {Array<ChatHistoryItem>} ChatHistory
 */

/**
 * 我的聊天会话
 * @typedef {Object} MyChatSession
 * @property {String} id - 会话id
 * @property {String} title - 会话标题
 * @property {String} summary - 会话摘要
 * @property {Array<ChatMember>} otherMembers - 其他成员
 * @property {ChatHistory} chatHistory - 聊天历史
 */

/**
 * 发送消息
 * @typedef {Object} SendMessage
 * @property {String} id - 消息ID
 * @property {String} content - 消息内容
 * @property {String} sessionId - 会话ID
 * @property {String} chatHistorySessionId - 用于存储对话历史的会话ID
 * @property {String} senderId - 发送人ID（用户名）
 * @property {String} receiverId - 接收人ID（一对一会话），例：xxx.cn_chat-game-a
 * @property {Number} time - 消息发送时间（时间戳）
 * @property {Object} opts - 附加选项
 */

/**
 * 回复消息
 * @typedef {Object} ReplyMessage
 * @property {Boolean} success - 是否成功
 * @property {String} reply - 回复内容
 * @property {String} id - 消息ID
 * @property {Object} usage - 使用量
 * @property {Number} time - 时间（时间戳）
 */