import TopController from "../../../controller/TopController";
import {UserCreateAgentMapper} from "../mapper/UserCreateAgentMapper";

export default class UserCreateAgentController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;

        /**
         *
         * @param {String} req.body.userId
         */
        app.post('/api/cma/uca/getMyAgentList', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/uca/getMyAgentList', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                const userCreateAgentMapper = new UserCreateAgentMapper(ctx);
                const r = await userCreateAgentMapper.getMyAgentList(loginUser.sac, loginUser.username);
                res.send({ success: true, data: r });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         *
         * @param {String} req.body.userId
         * @param {String} req.body.agentId
         */
        app.post('/api/cma/uca/getMyAgentForm', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/uca/getMyAgentForm', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                const userCreateAgentMapper = new UserCreateAgentMapper(ctx);
                const r = await userCreateAgentMapper.getMyAgentForm(loginUser.sac, loginUser.username, req.body.agentId);
                res.send({ success: true, data: r });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         *
         * @param {String} req.body.userId
         * @param {String} req.body.agentId
         * @param {String} req.body.form
         */
        app.post('/api/cma/uca/saveMyAgentForm', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/uca/saveMyAgentForm', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                const userCreateAgentMapper = new UserCreateAgentMapper(ctx);
                await userCreateAgentMapper.saveMyAgentForm(loginUser.sac, loginUser.username, req.body.agentId, req.body.form);
                res.send({ success: true });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         *
         * @param {String} req.body.userId
         * @param {String} req.body.agentId
         */
        app.post('/api/cma/uca/deleteMyAgent', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/uca/deleteMyAgent', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                const userCreateAgentMapper = new UserCreateAgentMapper(ctx);
                await userCreateAgentMapper.deleteMyAgent(loginUser.sac, loginUser.username, req.body.agentId);
                res.send({ success: true });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });
    }
}