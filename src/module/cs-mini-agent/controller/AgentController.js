import TopController from "../../../controller/TopController";

export default class AgentController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;

        /**
         * 获取智能体前端设置
         * @param {String} req.body.token 令牌
         * @param {String} req.body.id 智能体id
         */
        app.post('/api/cma/agent/get-agent-front-settings', async function(req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/agent/get-agent-front-settings', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                const r = await self.ctx.csMiniAgentService.agentService.getAgentFrontSettingsStr(req.body.id);
                res.send({ success: true, data: r });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });
        
        /**
         * 执行智能体插件
         * @param {String} req.body.token 令牌
         * @param {String} req.body.agentId 智能体id
         * @param {String} req.body.name 插件名称
         * @param {Object} req.body.params 插件参数
         */
        app.post('/api/cma/agent/exec-agent-plugin', async function(req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/agent/exec-agent-plugin', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                const agentService = self.ctx.csMiniAgentService.agentService;
                const agent = agentService.getAgent(req.body.agentId);
                const r = await agentService.execScript_execPlugin({
                    agent, 
                    name: req.body.name, 
                    params: req.body.params
                }, {
                    loginUser: loginUser,
                });
                res.send({ success: true, data: r });
            } catch (exc) {
                self.replyException(res, exc);
            }
        });
    }
}