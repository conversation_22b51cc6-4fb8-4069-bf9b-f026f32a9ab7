import { splitUpStr } from "../../../util/str-util";
import QywxCallbackController from "../../weixin/controller/QywxCallbackController";
import QywxMapper from "../../weixin/mapper/QywxMapper";
// import {marked} from "marked";

export default class QywxCbChatController extends QywxCallbackController {
    constructor(ctx, app, cfg, parent) {
        super(ctx, app, {
            ...cfg
        });

        const self = this;
        this.parent = parent;
        // corpId, sac, loginSKey
        this.qywxCorp = cfg.qywxCorp;

        this.qywxMapper = new QywxMapper(ctx);
        // 用于判断消息是否重复
        this._qywxMsgCache = {};
    }

    // 是否为重复消息
    isDuplicateMsg(msg) {
        const msgUniqueKey = `${msg.ToUserName}_${msg.FromUserName}_${msg.MsgId}_${msg.AgentID}`;
        if (this._qywxMsgCache[msgUniqueKey] && Date.now() - this._qywxMsgCache[msgUniqueKey] < 8000) {
            return true;
        }
        this._qywxMsgCache[msgUniqueKey] = Date.now();
        // 清理
        for (const key in this._qywxMsgCache) {
            if (Date.now() - this._qywxMsgCache[key] > 8000) {
                delete this._qywxMsgCache[key];
            }
        }
        return false;
    }

    // @param {Object} msg 消息 { ToUserName, FromUserName, CreateTime, MsgType, Content, MsgId, AgentID }
    // 语音消息 { ToUserName, FromUserName, CreateTime, MsgType, MediaId, Format }
    async onReceiveMessage(msg, req, res) {
        const self = this;
        try {
            log(`收到【企业微信】消息“${msg.ToUserName}”，来之：${msg.FromUserName}，类型：${msg.MsgType}，内容：${msg.Content}`);
            // 判定是否重复
            if (self.isDuplicateMsg(msg)) {
                log(`【重复消息】取消执行！`);
                return;
            }
            
            const loginResult = await self.ctx.shortTimeCacheService.getLoginService()
            .loginByWx(self.qywxCorp.sac, msg.FromUserName, self.qywxCorp.loginSKey, req, res);
            // log(`登录结果：${JSON.stringify(loginResult)}`);

            if (loginResult.success) {
                const session = await self.parent.myChatSessionService.getSession(self.qywxCorp.sac, loginResult.user.username, self.cfg.reqId);
                if (session == null) {
                    log(`未能找到聊天会话`);
                    // 回复消息给用户
                    const result = await self.sendAppMessageText(self.qywxCorp.corpId, self.cfg.secret, self.cfg.agentId, msg.FromUserName, `未能找到聊天会话`);
                }
                else {
                    // 文本消息
                    if (msg.MsgType === 'text') {
                        const receiveUserSendTime = Date.now();
                        // 发送消息给智能体
                        const r = await self.ctx.csMiniAgentService.chatService.sendMessage(self.qywxCorp.sac, {
                            id: msg.MsgId,
                            content: msg.Content,
                            sessionId: self.cfg.reqId,
                            senderId: loginResult.user.username,
                            receiverId: session.otherMembers[0].id,
                            time: receiveUserSendTime,
                        }, {
                            loginUser: loginResult.user,
                            chatHistory: null,
                        });
                        // 回复消息给用户
                        const replyMaxLength = 650; // 682
                        // 拆分回复内容
                        if (r.reply.length > replyMaxLength) {
                            const replyList = splitUpStr(r.reply, replyMaxLength);
                            // 分批发送
                            let sendReplyIndex = 0;
                            for (const reply of replyList) {
                                const result = await self.sendAppMessageText(self.qywxCorp.corpId, self.cfg.secret, 
                                    self.cfg.agentId, msg.FromUserName, `${reply}\n（第${sendReplyIndex + 1}/${replyList.length}段）`);
                                    sendReplyIndex++;
                            }
                        }
                        else {
                            const result = await self.sendAppMessageText(self.qywxCorp.corpId, self.cfg.secret, 
                                self.cfg.agentId, msg.FromUserName, `${r.reply}`);
                        }
                        // 推送聊天历史记录（用户发送的消息）
                        self.parent.chatSessionHistoryService.pushChatHistoryItem(self.qywxCorp.sac, loginResult.user.username, self.cfg.reqId, {
                            id: msg.MsgId,
                            content: msg.Content,
                            isMe: true,
                            role: 'user',
                            time: receiveUserSendTime,
                        });
                        // 推送聊天历史记录（智能体发送的消息）
                        self.parent.chatSessionHistoryService.pushChatHistoryItem(self.qywxCorp.sac, loginResult.user.username, self.cfg.reqId, {
                            id: Date.now().toString(),
                            content: r.reply,
                            isMe: false,
                            role: 'agent',
                            time: Date.now(),
                        });
                    }
                    // // 语音消息
                    // else if (msg.MsgType === 'voice') {
                    //     log(JSON.stringify(msg));
                    // }
                    else if (msg.MsgType === 'event') {
                    }
                    // 未知消息类型
                    else {
                        log(`不支持的消息类型：${msg.MsgType}`);
                        log(JSON.stringify(msg));
                        const result = await self.sendAppMessageText(self.qywxCorp.corpId, self.cfg.secret, self.cfg.agentId, msg.FromUserName, `不支持的消息类型：${msg.MsgType}`);
                    }
                }
            }
            else {
                log(`登录失败，请先挂钩微信号 -> ${msg.FromUserName}`);
                const result = await self.sendAppMessageText(self.qywxCorp.corpId, self.cfg.secret, self.cfg.agentId, msg.FromUserName, `登录失败，请先挂钩微信号 -> ${msg.FromUserName}`);
            }
        }
        catch(exc) {
            log(`QywxCbChatController.onReceiveMessage异常 -> ${exc.message} -> ${exc.stack}`);
            try {
                const result = await self.sendAppMessageText(self.qywxCorp.corpId, self.cfg.secret, self.cfg.agentId, msg.FromUserName, `发生异常 -> ${exc.message}`);
            }
            catch(exc) {
                log(`异常 -> ${exc.message} -> ${exc.stack}`);
            }
        }
    }
}