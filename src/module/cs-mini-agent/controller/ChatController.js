import TopController from "../../../controller/TopController";

export default class ChatController extends TopController {
    constructor(ctx, app) {
        super(ctx);
        const self = this;

        // 对话输出流状态库
        // "xxx": { time: 0, reqAbort: false }
        self._chat_stream_states = {};

        /**
         * 发送消息
         * @param {String} req.body.token 令牌
         * @param {SendMessage} req.body.message 消息
         * @param {*} req.body.chatHistory 聊天历史
         * @param {*} req.body.setConfigFields 配置属性
         */
        app.post('/api/cma/chat/send-message', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/chat/send-message', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                const r = await self.ctx.csMiniAgentService.chatService.sendMessage(loginUser.sac, req.body.message, {
                    loginUser,
                    chatHistory: req.body.chatHistory,
                    setConfigFields: req.body.setConfigFields,
                    logLevel: 1,
                });
                res.send({ success: true, data: r });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         * 发送消息（流式输出）
         * @param {String} req.body.token 令牌
         * @param {*} req.body.chatStreamStateId 对话输出流状态ID
         * @param {SendMessage} req.body.message 消息
         * @param {*} req.body.chatHistory 聊天历史
         * @param {*} req.body.chatModel 聊天模型
         * @param {*} req.body.setConfigFields 配置属性
         */
        app.post('/api/cma/chat/send-message-stream', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/chat/send-message-stream', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;

                res.set({
                    'Content-Type': 'text/event-stream; charset=utf-8',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                });
                res.status(200);

                // 创建对话输出流状态
                if (req.body.chatStreamStateId && req.body.chatStreamStateId.length > 0) {
                    self._chat_stream_states[req.body.chatStreamStateId] = {
                        time: Date.now()
                    };
                }

                // ...
                const result = await self.ctx.csMiniAgentService.chatService.sendMessageStream(loginUser.sac, req.body.message,
                    (chunk) => {
                        res.write(chunk);
                        // log(`【调试】发送消息（流式输出）：${chunk}`)
                        // 判定是否中断输出
                        if (req.body.chatStreamStateId && req.body.chatStreamStateId.length > 0) {
                            const state = self._chat_stream_states[req.body.chatStreamStateId];
                            if (state) {
                                if (state.reqAbort === true) {
                                    if (result && result.abortController) {
                                        result.abortController.abort();
                                    }
                                }
                                else {
                                    state.time = Date.now();
                                }
                            }
                        }
                    }, () => {
                        res.end();
                        if (req.body.chatStreamStateId && req.body.chatStreamStateId.length > 0) {
                            delete self._chat_stream_states[req.body.chatStreamStateId];
                        }
                        // log('【调试】发送消息（流式输出）完毕！')
                    }, (err) => {
                        self.replyException(res, err);
                    }, {
                        loginUser,
                        chatHistory: req.body.chatHistory,
                        chatModel: req.body.chatModel,
                        setConfigFields: req.body.setConfigFields,
                        logLevel: 1,
                    });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         * 中断流式输出
         * @param {String} req.body.token 令牌
         * @param {String} req.body.chatStreamStateId 对话输出流状态ID
         */
        app.post('/api/cma/chat/abort-send-message-stream', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/chat/abort-send-message-stream', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                if (req.body.chatStreamStateId && req.body.chatStreamStateId.length > 0) {
                    const state = self._chat_stream_states[req.body.chatStreamStateId]
                    if (state) {
                        state.reqAbort = true;
                    }
                }
                res.send({ success: true });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         * 获取我的聊天会话列表
         * @param {String} req.body.token 令牌
         * @param {String} req.body.kw 搜索关键字
         */
        app.post('/api/cma/chat/get-my-chat-sessions', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/chat/get-my-chat-sessions', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                const r = await self.ctx.csMiniAgentService.myChatSessionService.getSessions(loginUser.sac, loginUser.username, req.body.kw);
                res.send({ success: true, data: r });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         * 获取聊天会话历史记录
         * @param {String} req.body.sessionId 会话ID
         */
        app.post('/api/cma/chat/get-chat-history', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/chat/get-chat-history', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                const data = await self.ctx.csMiniAgentService.chatSessionHistoryService.getChatHistory(loginUser.sac, loginUser.username, req.body.sessionId, 10);
                res.send({ success: true, data: data });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         * 推送聊天会话历史记录
         * @param {String} req.body.sessionId 会话ID
         * @param {Object} req.body.item 历史记录
         */
        app.post('/api/cma/chat/push-chat-history-item', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/chat/push-chat-history-item', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                await self.ctx.csMiniAgentService.chatSessionHistoryService
                .pushChatHistoryItem(loginUser.sac, loginUser.username, req.body.sessionId, req.body.item);
                res.send({ success: true });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         * 删除聊天会话历史记录
         * @param {String} req.body.sessionId 会话ID
         * @param {String} req.body.id 历史记录ID
         */
        app.post('/api/cma/chat/delete-chat-history-item', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/chat/delete-chat-history-item', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                await self.ctx.csMiniAgentService.chatSessionHistoryService.deleteChatHistoryItem(loginUser.sac, loginUser.username, req.body.sessionId, req.body.id);
                res.send({ success: true });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });

        /**
         * 清空聊天会话历史记录
         * @param {String} req.body.sessionId 会话ID
         */
        app.post('/api/cma/chat/clear-chat-history', async function (req, res) {
            try {
                // 校验登陆状态
                const validateLoginResult = await self.validateLogin(req, res);
                self.logReq(req, '/api/cma/chat/clear-chat-history', null, validateLoginResult);
                if (!validateLoginResult) {
                    return;
                }
                const loginUser = validateLoginResult.user;
                // ...
                await self.ctx.csMiniAgentService.chatSessionHistoryService.clearChatHistory(loginUser.sac, loginUser.username, req.body.sessionId);
                res.send({ success: true });
            } catch (exc) {
                self.replyException(res, exc, null, req);
            }
        });
    }
}