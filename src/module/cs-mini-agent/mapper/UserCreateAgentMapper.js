import {newCUIdA} from "../../../util/id-util";

/**
 * 用户自建智能体
 */
export class UserCreateAgentMapper {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async getMyAgentList(sac, userId) {
        const { fs } = this.ctx.imports;
        let dirPath = `${this.ctx.rootPath}/data/cs-mini-agent/my-chat-session-list/${sac}`;
        let filePath = `${dirPath}/${userId}.uca.json`;
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }

        if (fs.existsSync(filePath)) {
            const data = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(data);
        }

        return [];
    }

    async saveMyAgentList(sac, userId, list) {
        const { fs } = this.ctx.imports;
        let dirPath = `${this.ctx.rootPath}/data/cs-mini-agent/my-chat-session-list/${sac}`;
        let filePath = `${dirPath}/${userId}.uca.json`;
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }

        fs.writeFileSync(filePath, JSON.stringify(list));
    }

    async getMyAgentForm(sac, userId, agentId) {
        const { fs } = this.ctx.imports;
        let dirPath = `${this.ctx.rootPath}/data/cs-mini-agent/user-create-agent/${sac}/${userId}`;
        let filePath = `${dirPath}/${agentId}.json`;
        if (fs.existsSync(filePath)) {
            const data = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(data);
        }
        return {};
    }

    async saveMyAgentForm(sac, userId, agentId, form) {
        if (agentId == null || agentId.length === 0) {
            agentId = newCUIdA()
        }
        form.id = agentId

        const { fs } = this.ctx.imports;
        let dirPath = `${this.ctx.rootPath}/data/cs-mini-agent/user-create-agent/${sac}/${userId}`;
        let filePath = `${dirPath}/${agentId}.json`;
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }

        fs.writeFileSync(filePath, JSON.stringify(form));

        const list = await this.getMyAgentList(sac, userId);
        const find = list.find((n) => {
            return n.id === form.id;
        });
        if (!find) {
            list.push({
                id: form.id,
                title: form.name,
                summary: form.summary,
                otherMembers: [
                    {
                        id: "a.cn_chat-uca-model",
                        name: null,
                        avatar: null,
                        summary: null,
                        role: "agent"
                    }
                ]
            })
        }
        else {
            find.title = form.name;
            find.summary = form.summary;
            find.otherMembers = [
                {
                    id: "a.cn_chat-uca-model",
                    name: null,
                    avatar: null,
                    summary: null,
                    role: "agent"
                }
            ]
        }

        await this.saveMyAgentList(sac, userId,list);
    }

    async deleteMyAgent(sac, userId, agentId) {
        const { fs } = this.ctx.imports;
        let dirPath = `${this.ctx.rootPath}/data/cs-mini-agent/user-create-agent/${sac}/${userId}`;
        let filePath = `${dirPath}/${agentId}.json`;
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
        }

        const list = await this.getMyAgentList(sac, userId);
        const index = list.findIndex((n) => {
            return n.id === agentId;
        });
        if (index !== -1) {
            list.splice(index, 1);
            await this.saveMyAgentList(sac, userId, list);
        }
    }
}