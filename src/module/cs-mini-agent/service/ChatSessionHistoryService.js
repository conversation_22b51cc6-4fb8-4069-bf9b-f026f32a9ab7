/**
 * 聊天会话历史记录服务
 */
export default class ChatSessionHistoryService {
    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     * 推送聊天会话历史记录
     * @param sac
     * @param {String} userId 用户ID（用户名）
     * @param {String} sessionId 会话ID
     * @param {Object} chatHistoryItem 聊天会话历史记录
     */
    async pushChatHistoryItem(sac, userId, sessionId, chatHistoryItem) {
        const {fs} = this.ctx.imports;
        const dirPath = `${this.ctx.rootPath}/data/cs-mini-agent/chat-session-history/${sac}/${userId}`;
        const filePath = `${dirPath}/${sessionId}.json`;

        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, {recursive: true});
        }

        let chatSessionHistory;
        // 判断文件是否存在，不存在则自动创建路径
        if (!fs.existsSync(filePath)) {
            chatSessionHistory = [];
        } else {
            // 加载文件
            const data = fs.readFileSync(filePath, 'utf8');
            if (data && data.length > 0) {
                chatSessionHistory = JSON.parse(data);
            } else {
                chatSessionHistory = [];
            }
        }
        chatSessionHistory.push(chatHistoryItem);
        // 保存到文件
        fs.writeFileSync(filePath, JSON.stringify(chatSessionHistory));
    }

    /**
     * 获取聊天会话历史记录
     * @param sac
     * @param {String} userId 用户ID（用户名）
     * @param {String} sessionId 会话ID
     * @param {Number} limit 限制条数
     * @returns {Array} 聊天会话历史记录
     */
    async getChatHistory(sac, userId, sessionId, limit) {
        const {fs} = this.ctx.imports;
        const filePath = `${this.ctx.rootPath}/data/cs-mini-agent/chat-session-history/${sac}/${userId}/${sessionId}.json`;
        if (fs.existsSync(filePath)) {
            const data = fs.readFileSync(filePath, 'utf8');
            if (data && data.length > 0) {
                const chatSessionHistory = JSON.parse(data);
                // 获取最新的limit条
                return chatSessionHistory.slice(-limit);
            }
        }
        return [];
    }

    /**
     * 删除聊天会话历史记录
     * @param sac
     * @param {String} userId 用户ID（用户名）
     * @param {String} sessionId 会话ID
     * @param {String} id 历史记录ID
     */
    async deleteChatHistoryItem(sac, userId, sessionId, id) {
        const {fs} = this.ctx.imports;
        const filePath = `${this.ctx.rootPath}/data/cs-mini-agent/chat-session-history/${sac}/${userId}/${sessionId}.json`;
        if (fs.existsSync(filePath)) {
            const data = fs.readFileSync(filePath, 'utf8');
            const chatSessionHistory = JSON.parse(data);
            const index = chatSessionHistory.findIndex(msg => msg.id === id);
            if (index > -1) {
                chatSessionHistory.splice(index);
            }
            fs.writeFileSync(filePath, JSON.stringify(chatSessionHistory));
        }
    }

    /**
     * 清空聊天会话历史记录
     * @param sac
     * @param {String} userId 用户ID（用户名）
     * @param {String} sessionId 会话ID
     */
    async clearChatHistory(sac, userId, sessionId) {
        const {fs} = this.ctx.imports;
        const filePath = `${this.ctx.rootPath}/data/cs-mini-agent/chat-session-history/${sac}/${userId}/${sessionId}.json`;
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
        }
    }
}