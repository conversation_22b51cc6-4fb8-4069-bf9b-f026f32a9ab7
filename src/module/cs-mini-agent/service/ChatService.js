import {toDateTimeStr} from "../../../util/time-util";

/**
 * 聊天服务
 */
export default class ChatService {

    constructor(ctx, parent) {
        this.ctx = ctx;
        this.parent = parent;
    }

    /**
     * 发送消息
     * @param {*} sac
     * @param {SendMessage} message 消息
     * @param {*} opts loginUser, chatHistory
     * @returns {Promise<ReplyMessage>}
     */
    async sendMessage(sac, message, opts = {}) {
        const { sendContent, sendChatHistory, sendOpts, agent, agentSettings } = await this._onSendMessageBefore(sac, message, opts);

        if (sendContent == null) {
            throw new Error('发送内容不能为空');
        }

        if (sendChatHistory == null) {
            throw new Error('发送历史不能为空');
        }

        if (agent) {
            // 打印日志
            if (sendContent.length > 100) {
                if (opts.logLevel == null || opts.logLevel >= 3) log(`【ChatService.sendMessage】${agent.getId()} | “${sendContent.substring(0, 100)}...” | 共${sendChatHistory.length}条历史消息（含系统消息）`);
            }
            else {
                if (opts.logLevel == null || opts.logLevel >= 3) log(`【ChatService.sendMessage】${agent.getId()} | “${sendContent}” | 共${sendChatHistory.length}条历史消息（含系统消息）`);
            }

            // 发送消息
            let receiveMessage;
            if (agentSettings.mockReceiveMessage) {
                receiveMessage = {
                    reply: 'mockReceiveMessage',
                    usage: {
                        total: 0,
                        input: 0,
                        output: 0
                    }
                };
            }
            else {
                if (opts.logLevel == null || opts.logLevel >= 3) log(`【调试】发送内容：${sendContent}`);
                // if (opts.logLevel == null || opts.logLevel >= 3) log(`【调试】对话历史：${JSON.stringify(sendChatHistory)}`);
                receiveMessage = await agent.sendMessage(sendContent, {
                    ...opts,
                    ...sendOpts,
                    chatHistory: sendChatHistory,
                    sessionId: message.sessionId,
                    // buildSystemMessageBeforeChat: this.parent.agentService.execScript_buildSystemMessageBeforeChat,
                });
            }

            if (agentSettings.onGetReceiveMessage) {
                receiveMessage = await agentSettings.onGetReceiveMessage(receiveMessage);
            }

            if (receiveMessage.usage) {
                if (opts.logLevel == null || opts.logLevel >= 2) log(`【ChatService.sendMessage】共消耗token：${receiveMessage.usage.total}（input: ${receiveMessage.usage.input}，output: ${receiveMessage.usage.output}）`);
            }

            return receiveMessage;
        }
        else {
            throw new Error('未能识别的发送对象');
        }
    }

    /**
     * 发送消息（流式）
     * @param {*} sac 
     * @param {*} message opts: { model }
     * @param {*} onData 
     * @param {*} onEnd 
     * @param {*} onError 
     * @param {*} opts loginUser, chatHistory
     */
    async sendMessageStream(sac, message, onData, onEnd, onError, opts = {}) {
        const { sendContent, sendChatHistory, sendOpts, agent, agentSettings } = await this._onSendMessageBefore(sac, message, opts);

        if (sendContent == null) {
            throw new Error('发送内容不能为空');
        }

        if (sendChatHistory == null) {
            throw new Error('发送历史不能为空');
        }

        if (agent) {
            // log(`chatModel: ${opts.chatModel}`)
            // log(`${JSON.stringify(sendOpts)}`)
            // 打印日志
            if (sendContent.length > 100) {
                if (opts.logLevel == null || opts.logLevel >= 3) log(`【ChatService.sendMessageStream】${agent.getId()} | “${sendContent.substring(0, 100)}...” | 共${sendChatHistory.length}条历史消息（含系统消息）`);
            }
            else {
                if (opts.logLevel == null || opts.logLevel >= 3) log(`【ChatService.sendMessageStream】${agent.getId()} | “${sendContent}” | 共${sendChatHistory.length}条历史消息（含系统消息）`);
            }

            if (opts.logLevel == null || opts.logLevel >= 3) log(`【调试】发送内容：${sendContent}`);
            // if (opts.logLevel == null || opts.logLevel >= 3) log(`【调试】对话历史：${JSON.stringify(sendChatHistory).replaceAll("\t", "")}`);
            // 发送消息
            return await agent.sendMessageStream(sendContent, onData, onEnd, onError, {
                ...opts,
                ...sendOpts,
                chatHistory: sendChatHistory,
                sessionId: message.sessionId,
                // buildSystemMessageBeforeChat: this.parent.agentService.execScript_buildSystemMessageBeforeChat,
            });
        }
        else {
            throw new Error('未能识别的发送对象');
        }
    }

    /**
     * 发送消息前
     * @param {*} sac 
     * @param {*} message opts: { model }
     * @param {*} opts loginUser, chatHistory
     * @returns 
     */
    async _onSendMessageBefore(sac, message, opts) {
        const sessionId = message.sessionId;
        const chatHistorySessionId = message.chatHistorySessionId;

        let sendContent;
        let sendChatHistory;
        let sendOpts;
        let agent;
        let agentSettings;

        // 是否设置系统消息到发送消息开头
        let setSystemMessageToChatStart = false;
        // 每次加载历史对话最多条数
        let maxHistoryCount = 10;

        if (message.opts == null) message.opts = {};

        // 一对一会话
        if (message.receiverId) {
            agent = this.parent.agentService.getAgent(message.receiverId);
            // 与智能体对话
            if (agent) {
                agentSettings = agent.getSettings();
                if (agentSettings == null) agentSettings = {};
                setSystemMessageToChatStart = agentSettings.setSystemMessageToChatStart || false;

                if (opts.setConfigFields) {
                    if (agentSettings.config) {
                        for (const prop in opts.setConfigFields) {
                            agentSettings.config[prop] = opts.setConfigFields[prop];
                        }
                    }
                    delete opts.setConfigFields;
                }

                // 加载工具集
                const tools = await this.parent.agentService.getAgentTools({ agent }, {
                    sessionId: sessionId,
                    ...opts,
                });
                if (message.opts) {
                    message.opts.tools = tools;
                }

                // 加载对话
                let chatHistory = [];

                // 使用外部传入的历史对话
                if (opts.chatHistory) {
                    if (opts.logLevel == null || opts.logLevel >= 3) log(`使用传入历史对话：${JSON.stringify(opts.chatHistory)}`)
                    chatHistory = opts.chatHistory;
                    log(`【*** 测试1 ***】maxChatHistoryLength: ${agentSettings.maxChatHistoryLength}`)
                    if (agentSettings.maxChatHistoryLength === 0) {
                        chatHistory = [];
                    }
                    else {
                        chatHistory = chatHistory.slice(-agentSettings.maxChatHistoryLength);
                    }
                }
                // 加载会话对应的历史对话
                else {
                    // 加载历史对话
                    let loadedChatHistory = await this.parent.chatSessionHistoryService
                        .getChatHistory(sac, message.senderId, chatHistorySessionId || sessionId, maxHistoryCount);

                    // log(`【*** 测试2 ***】maxChatHistoryLength: ${agentSettings.maxChatHistoryLength}`)
                    // 处理历史对话长度
                    if (agentSettings.maxChatHistoryLength === 0) {
                        loadedChatHistory = [];
                    }
                    else {
                        if (loadedChatHistory.length > 0) {
                            if (agentSettings.maxChatHistoryLength != null && agentSettings.maxChatHistoryLength >= 0) {
                                loadedChatHistory = loadedChatHistory.slice(-agentSettings.maxChatHistoryLength);
                            }
                            for (const item of loadedChatHistory) {
                                if (item.role === 'agent') {
                                    chatHistory.push({ role: 'assistant', content: item.content + `（生成时间：${toDateTimeStr(new Date(item.time))}）` });
                                }
                                else {
                                    chatHistory.push({ role: item.role, content: item.content + `（生成时间：${toDateTimeStr(new Date(item.time))}）` });
                                }
                            }
                        }
                    }
                    if (opts.logLevel == null || opts.logLevel >= 2)
                        log(`【加载历史对话】初始条数（${message.senderId}）：${loadedChatHistory.length}，转换后条数：${chatHistory.length}，配置限定：${agentSettings.maxChatHistoryLength}`);
                }

                // 获取自定义系统消息
                const systemMessage = await this.parent.agentService.execScript_getSystemMessage({ agent, chatHistory, message }, opts);

                if (setSystemMessageToChatStart) {
                    // ...
                    if (systemMessage && systemMessage.trim().length > 0) {
                        sendContent = `${systemMessage}\n\n` + message.content;
                    }
                }
                else {
                    // 加入系统消息到历史对话
                    if (systemMessage && systemMessage.trim().length > 0) {
                        chatHistory.splice(0, null, { role: 'system', content: systemMessage });
                    }
                    // ...
                    sendContent = message.content;
                }

                sendChatHistory = await this.parent.agentService.execScript_getChatHistory({ agent, chatHistory, message });
                sendOpts = message.opts;

                // 加上临时系统记忆
                {
                    let t_debug = false;

                    if (t_debug) log(`【调试】参数：${JSON.stringify(opts.loginUser)} | ${sessionId}`)
                    const memory = this.parent.chatTempMemoryService.getAllSys(opts.loginUser, sessionId);
                    if (t_debug) log(`【调试】是否有临时系统记忆：${JSON.stringify(memory)}`)
                    let findField = false;
                    for (const prop in memory) {
                        if (memory[prop] != null) {
                            findField = true;
                            break;
                        }
                    }
                    if (t_debug) log(`【调试】是否有临时系统记忆：${findField}`)

                    if (findField) {
                        let toAddStr = ``;
                        for (const prop in memory) {
                            if (memory[prop] != null) {
                                toAddStr += `\n\n### 历史对话记忆（${prop}）\n${'```'}\n${memory[prop]}\n${'```'}\n\n\n`;
                            }
                        }
                        if (t_debug) log(`【调试】临时系统记忆内容：${toAddStr}`)

                        let findSystem = false;
                        for (const i in sendChatHistory) {
                            const chatItem = sendChatHistory[i];
                            if (chatItem.role === 'system') {
                                findSystem = true;
                                chatItem.content += toAddStr;
                                if (t_debug) log(`【调试】修改现有系统题词`)
                                break;
                            }
                        }

                        if (!findSystem) {
                            sendChatHistory.splice(0, null, { role: 'system', content: toAddStr });
                            if (t_debug) log(`【调试】新增系统题词`)
                        }
                    }
                }

                if (agentSettings) {
                    // 限制AI回复内容大小
                    if (agentSettings.chatHistoryBotReplyMaxSize > 0) {
                        for (const i in sendChatHistory) {
                            const chatItem = sendChatHistory[i];
                            if (chatItem.role === 'assistant') {
                                if (chatItem.content.length > agentSettings.chatHistoryBotReplyMaxSize) {
                                    if (opts.logLevel == null || opts.logLevel >= 2) log(`限制回复内容大小...`)
                                    chatItem.content = `${chatItem.content.substring(0, agentSettings.chatHistoryBotReplyMaxSize)}...（由于限制后续内容被省略，之后的回答无需参考此做法）`;
                                    if (opts.logLevel == null || opts.logLevel >= 3) log(`限制后内容：${chatItem.content}`)
                                }
                            }
                        }
                    }
                    // 加载配置中的opts参数
                    if (agentSettings.chatOpts) {
                        for (const i in agentSettings.chatOpts) {
                            if (opts[i] == null) {
                                sendOpts[i] = agentSettings.chatOpts[i];
                            }
                        }
                    }
                }
            }
        }

        return {
            sendContent,
            sendChatHistory,
            sendOpts,
            agent,
            agentSettings,
        };
    }
}