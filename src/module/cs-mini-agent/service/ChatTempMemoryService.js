/**
 * 聊天临时记忆
 * 正常对话历史可能会干扰模型回复准确率，所以采用临时记忆形式来弥补
 */
export class ChatTempMemoryService {
    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     * 获取所有临时系统记忆（系统记忆会每次对话时加入到system题词中）
     * @param user
     * @param sessionId
     */
    getAllSys(user, sessionId) {
        const sac = user.sac;
        const userId = user.username;

        const { fs } = this.ctx.imports;
        const dirPath = `${this.ctx.rootPath}/data/cs-mini-agent/chat-temp-memory/${sac}/${userId}`;
        const filePath = `${dirPath}/${sessionId}.json`;

        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }

        let chatTempMemory;
        // 判断文件是否存在，不存在则自动创建路径
        if (!fs.existsSync(filePath)) {
            chatTempMemory = {};
        }
        else {
            // 加载文件
            const data = fs.readFileSync(filePath, 'utf8');
            chatTempMemory = JSON.parse(data);
        }

        const memory = {};
        const tag = '_sys_';
        for (const prop in chatTempMemory) {
            if (prop.startsWith(tag)) {
                memory[prop.substring(tag.length)] = chatTempMemory[prop];
            }
        }
        return memory;
    }

    /**
     *
     * @param user
     * @param sessionId
     * @param key
     * @returns {*}
     */
    get(user, sessionId, key) {
        const sac = user.sac;
        const userId = user.username;

        const { fs } = this.ctx.imports;
        const dirPath = `${this.ctx.rootPath}/data/cs-mini-agent/chat-temp-memory/${sac}/${userId}`;
        const filePath = `${dirPath}/${sessionId}.json`;

        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }

        let chatTempMemory;
        // 判断文件是否存在，不存在则自动创建路径
        if (!fs.existsSync(filePath)) {
            chatTempMemory = {};
        }
        else {
            // 加载文件
            const data = fs.readFileSync(filePath, 'utf8');
            chatTempMemory = JSON.parse(data);
        }

        return chatTempMemory[key];
    }

    /**
     *
     * @param user
     * @param sessionId
     * @param key
     * @param value
     */
    set(user, sessionId, key, value) {
        const sac = user.sac;
        const userId = user.username;

        const { fs } = this.ctx.imports;
        const dirPath = `${this.ctx.rootPath}/data/cs-mini-agent/chat-temp-memory/${sac}/${userId}`;
        const filePath = `${dirPath}/${sessionId}.json`;

        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }

        let chatTempMemory;
        // 判断文件是否存在，不存在则自动创建路径
        if (!fs.existsSync(filePath)) {
            chatTempMemory = {};
        }
        else {
            // 加载文件
            const data = fs.readFileSync(filePath, 'utf8');
            chatTempMemory = JSON.parse(data);
        }
        chatTempMemory[key] = value;

        // 保存到文件
        fs.writeFileSync(filePath, JSON.stringify(chatTempMemory));
    }

    /**
     *
     * @param user
     * @param sessionId
     * @param key
     */
    remove(user, sessionId, key) {
        const sac = user.sac;
        const userId = user.username;

        const { fs } = this.ctx.imports;
        const dirPath = `${this.ctx.rootPath}/data/cs-mini-agent/chat-temp-memory/${sac}/${userId}`;
        const filePath = `${dirPath}/${sessionId}.json`;

        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }

        let chatTempMemory;
        // 判断文件是否存在，不存在则自动创建路径
        if (!fs.existsSync(filePath)) {
            chatTempMemory = {};
        }
        else {
            // 加载文件
            const data = fs.readFileSync(filePath, 'utf8');
            chatTempMemory = JSON.parse(data);
        }
        delete chatTempMemory[key];

        // 保存到文件
        fs.writeFileSync(filePath, JSON.stringify(chatTempMemory));
    }

    /**
     *
     * @param user
     * @param sessionId
     * @param keyStart
     */
    removeByStart(user, sessionId, keyStart) {
        const sac = user.sac;
        const userId = user.username;

        const { fs } = this.ctx.imports;
        const dirPath = `${this.ctx.rootPath}/data/cs-mini-agent/chat-temp-memory/${sac}/${userId}`;
        const filePath = `${dirPath}/${sessionId}.json`;

        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }

        let chatTempMemory;
        // 判断文件是否存在，不存在则自动创建路径
        if (!fs.existsSync(filePath)) {
            chatTempMemory = {};
        }
        else {
            // 加载文件
            const data = fs.readFileSync(filePath, 'utf8');
            chatTempMemory = JSON.parse(data);
        }

        for (const prop in chatTempMemory) {
            if (prop.startsWith(keyStart)) {
                delete chatTempMemory[prop];
            }
        }

        // 保存到文件
        fs.writeFileSync(filePath, JSON.stringify(chatTempMemory));
    }
}