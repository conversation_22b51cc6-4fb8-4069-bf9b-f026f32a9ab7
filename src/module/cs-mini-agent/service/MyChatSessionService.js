/**
 * 我的聊天会话服务
 */
export default class MyChatSessionService {
    constructor(ctx, parent) {
        this.ctx = ctx;
        this.parent = parent;
    }

    /**
     * 获取我的聊天会话列表
     * @param {String} userId 用户ID（用户名）
     * @param {String} kw 搜索关键字
     * @returns {Array} 我的聊天会话列表
     */
    async getSessions(sac, userId, kw) {
        const { fs } = this.ctx.imports;
        let filePath = `${this.ctx.rootPath}/data/cs-mini-agent/my-chat-session-list/${sac}/${userId}.json`;
        // 判断文件是否存在
        if (!fs.existsSync(filePath)) {
            // 加载授权域公共会话列表
            filePath = `${this.ctx.rootPath}/data/cs-mini-agent/my-chat-session-list/${sac}/_.json`;
            if (!fs.existsSync(filePath)) {
                return [];
            }
        }
        const data = fs.readFileSync(filePath, 'utf8');
        const sessions = JSON.parse(data);

        for (const session of sessions) {
            for (const member of session.otherMembers) {
                const agent = this.parent.agentService.getCustomAgent(member.id);
                if (member.name == null) member.name = agent.getName();
                if (member.summary == null) member.summary = agent.getSummary();
            }
        }

        return sessions;
    }

    /**
     * 
     * @param {*} sac 
     * @param {*} userId 用户ID（用户名）
     * @param {*} sessionId 
     * @returns 
     */
    async getSession(sac, userId, sessionId) {
        const data = await this.getSessions(sac, userId);
        return data.find(session => session.id === sessionId);
    }

    // /**
    //  * 推送聊天会话历史记录
    //  * @param {String} userId 用户ID
    //  * @param {String} sessionId 会话ID
    //  * @param {Object} chatHistoryItem 聊天会话历史记录
    //  */
    // async pushChatHistoryItem(sac, userId, sessionId, chatHistoryItem) {
    //     await this.ctx.csMiniAgentService.chatSessionHistoryService
    //         .pushChatHistoryItem(sac, userId, sessionId, chatHistoryItem);
    // }
    //
    // /**
    //  * 获取聊天会话历史记录
    //  * @param {String} userId 用户ID
    //  * @param {String} sessionId 会话ID
    //  * @returns {Array} 聊天会话历史记录
    //  */
    // async getChatHistory(sac, userId, sessionId, limit) {
    //     return await this.ctx.csMiniAgentService.chatSessionHistoryService.getChatHistory(sac, userId, sessionId, limit);
    // }
    //
    // /**
    //  * 删除聊天会话历史记录
    //  * @param {String} userId 用户ID
    //  * @param {String} sessionId 会话ID
    //  * @param {String} id 历史记录ID
    //  */
    // async deleteChatHistoryItem(sac, userId, sessionId, id) {
    //     await this.ctx.csMiniAgentService.chatSessionHistoryService.deleteChatHistoryItem(sac, userId, sessionId, id);
    // }
}