import ChatModelAgentModel from "../model/agents/ChatModelAgentModel.js";
import { reqGet, reqPostJson, reqPostForm, reqGetSync, reqPostJsonSync, reqPostFormSync } from "../../../util/http-util.js";
import { toDateTimeStr, toDateStr, toTimeStr } from '../../../util/time-util';
import { buildMarkdownTable } from "../../../util/markdown-table-util.js";
import { clearHtml } from "../../../util/html-util.js";
import {ChatUCAModel} from "../model/agents/ChatUCAModel";
import ChatKbAgentModel from "../model/agents/ChatKbAgentModel";
import {ChatGameAAgentModel} from "../model/agents/ChatGameAAgentModel";
import {ChatSuperAgentInTUIModel} from "../model/agents/ChatSuperAgentInTUIModel";

/**
 * 智能体服务
 */
export default class AgentService {

    constructor(ctx) {
        this.ctx = ctx;

        this.agents = [
            new ChatModelAgentModel(ctx),
            new ChatUCAModel(ctx),
            new ChatKbAgentModel(ctx),
            new ChatGameAAgentModel(ctx),
            new ChatSuperAgentInTUIModel(ctx),
        ];
    }

    /**
     * 根据ID创建智能体
     * @param {String} id 智能体ID
     * @returns {Object} 智能体
     */
    newAgentById(id) {
        // 在 agents 数组中查找匹配的智能体
        const agent = this.agents.find(agent => agent.getId() === id);
        if (agent) {
            // 如果找到了匹配的智能体，创建它的新实例
            return new agent.constructor(this.ctx);
        }
        // if (id === 'chat-model') {
        //     return new ChatModelAgentModel(this.ctx);
        // }
        return null;
    }

    /**
     * 获取智能体
     * @param {String} id 智能体ID
     * @returns {Object} 智能体
     */
    getAgent(id, opts = {}) {
        const customAgent = this.getCustomAgent(id, opts);
        if (customAgent) {
            return customAgent;
        }
        return this.agents.find(agent => agent.getId() === id);
    }

    /**
     * 获取自定义智能体
     * @param {String} id 智能体ID
     * @returns {Object} 自定义智能体
     */
    getCustomAgent(id, opts = {}) {
        const { fs } = this.ctx.imports;
        const idArr = id.split('_');
        const filePath = `${this.ctx.rootPath}/data/cs-mini-agent/custom-agent/${idArr[0]}/${idArr[1]}.js`;

        if (fs.existsSync(filePath)) {
            // 读取文件内容
            const content = fs.readFileSync(filePath, 'utf8');
            // 优先查找零号智能体
            for (const agent of this.agents) {
                if (agent.getId() === id) {
                    return agent;
                }
            }
            // 执行文件内容
            let customAgent = eval(content)(this.getAgentContext({ agent: null }, opts));
            let findAgent;
            // 查找父级智能体
            for (const agent of this.agents) {
                if (agent.getId() === customAgent.parentId) {
                    // 子级继承父级
                    // 克隆父级
                    const newAgent = this.newAgentById(agent.getId());
                    // 修改参数
                    newAgent.id = id;
                    newAgent.parent = agent;
                    if (customAgent.name) newAgent.name = customAgent.name;
                    if (customAgent.summary) newAgent.summary = customAgent.summary;
                    newAgent.setSettings(customAgent.settings);
                    newAgent.setFrontSettings(customAgent.frontSettings);
                    findAgent = newAgent;
                    break;
                }
            }

            customAgent = eval(content)(this.getAgentContext({ agent: findAgent }, opts));
            findAgent.setSettings(customAgent.settings);
            findAgent.setFrontSettings(customAgent.frontSettings);
            return findAgent;
        }
        return null;
    }

    /**
     * 获取智能体前端设置字符串
     * @param {String} id 智能体ID
     * @returns {String} 智能体前端设置字符串
     */
    getAgentFrontSettingsStr(id) {
        const { fs } = this.ctx.imports;
        const idArr = id.split('_');
        const filePath = `${this.ctx.rootPath}/data/cs-mini-agent/custom-agent/${idArr[0]}/${idArr[1]}.front-settings.js`;
        if (fs.existsSync(filePath)) {
            // 读取文件内容
            const content = fs.readFileSync(filePath, 'utf8');
            // 执行文件内容
            return content;
        }
        return null;
    }



    /**
     * 获取智能体工具集
     * @param {*} agent agent
     * @param {*} opts
     * @returns 
     */
    async getAgentTools(pars, opts) {
        const { agent } = pars;
        const { fs } = this.ctx.imports;

        const idArr = agent.getId().split('_');
        const filePath = `${this.ctx.rootPath}/data/cs-mini-agent/custom-agent/${idArr[0]}/${idArr[1]}.tools.js`;
        if (fs.existsSync(filePath)) {
            // 读取文件内容
            const content = fs.readFileSync(filePath, 'utf8');
            const tools = eval(content)(this.getAgentContext(pars, opts));
            return tools;
        }
        return null;
    }

    /**
     * 执行脚本：getSystemMessage
     * @param {*} pars agent, chatHistory, message
     * @param {*} opts
     */
    async execScript_getSystemMessage(pars, opts = {}) {
        const { agent, chatHistory, message } = pars;
        let settings = agent.getSettings();

        if (settings && settings.getSystemMessage) {
            try {
                const systemMessage = settings.getSystemMessage({
                    agent, model: message.opts.model,
                }, opts, this.getAgentContext({ agent }, opts));
                return systemMessage;
            } catch (exc) {
                log(`调用脚本异常 | getSystemMessage -> ${exc.message}`);
            }
        }
    }

    /**
     *
     * @param pars agent, systemMessage, type: 'chat'|'tool'
     * @param opts
     * @returns {Promise<*>}
     */
    async execScript_buildSystemMessageBeforeChat(pars, opts = {}) {
        const { agent, systemMessage, type } = pars;
        let settings = agent.getSettings();

        if (settings && settings.buildSystemMessageBeforeChat) {
            try {
                const systemMessage = settings.buildSystemMessageBeforeChat(
                    { ...pars },
                    opts,
                    this.getAgentContext({ agent }, opts)
                );
                return systemMessage;
            } catch (exc) {
                log(`调用脚本异常 | buildSystemMessageBeforeChat -> ${exc.message}`);
            }
        }
        return pars.systemMessage;
    }

    async execScript_getChatHistory(pars) {
        const { agent, chatHistory, message } = pars;
        let settings = agent.getSettings();

        if (settings && settings.getChatHistory) {
            try {
                const r = settings.getChatHistory({
                    model: message.opts.model,
                    chatHistory,
                });
                return r;
            } catch (exc) {
                log(`调用脚本异常 | getChatHistory -> ${exc.message}`);
            }
        }
        return chatHistory;
    }

    /**
     * 执行脚本 -> 调用工具
     * @param {*} pars agent, name, params
     * @param {*} opts loginUser
     * @returns 
     */
    async execScript_callTool(pars, opts = {}) {
        let { agent, name, params, tools } = pars;

        // if (opts.debug) {
        //     log(`调用工具 | ${name} | ${JSON.stringify(params)} ...`);
        // }

        try {
            if (tools == null) {
                tools = await this.getAgentTools(this.getAgentContext({ agent }, opts), opts);
            }
            if (tools) {
                for (const i in tools) {
                    const tool = tools[i];
                    if (tool.name === name) {
                        const r = await tool.call(params, opts);
                        return r;
                    }
                }
            }
            log(`未能找到工具 -> ${name}`);
        }
        catch (exc) {
            throw new Error(`调用工具异常 | ${name} -> ${exc.message}`);
        }
        return null;
    }

    /**
     * 执行脚本 -> 执行插件
     * @param {*} pars agent, name, params
     * @param {*} opts loginUser
     */
    async execScript_execPlugin(pars, opts = {}) {
        const { agent, name, params } = pars;
        const { fs } = this.ctx.imports;

        if (opts.debug) {
            log(`调用插件 | ${name} | ${JSON.stringify(params)} ...`);
        }

        try {
            const idArr = agent.getId().split('_');
            const filePath = `${this.ctx.rootPath}/data/cs-mini-agent/custom-agent/${idArr[0]}/${idArr[1]}.plugins.js`;
            if (fs.existsSync(filePath)) {
                // 读取文件内容
                const content = fs.readFileSync(filePath, 'utf8');
                const plugin = eval(content)(this.getAgentContext({ agent }, opts))[name];
                if (plugin) {
                    const r = await plugin.exec(params);
                    return r;
                }
                else {
                    throw new Error(`plugin不能为null`);
                }
            }
        }
        catch (exc) {
            throw new Error(`调用插件异常 | ${name} -> ${exc.message} -> ${exc.stack}`);
        }
        return null;
    }

    getAgentContext(pars, opts = {}) {
        const self = this;
        const { agent } = pars;

        return {
            agentService: self,
            agent,
            loginUser: opts.loginUser,
            strUtil: {
                buildMarkdownTable: buildMarkdownTable,
            },
            htmlUtil: {
                clearHtml: clearHtml,
            },
            timeUtil: {
                toDateTimeStr: toDateTimeStr,
                toDateStr: toDateStr,
                toTimeStr: toTimeStr,
            },
            httpUtil: {
                reqGet: reqGet,
                reqPostJson: reqPostJson,
                reqPostForm: reqPostForm,
                reqGetSync: reqGetSync,
                reqPostJsonSync: reqPostJsonSync,
                reqPostFormSync: reqPostFormSync,
            },
            // 对话临时记忆工具
            chatTempMemoryUtil: {
                // 获取临时记忆
                get(key) {
                    return self.ctx.csMiniAgentService.chatTempMemoryService.get(opts.loginUser, opts.sessionId, key);
                },
                // 设置临时记忆
                set(key, value) {
                    return self.ctx.csMiniAgentService.chatTempMemoryService.set(opts.loginUser, opts.sessionId, key, value);
                },
                // 移除临时记忆
                remove(key) {
                    return self.ctx.csMiniAgentService.chatTempMemoryService.remove(opts.loginUser, opts.sessionId, key);
                },
                // 获取临时系统记忆
                getSys(key) {
                    return self.ctx.csMiniAgentService.chatTempMemoryService.get(opts.loginUser, opts.sessionId, `_sys_${key}`);
                },
                // 设置临时系统记忆（系统记忆会每次对话时加入到system题词中）
                setSys(key, value) {
                    return self.ctx.csMiniAgentService.chatTempMemoryService.set(opts.loginUser, opts.sessionId, `_sys_${key}`, value);
                },
                // 移除临时系统记忆
                removeSys(key) {
                    return self.ctx.csMiniAgentService.chatTempMemoryService.remove(opts.loginUser, opts.sessionId, `_sys_${key}`);
                },
                // 清空临时系统记忆
                clearSys() {
                    return self.ctx.csMiniAgentService.chatTempMemoryService.removeByStart(opts.loginUser, opts.sessionId, `_sys_`);
                },
            },
            getConfig() {
                return agent.getSettings().config;
            },
            getService(name) {
                return self.ctx.shortTimeCacheService[`get${name}`]();
            },
            async execPlugin(pars) {
                const { name, params } = pars;
                return await self.execScript_execPlugin({ agent, name, params }, opts);
            },
            async callTool(pars) {
                const { name, params } = pars;
                return await self.execScript_callTool({ agent, name, params }, opts);
            }
        };
    }

}
