/**
 * 聊天会话服务
 */
export default class ChatSessionService {
    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     * 设置私密聊天会话
     * @param {String} memberId1 成员1ID
     * @param {String} memberId2 成员2ID
     * @param {Object} data 会话数据
     * @returns {ChatSession} 私密聊天会话
     */
    setPrivateSession(memberId1, memberId2, data) {
        let sessionId = this.createPrivateSessionId(memberId1, memberId2);
        let session = {
            memberIds: [memberId1, memberId2],
            data: data
        };
        // 检查路径是否存在，如果不存在则自动创建
        let sessionSaveDirPath = this.getSessionSaveDirPath();
        if (!fs.existsSync(sessionSaveDirPath)) {
            fs.mkdirSync(sessionSaveDirPath, { recursive: true });
        }
        // 保存到文件
        let filePath = `${sessionSaveDirPath}/${sessionId}.json`;
        fs.writeFileSync(filePath, JSON.stringify(session));
        return session;
    }

    /**
     * 获取私密聊天会话
     * @param {String} memberId1 成员1ID
     * @param {String} memberId2 成员2ID
     * @returns {ChatSession} 私密聊天会话
     */
    getPrivateSession(memberId1, memberId2) {
        let sessionId = this.createPrivateSessionId(memberId1, memberId2);
        return this.getSession(sessionId);
    }

    /**
     * 获取聊天会话
     * @param {String} sessionId 会话ID
     * @returns {ChatSession} 聊天会话
     */
    getSession(sessionId) {
        let filePath = `${this.getSessionSaveDirPath()}/${sessionId}.json`;
        // 判断是否存在
        if (fs.existsSync(filePath)) {
            let session = fs.readFileSync(filePath, 'utf8');
            if (session && session.length > 0) {
                return JSON.parse(session);
            }
        }
        return null;
    }

    /**
     * 创建私密聊天会话ID
     * @param {String} memberId1 成员1ID
     * @param {String} memberId2 成员2ID
     * @returns {String} 私密聊天会话ID
     */
    createPrivateSessionId(memberId1, memberId2) {
        const list = [memberId1, memberId2];
        list.sort();
        return list.join('_');
    }

    /**
     * 获取聊天会话保存目录
     * @returns {String} 聊天会话保存目录
     */
    getSessionSaveDirPath() {
        return `${this.ctx.rootPath}/data/cs-mini-agent/session`;
    }
}