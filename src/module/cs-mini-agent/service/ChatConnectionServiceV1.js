/**
 * 聊天连接服务 v1 （WebSocket）
 * sessionId 是用户的session的id，可以从controller里获取
 * 前端代码：const ws = new WebSocket(`ws://localhost:8080?sessionId=${sessionId}`);
 */
export default class ChatConnectionServiceV1 {
    constructor(ctx, parent, cfg) {
        this.ctx = ctx;
        this.parent = parent;

        if (cfg) {
            if (cfg.port) {
                this.port = cfg.port;
            }
        }

        this.wss = null;
        // 使用Map存储sessionId和ws的映射
        this.clients = new Map();
    }

    start() {
        const { WebSocket } = this.ctx.imports;
        this.wss = new WebSocket.Server({ port: this.port });
        console.log(`WebSocket服务器启动在端口 ${this.port}`);

        this.wss.on('connection', (ws, req) => {
            console.log('新的客户端连接');
            
            // 从URL中获取sessionId
            const urlParams = new URL(req.url, 'ws://localhost').searchParams;
            let sessionId = urlParams.get('sessionId');
            
            // 将ws和sessionId关联存储
            if (sessionId) {
                this.clients.set(sessionId, ws);
                console.log(`客户端 ${sessionId} 已连接`);
            }

            // 处理接收到的消息
            ws.on('message', (message) => {
                try {
                    const data = JSON.parse(message);
                    console.log('收到消息:', data);
                    
                    // 如果是认证消息，保存sessionId
                    if (data.type === 'auth' && data.sessionId) {
                        sessionId = data.sessionId;
                        this.clients.set(sessionId, ws);
                        console.log(`客户端 ${sessionId} 已认证`);
                        return;
                    }
                    
                    // 广播消息给所有连接的客户端
                    this.broadcast(message);
                } catch (error) {
                    console.error('消息处理错误:', error);
                }
            });

            // 处理客户端断开连接
            ws.on('close', () => {
                console.log(`客户端 ${sessionId} 断开连接`);
                if (sessionId) {
                    this.clients.delete(sessionId);
                }
            });

            // 处理错误
            ws.on('error', (error) => {
                console.error('WebSocket错误:', error);
                if (sessionId) {
                    this.clients.delete(sessionId);
                }
            });

            // 发送欢迎消息
            ws.send(JSON.stringify({
                type: 'system',
                message: '欢迎连接到WebSocket服务器',
                sessionId: sessionId
            }));
        });
    }

    // 获取指定sessionId的WebSocket连接
    getClient(sessionId) {
        return this.clients.get(sessionId);
    }

    // 向指定sessionId的客户端发送消息
    sendToClient(sessionId, message) {
        const client = this.getClient(sessionId);
        if (client && client.readyState === WebSocket.OPEN) {
            client.send(typeof message === 'string' ? message : JSON.stringify(message));
            return true;
        }
        return false;
    }

    // 广播消息给所有客户端
    broadcast(message) {
        for (const [sessionId, client] of this.clients) {
            if (client.readyState === WebSocket.OPEN) {
                client.send(typeof message === 'string' ? message : JSON.stringify(message));
            }
        }
    }

    // 关闭服务器
    stop() {
        if (this.wss) {
            this.wss.close(() => {
                console.log('WebSocket服务器已关闭');
            });
        }
    }
}