
import ChatController from "../controller/ChatController";
import AgentController from "../controller/AgentController";
import QywxCbChatController from "../controller/QywxCbChatController";

import ChatConnectionServiceV1 from "./ChatConnectionServiceV1";
import MyChatSessionService from "./MyChatSessionService";
import ChatSessionService from "./ChatSessionService";
import ChatService from "./ChatService";
import AgentService from "./AgentService";
import ChatSessionHistoryService from "./ChatSessionHistoryService";
import {ChatTempMemoryService} from "./ChatTempMemoryService";
import UserCreateAgentController from "../controller/UserCreateAgentController";

export default class CSMiniAgentService {
    constructor(ctx, cfg) {
        this.ctx = ctx;
        this.cfg = cfg;
    }

    start(pars) {
        // 注册聊天控制器
        if (pars.app) {
            new AgentController(this.ctx, pars.app);
            new ChatController(this.ctx, pars.app);
            new UserCreateAgentController(this.ctx, pars.app);
        }

        if (this.cfg) {
            if (this.cfg.qywx) {
                if (this.cfg.qywx.corps) {
                    this.cfg.qywx.corps.forEach(corp => {
                        if (corp.apps) {
                            corp.apps.forEach(app => {
                                if (pars.app) {
                                    new QywxCbChatController(this.ctx, pars.app, {
                                        ...app,
                                        ...app.receiveMessages,
                                        qywxCorp: corp,
                                    }, this);
                                }
                            });
                        }
                    });
                }
            }
        }
        
        // 注册服务
        this.agentService = new AgentService(this.ctx, this);
        this.chatService = new ChatService(this.ctx, this);
        this.chatSessionService = new ChatSessionService(this.ctx, this);
        this.myChatSessionService = new MyChatSessionService(this.ctx, this);
        this.chatSessionHistoryService = new ChatSessionHistoryService(this.ctx, this);
        this.chatTempMemoryService = new ChatTempMemoryService(this.ctx, this);

        // 注册聊天连接服务
        this.ctx.chatConnectionServiceV1 = new ChatConnectionServiceV1(this.ctx, this, {
            port: this.ctx.config.cs_mini_agent.chatWsPort
        });
    }
}