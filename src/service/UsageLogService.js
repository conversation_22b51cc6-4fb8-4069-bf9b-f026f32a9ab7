import {UserActionLogService} from "./UserActionLogService";

/**
 * 使用量日志
 */
export class UsageLogService extends UserActionLogService {

    constructor(ctx) {
        super(ctx);
    }

    /**
     *
     * @param sac
     * @param userId
     * @param appKey
     * @param item sac, user_id, user, input_words, output_words, model
     */
    push(appKey, item) {
        const { sac, user_id } = item;
        let groupKey = `${appKey}/${sac}/${user_id}`;
        super.push(groupKey, JSON.stringify(item));
    }

    _getStartDirName() {
        return 'usage-log';
    }
}