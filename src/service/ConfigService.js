import yaml from "yaml";

export default class ConfigService {
    constructor(ctx) {
        this.ctx = ctx;
    }

    load() {
        const { fs } = this.ctx.imports;
        const config = {};
        let filePath = this.ctx.rootPath.concat('/config/app.yml');
        let devFilePath = this.ctx.rootPath.concat('/config/app-dev.yml');
        let prodFilePath = this.ctx.rootPath.concat('/config/app-prod.yml');
        if (fs.existsSync(filePath)) {
            const nativeObj = this.loadYamlFile(filePath);
            for (const i in nativeObj) {
                config[i] = nativeObj[i];
            }
        }
        if (config.env === 'dev' && fs.existsSync(devFilePath)) {
            const nativeObj = this.loadYamlFile(devFilePath);
            for (const i in nativeObj) {
                config[i] = nativeObj[i];
            }
        }
        if (config.env === 'prod' && fs.existsSync(prodFilePath)) {
            const nativeObj = this.loadYamlFile(prodFilePath);
            for (const i in nativeObj) {
                config[i] = nativeObj[i];
            }
        }
        return config;
    }

    loadYamlFile(filePath) {
        const { fs } = this.ctx.imports;
        const content = fs.readFileSync(filePath, 'utf8')
        return yaml.parse(content);
    }
}

// module.exports = ConfigService;