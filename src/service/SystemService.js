import ConfigService from "./ConfigService";
import ShortTimeCacheService from "./ShortTimeCacheService";

export default class SystemService {
    constructor(ctx) {
        this.ctx = ctx;
    }

    init(opts = {}) {
        const { path } = this.ctx.imports;
        const ctx = this.ctx;

        if (opts.rootPath) {
            ctx.rootPath = opts.rootPath;
        }
        else {
            ctx.rootPath = path.resolve();
        }
        ctx.configService = new ConfigService(ctx);
        ctx.config = ctx.configService.load();
        ctx.shortTimeCacheService = new ShortTimeCacheService(ctx);
    }
}