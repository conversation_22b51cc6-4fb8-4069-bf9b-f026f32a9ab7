import QywxService from "../module/weixin/service/QywxService";
import AnythingLLMMapper from "../mapper/AnythingLLMMapper";
import CSBotKb0Mapper from "../mapper/CSBotKb0Mapper";
import CSBotKb0V2Mapper from "../mapper/CSBotKb0V2Mapper";
import BaiduQianFanAppBuilderMapper from "../mapper/BaiduQianFanAppBuilderMapper";
import OllamaMapper from "../mapper/OllamaMapper";
import CSMiniKbService from "../module/cs-mini-kb/service/CSMiniKbService";
import OpenAIBrainWildMapper from "../mapper/OpenAIBrainWildMapper";
import OpenAIBrainMapper from "../mapper/OpenAIBrainMapper";
import Moonshot<PERSON>IMapper from "../mapper/MoonshotAIMapper";
import KbMapper from "../mapper/KbMapper";
import DeepSeekMapper from "../mapper/DeepSeekMapper";
import ALiBaiLianMapper from "../mapper/ALiBaiLianMapper";
import SiliconflowMapper from "../mapper/SiliconflowMapper";
import LoginService from "../module/user/service/LoginService";
import AnthropicBrainWildMapper from "../mapper/AnthropicBrainWildMapper";
import MiniMaxMapper from "../mapper/MiniMaxMapper";
import ZhiPuMapper from "../mapper/ZhiPuMapper";
import DataSourceService from "../module/cs-data-source/service/DataSourceService";
import {UserActionLogService} from "./UserActionLogService";
import {UsageLogService} from "./UsageLogService";
import {Token_29e65a8f_Service} from "./Token_29e65a8f_Service";
import KbDataSourceService from "../module/cs-mini-kb/service/KbDataSourceService";


/**
 * 短时间缓存服务
 * （如果一段时间不用的缓存就会自动消除）
 */
export default class ShortTimeCacheService {

    constructor(ctx) {
        this.ctx = ctx;
        this._cache = {};
        this._timeout = 1000 * 60;

        this.thread();
    }

    /**
     *
     * @returns {LoginService}
     */
    getLoginService() {
        const self = this;
        const key = 'loginService';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new LoginService(ctx, ctx.config);
        });
    }

    /**
     *
     * @returns {*}
     */
    getUserActionLogService() {
        const self = this;
        const key = 'userActionLogService';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new UserActionLogService(ctx, ctx.config);
        });
    }

    /**
     *
     * @returns {*}
     */
    getUsageLogService() {
        const self = this;
        const key = 'usageLogService';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new UsageLogService(ctx, ctx.config);
        });
    }

    /**
     *
     * @returns {DataSourceService}
     */
    getDataSourceService() {
        const self = this;
        const key = 'dataSourceService';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new DataSourceService(ctx, ctx.config);
        });
    }

    getToken_29e65a8f_Service() {
        const self = this;
        const key = 'token_29e65a8f_Service';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new Token_29e65a8f_Service(ctx, ctx.config);
        });
    }

    /**
     * 
     * @returns {QywxService}
     */
    getQywxService() {
        const self = this;
        const key = 'qywxService';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new QywxService(ctx);
        });
    }

    /**
     *
     * @returns {ChatModel}
     */
    getChatModel() {
        const self = this;
        const key = 'chatModel';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new ctx.imports.ChatModel(ctx);
        });
    }

    /**
     *
     * @returns {CSMiniKbService}
     */
    getCSMiniKbService() {
        const self = this;
        const key = 'CSMiniKbService';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new CSMiniKbService(ctx, ctx.config.cs_mini_kb);
        });
    }

    /**
     *
     * @returns {KbDataSourceService}
     */
    getKbDataSourceService() {
        const self = this;
        const key = 'KbDataSourceService';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new KbDataSourceService(ctx);
        });
    }

    /**
     *
     * @returns {CSBotKb0Mapper}
     */
    getCSBotKb0Mapper() {
        const self = this;
        const key = 'CSBotKb0Mapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new CSBotKb0Mapper(ctx, ctx.config.cs_bot_kb0);
        });
    }

    /**
     *
     * @returns {CSBotKb0V2Mapper}
     */
    getCSBotKb0V2Mapper() {
        const self = this;
        const key = 'csBotKb0V2Mapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new CSBotKb0V2Mapper(ctx, ctx.config.cs_bot_kb0_v2);
        });
    }

    /**
     *
     * @returns {BaiduQianFanAppBuilderMapper}
     */
    getBaiduQianFanAppBuilderMapper() {
        const self = this;
        const key = 'baiduQianFanAppBuilderMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new BaiduQianFanAppBuilderMapper(ctx, ctx.config.bd_qf_ab);
        });
    }

    /**
     *
     * @returns {OllamaMapper}
     */
    getOllamaMapper() {
        const self = this;
        const key = 'ollamaMMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new OllamaMapper(ctx, ctx.config.ollama);
        });
    }

    /**
     *
     * @returns {OpenAIBrainMapper}
     */
    getOpenAIBrainMapper() {
        const self = this;
        const key = 'openAIBrainMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new OpenAIBrainMapper(ctx, ctx.config.openai_brain);
        });
    }

    /**
     *
     * @returns {OpenAIBrainWildMapper}
     */
    getOpenAIBrainWildMapper() {
        const self = this;
        const key = 'openAIBrainWildMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new OpenAIBrainWildMapper(ctx, ctx.config.openai_brain_wild);
        });
    }

    /**
     *
     * @returns {AnthropicBrainWildMapper}
     */
    getAnthropicBrainWildMapper() {
        const self = this;
        const key = 'anthropicBrainWildMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new AnthropicBrainWildMapper(ctx, null);
        });
    }

    /**
     *
     * @returns {MoonshotAIMapper}
     */
    getMoonshotAIMapper() {
        const self = this;
        const key = 'moonshotAIMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new MoonshotAIMapper(ctx, ctx.config.moonshot_ai);
        });
    }

    /**
     *
     * @returns {DeepSeekMapper}
     */
    getDeepSeekMapper() {
        const self = this;
        const key = 'deepSeekMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new DeepSeekMapper(ctx, ctx.config.deepseek);
        });
    }

    /**
     *
     * @returns {ZhiPuMapper}
     */
    getZhiPuMapper() {
        const self = this;
        const key = 'zhiPuMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new ZhiPuMapper(ctx, ctx.config.zhipu);
        });
    }

    /**
     *
     * @returns {MiniMaxMapper}
     */
    getMiniMaxMapper() {
        const self = this;
        const key = 'miniMaxMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new MiniMaxMapper(ctx, ctx.config.deepseek);
        });
    }

    /**
     *
     * @returns {ALiBaiLianMapper}
     */
    getALiBaiLianMapper() {
        const self = this;
        const key = 'aLiBaiLianMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new ALiBaiLianMapper(ctx, ctx.config.al_bl);
        });
    }

    /**
     *
     * @returns {SiliconflowMapper}
     */
    getSiliconflowMapper() {
        const self = this;
        const key = 'siliconflowMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new SiliconflowMapper(ctx, ctx.config.siliconflow);
        });
    }

    /**
     *
     * @returns {KbMapper}
     */
    getKbMapper() {
        const self = this;
        const key = 'kbMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new KbMapper(ctx, {});
        });
    }

    /**
     *
     * @returns {AnythingLLMMapper}
     */
    getAnythingLLMMapper() {
        const self = this;
        const key = 'anythingLLMMapper';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new AnythingLLMMapper(ctx, ctx.config.anythingllm);
        });
    }

    /**
     * 获取花村并更新使用时间
     * @param key
     * @param newFunc
     * @returns {*}
     */
    get(key, newFunc) {
        const cache = this._cache[key];
        if (cache == null) {
            const t = newFunc();
            this._cache[key] = {
                data: t,
                time: Date.now()
            };
            return t;
        }
        else {
            cache.time = Date.now();
            return cache.data;
        }
    }

    /**
     * 线程
     */
    thread() {
        const self = this;

        for (const prop in this._cache) {
            const obj = this._cache[prop];
            if (Date.now() - obj.time > self._timeout) {
                delete this._cache[prop];
            }
        }

        setTimeout(() => {
            self.thread();
        }, 1000 * 10);
    }
}