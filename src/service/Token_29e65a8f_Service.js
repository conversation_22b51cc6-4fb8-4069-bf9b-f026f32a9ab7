import CSDESA from "../util/CSDESA";

export class Token_29e65a8f_Service {
    constructor(ctx) {
        this.ctx = ctx;
        this._token_key = '7dbf8dea';
    }

    buildToken(sac, un, skey, opts = {}) {
        const tokenObj = {
            sac: sac,
            un: un,
            skey: skey,
            time: Date.now(),
        };
        if (opts.time != null) {
            tokenObj.time = opts.time;
        }

        const csdesa = new CSDESA();
        return csdesa.encrypt(this._token_key, JSON.stringify(tokenObj));
    }

    parseToken(token) {
        const csdesa = new CSDESA();
        const json = csdesa.decrypt(this._token_key, token);
        return JSON.parse(json);
    }
}