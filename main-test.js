const express = require('express');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');

const lancedb = require("@lancedb/lancedb");
const apacheArrow = require("apache-arrow");

// 用于主项目执行
const {start} = require('./src/main-start');
// 用于派生项目执行
// const {start} = require('./lib/main-start');

const imports = {
    express, bodyParser,
    fs, path,
    lancedb, apacheArrow,
};
const ctx = start(imports, {
    startServer: false,
});

console.log(`${path.extname('ababaaf.txt')}`)