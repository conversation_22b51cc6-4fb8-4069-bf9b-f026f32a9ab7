#!/usr/bin/env bun

const express = require('express');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');
import {Buffer} from "buffer";
const crypto = require('crypto');
const os = require('os');


// 用于主项目执行
const {start} = require('./src/main-start');
// 用于派生项目执行
// const {start} = require('./lib/main-start');

const ctx = start({
    express, bodyParser,
    fs, path,
    Buffer, crypto,
    os,
}, {
    rootPath: __dirname,
    startServer: false,
    startCSMiniAgentServer: true,
    startWeiXinServer: false,
})

const readline = require('readline');
// 方法1: 使用 readline 模块（推荐）
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 异步获取输入
function getInputAsync() {
    return new Promise((resolve) => {
        rl.question('请输入: ', (answer) => {
            resolve(answer);
        });
    });
}

// 示例程序
async function main() {
    // 使用 readline 获取输入
    process.stdout.write(`\n=== 开始对话（输入 /exit 退出） ===\n`);
    process.stdout.write(`当前目录：${process.cwd()}\n`)
    process.stdout.write(`主机名：${os.hostname()}\n`)
    process.stdout.write(`操作系统类型：${os.type()}\n`)
    process.stdout.write(`平台：${os.platform()}\n`)
    process.stdout.write(`CPU 架构：${os.arch()}\n`)
    // process.stdout.write(`CPU 信息：${JSON.stringify(os.cpus())}\n`)
    while (true) {
        const inputText = await getInputAsync();
        // 斜杠命令
        if (inputText === '/exit') {
            process.exit();
        }

        let loginUser = {
            sac: 'bot.test',
            un: 'root',
            rn: '根',
        };
        let sessionId = 'chat-super-agent-in-tui-0';
        let senderId = loginUser.un;
        let messageId = Date.now();

        const r = await ctx.csMiniAgentService.chatService.sendMessage(loginUser.sac, {
            id: messageId,
            content: inputText,
            sessionId: sessionId,
            senderId: senderId,
            receiverId: `${loginUser.sac}_chat-super-agent-in-tui-0`,
            time: Date.now(),
        }, {
            loginUser,
            // 越大越详细
            // 0 屏蔽，null 全部，1 业务信息，2 一般调试信息，3 详细调试信息
            logLevel: 2,

            onProgressChange(pars) {
                log(`【进度变化】${pars.label}`);
            },
        });

        // 加入发送消息到历史记录
        ctx.csMiniAgentService.chatSessionHistoryService
            .pushChatHistoryItem(loginUser.sac, senderId, sessionId, {
                id: messageId,
                content: inputText,
                isMe: true,
                role: 'user',
                time: Date.now(),
            });
        // 加入回复消息到历史记录
        ctx.csMiniAgentService.chatSessionHistoryService
            .pushChatHistoryItem(loginUser.sac, senderId, sessionId, {
                id: Date.now(),
                content: r.reply,
                isMe: false,
                role: 'agent',
                time: Date.now(),
            });

        process.stdout.write(`智能体：${r.reply}\n`)
    }
}

// 启动程序
main();

// 监听程序退出
process.on('SIGINT', () => {
    console.log('\n程序退出');
    rl.close();
    process.exit();
});