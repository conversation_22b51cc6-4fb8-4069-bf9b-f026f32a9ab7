function reqPostJson(url, pars, callback, errorCallback, opts = {}) {
    if (pars == null) pars = {};
    // if (opts.withCredentials == null) opts.withCredentials = true;
    if (opts.headers == null) opts.headers = {};
    if (opts.headers['Content-Type'] == null) opts.headers['Content-Type'] = 'application/json;charset=utf-8';

    axios({
        url: url,
        method: 'post',
        headers: opts.headers,
        data: pars,
        withCredentials: opts.withCredentials,
        responseType: opts.responseType,
    }).then(function (response) {
        const r = response.data;
        if (callback != null) {
            callback(r);
        }
    }).catch(function (error) {
        if (errorCallback != null) {
            errorCallback(error);
        }
    });
}