<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <script src="../../lib/vue/vue2.min.js"></script>
</head>
<body>
<div id="app">
    <div>客户端类型：{{clientType}}</div>
    <div>是否在客户端：{{isInClient}}</div>
    <input type="button" value="打开网页" @click="handleOpenSite" />
</div>
<script>
    new Vue({
        el: '#app',
        data: function() {
            return {
                clientType: '',
                isInClient: false,
            };
        },
        methods: {
            handleOpenSite() {
                parent.shellMapper.openExternalUrl('http://www.baidu.com')
            }
        },
        created: function () {
        },
        mounted: function () {
            var self = this;
            this.$nextTick(function () {
                if (parent.shellMapper) {
                    self.clientType = parent.shellMapper.getType()
                    self.isInClient = parent.shellMapper.isInShell()
                }
            });
        }
    });
</script>
</body>
</html>