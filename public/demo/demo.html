<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <script src="../lib/vue/vue2.min.js"></script>
    <script src="../lib/axios/axios.min.js"></script>
    <script src="../util/http-util.js"></script>
</head>
<body>
<div id="app">
    <fieldset>
        <legend>聊天</legend>
        <p>
            <button @click="test_chat()">测试</button>
        </p>
    </fieldset>
    <fieldset>
        <legend>更新RawText到工作空间</legend>
        <p>
            <button @click="test_updateRawText()">测试</button>
            <textarea v-model="rawText" style="width: 200px;height: 100px;"></textarea>
        </p>
    </fieldset>
    <fieldset>
        <legend>从工作空间删除RawText</legend>
        <p>
            <button @click="test_deleteRawText()">测试</button>
        </p>
    </fieldset>
    <fieldset>
        <legend>与百度千帆AppBuilder的Agent对话（流式）</legend>
        <p>
        <div>
        <pre>{{chatBQFABAgentReply}}</pre>
        <div>
            <div v-for="item in chatBQFABAgentSources">
                <a href="javascript:;" @click="alert(item.content);">{{item.title}}</a>
            </div>
        </div>
        <div v-if="chatBQFABAgentState === 1">Loading...</div>
        </div>
        <div>
            <input type="text" v-model="chatBQFABAgentInput" />
            <button @click="test_chatBQFABAgent_stream()">测试</button>
        </div>
        </p>
    </fieldset>
</div>
<script>
    function fetchStream(url, method = 'GET', pars, cb, doneCb) {
        // 创建流式响应
        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json', // 指定事件流格式
                // 'Content-Type': 'text/event-stream', // 指定事件流格式
                // 其他需要的HTTP头部
            },
            body: JSON.stringify(pars),
            // 设置为stream以获取流式响应
            // mode: 'cors',
        }).then((response) => {

            // 处理流式响应
            const reader = response.body.getReader();
            reader.read().then(function processStream({done, value}) {
                if (done) {
                    // 流结束
                    if (doneCb) doneCb();
                } else {
                    // 处理收到的数据
                    const utf8Decoder = new TextDecoder("utf-8");
                    let raw = value ? utf8Decoder.decode(value, {stream: true}) : '';
                    if (cb) cb(raw);
                    // console.log(`Received data: ${String.fromCharCode.apply(null, new Uint8Array(value))}`);
                    // if (cb) cb(String.fromCharCode.apply(null, new Uint8Array(value)));

                    // 递归读取下一个chunk
                    reader.read().then(processStream);
                }
            });
        }).catch((err) => {

        });

    }
</script>
<script>
    new Vue({
        el: '#app',
        data: function () {
            return {
                rawText: '我叫小明\n我出生在上海',
                chatBQFABAgentState: 0,
                chatBQFABAgentInput: '几点开门？',
                chatBQFABAgentReply: '',
                chatBQFABAgentSources: [],
            };
        },
        methods: {
            test_chat() {
                console.log('请求...')
                reqPostJson('/api/kb1/chat', {
                    kbType: 'allm',
                    kbId: '111',
                    slug: '6d57420c-5576-4307-85cc-c6cb08b289e9',
                    content: '几点开门？'
                }, function (r) {
                    console.log(r);
                }, function (err) {
                    console.error(err);
                });
            },
            test_updateRawText() {
                const self = this;
                console.log('请求...')
                reqPostJson('/api/kb1/setKfRawText', {
                    kbType: 'allm',
                    kbId: '111',
                    kfId: '1001',
                    slug: '6d57420c-5576-4307-85cc-c6cb08b289e9',
                    content: self.rawText
                }, function (r) {
                    console.log(r);
                }, function (err) {
                    console.error(err);
                });
            },
            test_deleteRawText() {
                const self = this;
                console.log('请求...')
                reqPostJson('/api/kb1/delKf', {
                    kbType: 'allm',
                    kbId: '6d57420c-5576-4307-85cc-c6cb08b289e9',
                    kfId: '1001',
                    slug: '6d57420c-5576-4307-85cc-c6cb08b289e9',
                }, function (r) {
                    console.log(r);
                }, function (err) {
                    console.error(err);
                });
            },
            test_chatBQFABAgent_stream() {
                const self = this;

                self.chatBQFABAgentState = 1;
                self.chatBQFABAgentReply = '';
                self.chatBQFABAgentSources = [];

                fetchStream('/api/kb-bqfab/chat-agent-stream', 'POST', {
                    appId: 'be6d91bc-4902-4183-b308-5b48bcc690a4',
                    content: self.chatBQFABAgentInput,
                }, function (r) {
                    var raw = r.trim();

                    raw.split('data: ').forEach((n) => {
                        if (n != null && n.trim().length > 0) {
                            console.log(n);
                            // { answer, content: [{ outputs: { references: [{ content, title }] } }] }
                            var obj = JSON.parse(n);

                            if (obj.content.length > 0) {
                                if (obj.content[0].outputs.references != null && obj.content[0].outputs.references.length > 0) {
                                    obj.content[0].outputs.references.forEach((item) => {
                                        self.chatBQFABAgentSources.push(item);
                                    });
                                }
                            }

                            if (obj.answer != null && obj.answer.length > 0) {
                                // console.log(obj.answer)
                                self.chatBQFABAgentReply += obj.answer || '';
                            }
                        }
                    });
                }, function () {
                    self.chatBQFABAgentState = 0;
                    console.log('done');
                });
            }
        },
        created: function () {
        },
        mounted: function () {
            var self = this;
            this.$nextTick(function () {
            });
        }
    });
</script>
</body>
</html>