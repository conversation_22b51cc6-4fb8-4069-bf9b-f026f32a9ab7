<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>AI使用工具样例</title>
    <script src="../lib/vue/vue2.min.js"></script>
    <script src="../lib/axios/axios.min.js"></script>
    <script src="../util/http-util.js"></script>
</head>
<body>
<div id="app">
    <fieldset>
        <legend>对话（全量）</legend>
        <p>
        <div>
        <pre>{{chatReply}}</pre>
        <div>
            <div v-for="item in chatSources">
                <a href="javascript:;" @click="alert(item.content);">{{item.title}}</a>
            </div>
        </div>
        <div v-if="chatState === 1">Loading...</div>
        </div>
        <div>
            <input type="text" v-model="chatInput" />
            <button @click="test_chatCMK()">测试（CS迷你知识库）</button>
        </div>
        </p>
    </fieldset>
</div>
<script>
    new Vue({
        el: '#app',
        data: function () {
            return {
                rawText: '我叫小明\n我出生在上海',
                chatState: 0,
                chatInput: '几点上班？',
                chatReply: '',
                chatSources: [],
            };
        },
        methods: {
            // OpenAI
            async test_chatCMK() {
                const self = this;
                const debug = true;

                const response = await axios.post(`/api/kb0/chat`, {
                    kbType: 'cmk',
                    kbId: '20240622125505178-f7ebc1',
                    content: self.chatInput,
                    chatModelPlatform: 'openai',
                    chatModel: 'gpt-4o-mini',
                }, {
                });

                self.chatReply = response.data.data.output_text;
                console.log(response);
            },
        },
        created: function () {
        },
        mounted: function () {
            var self = this;
            this.$nextTick(function () {
            });
        }
    });
</script>
</body>
</html>