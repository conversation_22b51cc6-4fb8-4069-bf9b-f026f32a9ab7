<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <script src="../lib/vue/vue2.min.js"></script>
    <script src="../lib/axios/axios.min.js"></script>
    <script src="../util/http-util.js"></script>
</head>
<body>
<div id="app">
    <fieldset>
        <legend>对话（流式）</legend>
        <p>
        <div>
        <pre>{{chatReply}}</pre>
        <div>
            <div v-for="item in chatSources">
                <a href="javascript:;" @click="alert(item.content);">{{item.title}}</a>
            </div>
        </div>
        <div v-if="chatState === 1">Loading...</div>
        </div>
        <div>
            <input type="text" v-model="chatInput" />
            <button @click="test_chat_stream()">测试（百度千帆）</button>
            <button @click="test_chatCMK_stream()">测试（CS迷你知识库-openai）</button>
            <button @click="test_chatCMK_ollama_stream()">测试（CS迷你知识库-ollama）</button>
            <button @click="test_chat_kimi_stream()">测试（kimi）</button>
        </div>
        </p>
    </fieldset>
</div>
<script>
    function fetchStream(url, method = 'GET', pars, cb, doneCb) {
        // 创建流式响应
        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json', // 指定事件流格式
                // 'Content-Type': 'text/event-stream', // 指定事件流格式
                // 其他需要的HTTP头部
            },
            body: JSON.stringify(pars),
            // 设置为stream以获取流式响应
            // mode: 'cors',
        }).then((response) => {

            // 处理流式响应
            const reader = response.body.getReader();
            reader.read().then(function processStream({done, value}) {
                if (done) {
                    // 流结束
                    if (doneCb) doneCb();
                } else {
                    // 处理收到的数据
                    const utf8Decoder = new TextDecoder("utf-8");
                    let raw = value ? utf8Decoder.decode(value, {stream: true}) : '';
                    if (cb) cb(raw);
                    // console.log(`Received data: ${String.fromCharCode.apply(null, new Uint8Array(value))}`);
                    // if (cb) cb(String.fromCharCode.apply(null, new Uint8Array(value)));

                    // 递归读取下一个chunk
                    reader.read().then(processStream);
                }
            });
        }).catch((err) => {

        });

    }
</script>
<script>
    new Vue({
        el: '#app',
        data: function () {
            return {
                rawText: '我叫小明\n我出生在上海',
                chatState: 0,
                chatInput: '几点上班？',
                chatReply: '',
                chatSources: [],
            };
        },
        methods: {
            // 百度千帆
            test_chat_stream() {
                const self = this;

                self.chatState = 1;
                self.chatReply = '';
                self.chatSources = [];

                fetchStream('/api/kb-bqfab/chat-agent-stream', 'POST', {
                    appId: 'be6d91bc-4902-4183-b308-5b48bcc690a4',
                    content: self.chatInput,
                }, function (r) {
                    var raw = r.trim();

                    raw.split('\n').forEach((n0) => {
                        // 去除“data: ”前缀
                        const n = n0.substring(6);
                        if (n != null && n.trim().length > 0) {
                            // console.log(n);
                            // { answer, content: [{ outputs: { references: [{ content, title }] } }] }
                            var obj = JSON.parse(n);

                            if (obj.content.length > 0) {
                                if (obj.content[0].outputs.references != null && obj.content[0].outputs.references.length > 0) {
                                    obj.content[0].outputs.references.forEach((item) => {
                                        self.chatSources.push(item);
                                    });
                                }
                            }

                            if (obj.answer != null && obj.answer.length > 0) {
                                // console.log(obj.answer)
                                self.chatReply += obj.answer || '';
                            }
                        }
                    });
                }, function () {
                    self.chatState = 0;
                    console.log('done');
                });
            },
            // OpenAI
            test_chatCMK_stream() {
                const self = this;
                const debug = true;

                self.chatState = 1;
                self.chatReply = '';
                self.chatSources = [];

                let bwzzc = '';
                fetchStream('/api/kb0/chat-stream', 'POST', {
                    kbType: 'cmk',
                    kbId: '20241023172616342-6e7218', // 绿化工程
                    // kbId: '20240622125505178-f7ebc1', // 创正员工培训（CS-MK）
                    content: self.chatInput,
                    chatModelPlatform: 'openai',
                    chatModel: 'gpt-4o-mini',
                }, function (r) {
                    const chatSources = [];
                    let chatReply = '';
                    let canBuildReply = true;
                    let raw = r.trim();


                    if (debug) console.log('------------------------------------------------------------------')
                    if (debug) console.log(raw);

                    // 这是最后一个不完整chunk
                    if (bwzzc.length > 0 && (raw.endsWith('data: [DONE]') || raw.endsWith('"usage":null}') || raw.endsWith('{"reasoning_tokens":0}}}'))) {
                        if (debug) console.log('********************************************************************')
                        if (debug) console.log(`处理不完整暂存：${raw}`);
                        raw = bwzzc + raw;
                        bwzzc = '';
                    }
                    // 这是一个不完整chunk
                    else if (!raw.endsWith('"usage":null}')
                        && !raw.endsWith('{"reasoning_tokens":0}}}') && !raw.endsWith(('data: [DONE]'))) {
                        if (debug) console.log('********************************************************************')
                        if (debug) console.log(`发现一个不完整chunk：${raw}`);
                        bwzzc += raw;
                        canBuildReply = false;
                    }
                    // 这是一个不完整chunk
                    else if (!raw.startsWith('data: ')) {
                        if (debug) console.log('********************************************************************')
                        if (debug) console.log(`发现一个不完整chunk：${raw}`);
                        bwzzc += raw;
                        canBuildReply = false;
                    }

                    if (canBuildReply) {
                        const splitList = raw.split('\n');
                        for (const i in splitList) {
                            const n0 = splitList[i];

                            // 去除“data: ”前缀
                            const n = n0.substring(6);
                            if (n != null && n.trim().length > 0) {
                                // if (debug) console.log('------------------------------------------------------------------')
                                // if (debug) console.log(n);

                                if (n === '[DONE]') {
                                }
                                else {
                                    try {
                                        const obj = JSON.parse(n);

                                        if (obj.choices.length > 0) {
                                            if (obj.choices[0].delta.content != null && obj.choices[0].delta.content.length > 0) {
                                                chatReply += obj.choices[0].delta.content || '';
                                            }
                                        }
                                    } catch (exc) {
                                        if (debug) console.error(`解析异常 -> ${exc.message}`);
                                        if (debug) console.log(`异常chunk：${raw}`);
                                        return;
                                    }
                                }
                            }
                        }
                    }

                    self.chatReply += chatReply;
                }, function () {
                    self.chatState = 0;
                    console.log('done');
                });
            },
            // Ollama
            test_chatCMK_ollama_stream() {
                const self = this;

                self.chatState = 1;
                self.chatReply = '';
                self.chatSources = [];

                fetchStream('/api/kb0/chat-stream', 'POST', {
                    kbType: 'cmk',
                    kbId: '20240622125505178-f7ebc1',
                    content: self.chatInput,
                    chatModelPlatform: 'ollama',
                    chatModel: 'qwen2.5:7b',
                }, function (r) {
                    var raw = r.trim();

                    raw.split('\n').forEach((n0) => {
                        // 去除“data: ”前缀
                        const n = n0.substring(6);
                        if (n != null && n.trim().length > 0) {
                            console.log(n);

                            var obj = JSON.parse(n);
                            if (obj.message) {
                                self.chatReply += obj.message.content || '';
                            }
                        }
                    });
                }, function () {
                    self.chatState = 0;
                    console.log('done');
                });
            },
            // MoonshotAI kimi
            test_chat_kimi_stream() {
                const self = this;

                self.chatState = 1;
                self.chatReply = '';
                self.chatSources = [];

                fetchStream('/api/chat-stream', 'POST', {
                    model: 'moonshot-v1-8k',
                    messages: [
                        { role: 'user', content: self.chatInput }
                    ],
                }, function (r) {
                    var raw = r.trim();
                    console.log(raw);

                    raw = raw.replaceAll('}{', '},{');
                    const arr = JSON.parse(`[${raw}]`);
                    arr.forEach((n) => {
                        self.chatReply += n.content || '';
                    });
                }, function () {
                    self.chatState = 0;
                    console.log('done');
                });
            },
        },
        created: function () {
        },
        mounted: function () {
            var self = this;
            this.$nextTick(function () {
            });
        }
    });
</script>
</body>
</html>