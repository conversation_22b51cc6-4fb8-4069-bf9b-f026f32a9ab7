import path from "path";


import BaiduQianFanAppBuilderMapper from "./src/mapper/BaiduQianFanAppBuilderMapper";

const bqfabMapper = new BaiduQianFanAppBuilderMapper();

// // 获取文档列表
// bqfabMapper.getDocumentList('0029d067-95ec-4250-95a7-15a95ec8f970', undefined, 100,
// (resp) => {
//     console.log(resp);
// }, (err) => {
//     console.error('请求异常');
//     if (err.data != null) {
//         console.error(err.data.message);
//     }
//     else {
//         console.error(err.message);
//     }
// });
// 上传文档
bqfabMapper.uploadDocumentByPath('0029d067-95ec-4250-95a7-15a95ec8f970',
    `${path.resolve()}\\data\\test0.txt`, (resp) => {
    console.log(resp);
}, (err) => {
    console.error('请求异常');
    console.error(err.data.message);
})
// // 与Agent应用对话(流式)
// bqfabMapper.chatAgentStream('be6d91bc-4902-4183-b308-5b48bcc690a4', '几点开门？', (resp) => {
//     const resStream = resp;
//     resStream.on('data', (chunk) => {
//         console.log('获取块：')
//         console.log(chunk.toString());
//     });
//     resStream.on('end', () => {
//         console.log('end');
//     });
//
// }, (err) => {
//     console.error('请求异常');
//     console.error(err);
// }, {
//     stream: true
// })



// import AnythingLLMMapper from "./src/mapper/AnythingLLMMapper";
//
// const anythingLLMMapper =
//     new AnythingLLMMapper({}, 'http://test.ovinfo.com:2235', 'FFSFDFX-8GKMSV3-HWGDW3H-Y5Y3DHW');
//
// anythingLLMMapper.updateEmbeddings('6d57420c-5576-4307-85cc-c6cb08b289e9', ['custom-documents/test0.txt-313c14dc-2948-456a-83c5-710707cfb1a5.json'], [], (resp) => {
//     console.log(resp);
// }, (err) => {
//     console.error(err.response.status);
//     console.error(err.response.data.error);
//     // console.error(err.response);
// });