接口文档：http://test.ovinfo.com:3021/renren-admin/doc.html#/RenRen/AI%E6%99%BA%E8%83%BD%E9%97%AE%E7%AD%94%E8%B0%83%E7%94%A8%E7%9A%84%E6%8E%A5%E5%8F%A3/isProjectHasBid
右上角搜索：AI智能问答调用的接口


lhgc_token: f38326098a99abcba432968e01ed4f58

用机构代码来区分用户
lhgc_uid: 91310000132210069Q


# 测试样例
```markdown
1、我的XXX项目现在进行到了哪一步，下一步需要做什么
num：2202PT0162     nameKw：
普陀区桃浦科技智慧城（W06-1401单元）027-01，039-01地块项目园林景观工程
2、我的某某项目整改内容是什么？
num：1801CM0068   nameKw：
崇明生态岛环岛防汛提标及景观道一期工程（分水岭路-西门路西侧）绿化工程
3、某某项目是否中标？
num：1905PD0049   nameKw：  黄浦江沿岸E10单元E08-5地块公共绿地项目
4、某某项目验收材料还差哪些？
num：2205MH0051  nameKw：环城生态公园带“环上”功能提升-春申公园（跨河桥梁）
5、今年我们单位申报项目总额是多少？
num：2405PD0109   nameKw：上钢新村街道“精品城区”建设专项包（2024年第一批）
```


# 详细分析
```markdown
// 项目编号位数是固定的共10位+3位
// 对接方式，我这里开发对话页，然后嵌入到绿化工程的系统中使用，打开嵌入时会传入token给我

// 接口返回结果标准
{
success: true,
msg: null, // 错误信息
code: 0, // 值位多少代表token失效
data: null, // 具体的数据
}

1、我的XXX项目现在进行到了哪一步，下一步需要做什么
// token 令牌，num 项目编号，nameKw 项目名称关键词
// 返回结果：{ curState: '', nextState: '' }
// nextState为空有两种情况，项目已结束或最后一步
getProjectStateAndNext(token, num, nameKw)

2、我的某某项目整改内容是什么？
// token 令牌，num 项目编号，nameKw 项目名称关键词
// 返回结果：{ content: '' }
getProjectZGNR(token, num, nameKw)

3、某某项目是否中标？
// token 令牌，num 项目编号，nameKw 项目名称关键词
// 返回结果：{ result: false }
isProjectHasBid(token, num, nameKw)

4、某某项目验收材料还差哪些？
// token 令牌，num 项目编号，nameKw 项目名称关键词
// 返回结果：{ content: '' }
getProjectMissingMaterials(token, num, nameKw)

5、今年我们单位申报项目总额是多少？
// token 令牌，year 年份（可以不传，不传就是当前的）
// 返回结果：{ content: '' } // 总金额
getProjectTotalAmount(token, year)
```
