const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs');

// const lancedb = require("@lancedb/lancedb");
// const apacheArrow = require("apache-arrow");

// 用于主项目执行
const {start} = require('./src/main-start');
// 用于派生项目执行
// const {start} = require('./lib/main-start');

var TurndownService = require('turndown')
const {ImportDataToKBUtil} = require("./src/module/cs-mini-kb/util/ImportDataToKBUtil");

const imports = {
    express, bodyParser,
    path, fs,
    // lancedb, apacheArrow,
};
const ctx = start(imports, {
    startServer: false,
});


/**
 * 加载指定目录下的所有文件并解析json内容作为数组项目
 * @param {string} dirPath 目录路径
 * @returns {Array} 返回包含所有文件JSON内容的数组
 */
async function loadFiles(dirPath) {
    const { fs } = ctx.imports;
    
    // 检查目录是否存在
    if (!fs.existsSync(dirPath)) {
        console.log(`目录不存在: ${dirPath}`);
        return [];
    }
    
    try {
        // 读取目录中的所有文件
        const files = fs.readdirSync(dirPath);
        const result = [];
        
        // 遍历文件列表
        for (const file of files) {
            const filePath = `${dirPath}/${file}`;
            
            // 检查是否为文件（忽略子目录）
            const stats = fs.statSync(filePath);
            if (!stats.isFile()) {
                continue;
            }
            
            try {
                // 读取文件内容
                const fileContent = fs.readFileSync(filePath, 'utf8');
                
                // 检查内容是否为空
                if (!fileContent || fileContent.trim().length === 0) {
                    console.log(`文件内容为空，跳过: ${filePath}`);
                    continue;
                }
                
                // 解析JSON内容
                const jsonData = JSON.parse(fileContent);
                jsonData.id = file
                
                // 如果解析的是数组，则合并到结果中；如果是对象，则作为单个项目添加
                if (Array.isArray(jsonData)) {
                    result.push(...jsonData);
                } else {
                    result.push(jsonData);
                }
                
                console.log(`成功加载文件: ${filePath} (${Array.isArray(jsonData) ? jsonData.length + '个项目' : '1个项目'})`);
                
            } catch (parseError) {
                console.error(`解析JSON文件失败: ${filePath} - ${parseError.message}`);
                // 继续处理其他文件，不中断整个流程
            }
        }
        
        console.log(`总共加载了 ${result.length} 个项目，来自目录: ${dirPath}`);
        return result;
        
    } catch (error) {
        console.error(`读取目录失败: ${dirPath} - ${error.message}`);
        return [];
    }
}


async function main() {

    // 从文件导入知识碎片
    if (0) {
        const idtkbu = new ImportDataToKBUtil(ctx)
        var turndownService = new TurndownService()
        const list = await loadFiles('C:\\Users\\<USER>\\AppData\\Roaming\\cs-ai-client\\data\\sui_dao_zixun');

        for (const item of list) {
            item.content = turndownService.turndown(item.content)

            await idtkbu.importKf('http://cs.juzhengdevelop.com:8072/open-api/extappbackplugin/epto1', 'bot.test', {
                kbId: '20250902170331804-3b9e4f',
                title: item.title,
                content: item.content,
            })
        }
    }

    // 根据内容查询文件
    if (0) {
        let kw = '甬舟铁路复线金塘海底隧道开工';
        const list = await loadFiles('C:\\Users\\<USER>\\AppData\\Roaming\\cs-ai-client\\data\\sui_dao_zixun');
        const findList = [];
        for (const item of list) {
            if (item.title.indexOf(kw) != -1) {
                findList.push(item);
            }
            else if (item.content.indexOf(kw) != -1) {
                findList.push(item);
            }
        }


        log(`共找到 ${findList.length} 条：`)
        for (const item of findList) {
            log(`${item.id} ${item.title}`)
        }
    }
}

main();