const express = require('express');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');

const lancedb = require("@lancedb/lancedb");
const apacheArrow = require("apache-arrow");
// const ffmpeg = require('fluent-ffmpeg');
// const temp = require('temp');

// 用于主项目执行
// const {start} = require('./src/main-start');
// 用于派生项目执行
const {start} = require('./lib/main-start');

const imports = {
    express, bodyParser, fs, path,
    lancedb, apacheArrow,
    // ffmpeg, temp,
};
const ctx = start(imports, {
    startServer: false,
    startCSMiniAgentServer: false,
    startWeiXinServer: false,
});

// 下载企业微信语音素材
const armBuffer = await ctx.shortTimeCacheService.getQywxService()
.downloadMediaToBuffer('wx2403e0282a67d98d', 'kr8a2K-VG8gismENnoS3CK1UUJ1w8Ax3mOrTb8vL-_c', 
    '1s-LFEW-wK0kW_rcx3H0zzENz6nH85BMurrsD8QxEdzStiCQKBwFQaFZRj0gsTX9H');

console.log(armBuffer);

// 保存到文件中
fs.writeFileSync(ctx.rootPath + '/test.amr', armBuffer);

// // 直接使用AMR格式进行语音识别
// const axios = require('axios');
// const FormData = require('form-data');

// async function recognizeAMRAudio(audioBuffer) {
//     try {
//         // 创建FormData对象
//         const formData = new FormData();
        
//         // 添加AMR音频数据，无需转换格式
//         formData.append('audio', Buffer.from(audioBuffer), {
//             filename: 'audio.amr',
//             contentType: 'audio/amr'
//         });
//         formData.append('format', 'amr');
        
//         // 设置API地址和访问令牌
//         const baseUrl = 'http://test.ovinfo.com:2243';
//         const accessToken = '838bc71ca3a0';
        
//         // 发送请求
//         const response = await axios.post(
//             `${baseUrl}/recognize?token=${accessToken}`,
//             formData,
//             {
//                 headers: {
//                     ...formData.getHeaders(),
//                 }
//             }
//         );
        
//         // 处理响应
//         if (response.status !== 200) {
//             throw new Error(`HTTP error! status: ${response.status}`);
//         }
        
//         return response.data;
//     } catch (error) {
//         console.error('语音识别失败：', `${error.code} -> ${error.message} -> ${JSON.stringify(error.response.data)}`);
//         // throw error;
//     }
// }

// // 执行语音识别
// try {
//     const recognitionResult = await recognizeAMRAudio(armBuffer);
//     console.log('语音识别结果:', recognitionResult);
// } catch (error) {
//     console.error('处理过程中出错:', error);
// }