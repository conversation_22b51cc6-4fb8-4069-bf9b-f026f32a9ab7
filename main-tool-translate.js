const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs');
const xml2js = require('xml2js')

// const lancedb = require("@lancedb/lancedb");
// const apacheArrow = require("apache-arrow");

// 用于主项目执行
const {start} = require('./src/main-start');
// 用于派生项目执行
// const {start} = require('./lib/main-start');

var TurndownService = require('turndown')
const {ImportDataToKBUtil} = require("./src/module/cs-mini-kb/util/ImportDataToKBUtil");

const imports = {
    express, bodyParser,
    path, fs,
    // lancedb, apacheArrow,
};
const ctx = start(imports, {
    startServer: false,
});


/**
 *
 * @param {string} filePath 文件路径
 * @returns {string} xml格式的内容
 */
async function loadAndParseContent(filePath) {
    const xml = fs.readFileSync(filePath, 'utf8');
    const result = await xml2js.parseStringPromise(xml)
    return result;
}



async function main() {
    const filePath = 'C:\\data\\game\\Steam\\steamapps\\common\\StarDrive\\Content\\Localization\\English\\GameText_EN.xml'
    const r = await loadAndParseContent(filePath)
    // console.log(JSON.stringify(r.LocalizationFile.TokenList).substring(0, 300))
    // console.log(r.LocalizationFile.TokenList[0].Token.length)
    for (const i in r.LocalizationFile.TokenList[0].Token) {
        const token = r.LocalizationFile.TokenList[0].Token[i];
        console.log(`原始文本：${token.Text[0]}`)

        const messages = [
            {
                role: 'system', content: `
                - 你现在是一个翻译助手，把输入的内容翻译为中文
                - 回复不要有任何解释，就返回结果
                `
            },
            {
                role: 'user', content: token.Text[0]
            }
        ]
        const chatResult = await ctx.shortTimeCacheService.getChatModel().chat('ollama_gpt-oss:20b', messages)
        console.log(`翻译文本：${chatResult.reply}`)
        token.Text[0] = chatResult.reply
        console.log(`------------`)

        // if (Number(i) > 3) {
        //     break;
        // }
    }

    const builder = new xml2js.Builder();
    const newXml = builder.buildObject(r);
    console.log(newXml.substring(0, 300))

    fs.writeFileSync(filePath, newXml);
    console.log(`翻译完毕！`)
}

main();