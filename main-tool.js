const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs');

// const lancedb = require("@lancedb/lancedb");
// const apacheArrow = require("apache-arrow");

// 用于主项目执行
const {start} = require('./src/main-start');
// 用于派生项目执行
// const {start} = require('./lib/main-start');

const imports = {
    express, bodyParser,
    path, fs,
    // lancedb, apacheArrow,
};
const ctx = start(imports, {
    startServer: false,
});

/*** 生成登录令牌（token_29e65a8f） ***/
if (!1) {
    const token = ctx.shortTimeCacheService.getToken_29e65a8f_Service().buildToken('lhgc.cn', 'root', '2a07a796', {
        time: 0
    })
    console.log(`登录令牌：${token}`);
    const token_b64 = encodeURIComponent(btoa(unescape(encodeURIComponent(token))));
    console.log(`登录令牌b64：${token_b64}`);
    // const obj = ctx.shortTimeCacheService.getToken_29e65a8f_Service().parseToken(token)
    // console.log(JSON.stringify(obj))
}

/*** 解析登录令牌（token_29e65a8f） ***/
if (!1) {
    const token_b64 = 'E5408149124FE0BDE58D53717CBFCCBAA261895FE03173FDDCCB8853704801A56EC372B11508DE2E6FC4C818D543CC27630A9843778BF39A2DA4A5FEFC84F74FD999AD48C8BB2135630A9843778BF39A06C157963BBC5C39A7D54C15196BBC36630A9843778BF39A630A9843778BF39A44F6C661E69A667F0114C944EED8CCDB2379CBE78DA6D4A826F3A92435AC489D8E0E3372F7C202B7AE63422D25C219E6B0844050BA7567D8AA52332EDEA8A4B3FA0CF74C8B5A8DE2';
    const token = decodeURIComponent(escape(atob(decodeURIComponent(token_b64))));
    const obj = ctx.shortTimeCacheService.getToken_29e65a8f_Service().parseToken(token)
    console.log(JSON.stringify(obj))
}

/*** 生成登录令牌 ***/
if (!1) {
    const token = ctx.shortTimeCacheService.getLoginService().buildToken('lhgc.cn', { un: 'root' });
    console.log(`登录令牌：${token}`);
}

/*** 解析登录令牌 ***/
if (1) {
    const token = 'E5408149124FE0BDE58D53717CBFCCBAA261895FE03173FDDCCB8853704801A56EC372B11508DE2E6FC4C818D543CC27630A9843778BF39A2DA4A5FEFC84F74FD999AD48C8BB2135630A9843778BF39A06C157963BBC5C39A7D54C15196BBC36630A9843778BF39A630A9843778BF39A44F6C661E69A667F0114C944EED8CCDB2379CBE78DA6D4A826F3A92435AC489D8E0E3372F7C202B7AE63422D25C219E6B0844050BA7567D8AA52332EDEA8A4B3FA0CF74C8B5A8DE2';
    const obj = ctx.shortTimeCacheService.getLoginService().parseToken(token)
    console.log(JSON.stringify(obj))
}