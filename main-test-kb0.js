const express = require('express');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');

import {Buffer} from "buffer";
const crypto = require('crypto');
import OpenAI from "openai";

const lancedb = require("@lancedb/lancedb");
const apacheArrow = require("apache-arrow");

// 用于主项目执行
const {start} = require('./src/main-start');
// 用于派生项目执行
// const {start} = require('./lib/main-start');

const imports = {
    express, bodyParser,
    fs, path,
    Buffer, crypto, OpenAI,
    lancedb, apacheArrow,
};
const ctx = start(imports, {
    startServer: false,
});

let kbId = '20241023172616342-6e7218';

async function main() {
    const csMiniKBService = ctx.shortTimeCacheService.getCSMiniKbService();
    const lanceDBMapper = csMiniKBService.lanceDBMapper;
    const conn = await csMiniKBService._getConn(kbId);

    // 查询预设回答库
    if (0) {
        const rows = await csMiniKBService.presetAnswerService.search(kbId,
            '园林绿化工程总承包招标中，项目包含建筑或市政等内容的，对施工承包单位资格如何确定', 5, {
            minScore: 0.85,
        });
        console.log(`查到行数：${rows.length}`);
        let i = 0;
        for (const row of rows) {
            console.log(`(${++i})【${row.score}】${row.title}`);
        }
    }

    // 查询预设回答库
    if (0) {
        const rows = await lanceDBMapper.getTableRows(conn, 'table_yshd', `1 = 1`);
        console.log(`查到行数：${rows.length}`);
        let i = 0;
        for (const row of rows) {
            console.log(`(${++i}) ${row.title}`);
        }
    }

    // 给表添加字段
    if (0) {
        await lanceDBMapper.addFieldToTable(conn, 'table_0', 'is_yshd', 'string');
        console.log(`添加字段完毕`)
    }

    // 创建预设回答表
    if (0) {
        let vectorListSize = 1536;
        await lanceDBMapper.createEmptyTableSync(conn, 'table_yshd', [
            // 唯一ID
            {name: "id", type: "string"},
            // 标题
            {name: "title", type: "string"},
            // 内容
            {name: "content", type: "string"},
            // 标题向量
            {name: "title_vector", type: "float32-array", listSize: vectorListSize},
            // 内容向量
            {name: "content_vector", type: "float32-array", listSize: vectorListSize},
        ]);
        console.log(`创建预设回答表完毕`)
    }

    console.log('执行完毕')
}

main();