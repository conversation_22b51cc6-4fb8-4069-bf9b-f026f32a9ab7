{"name": "cbaio", "bin": {"aura": "./main-tui.js"}, "scripts": {"start_backend": "bun main.js", "run-es5": "npx babel-node main.js", "build-main-start-es5": "dev-build-main-start-es5.bat", "build-main-start-es5_mac": "./dev-build-main-start-es5.sh", "start2": "npx babel-node main.js", "build": "dev-build.bat", "build-es5": "dev-build-es5.bat", "dist-start": "dev-dist-run.bat", "dev-tui": "bun main-tui.js", "exec-tool-a": "bun main-tool.js"}, "dependencies": {"@lancedb/lancedb": "^0.10.0", "apache-arrow": "^17.0.0", "axios": "^1.7.2", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "crypto": "^1.0.1", "express": "^4.19.2", "express-session": "^1.18.1", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.0", "mammoth": "^1.9.1", "marked": "^15.0.7", "multer": "^2.0.1", "ollama": "^0.5.12", "openai": "^4.68.4", "temp": "^0.9.4", "turndown": "^7.2.1", "ws": "^8.16.0", "xml2js": "^0.6.2", "yaml": "^2.5.0"}, "devDependencies": {"webpack": "^5.92.1", "webpack-cli": "^5.1.4", "@babel/cli": "^7.24.7", "@babel/core": "^7.24.7", "@babel/node": "^7.24.7", "@babel/preset-env": "^7.24.7"}}