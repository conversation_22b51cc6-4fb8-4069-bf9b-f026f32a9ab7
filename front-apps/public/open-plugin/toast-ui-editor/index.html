<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8"/>
    <title></title>
    <!--    <link rel="stylesheet" href="./css/tuidoc-example-style.css" />-->
    <!-- Editor -->
    <link rel="stylesheet" href="./toastui-editor.css"/>
    <script>var errorLogs = [];
    window.onerror = function (o, r, e, n) {
        errorLogs.push({message: o, source: r, lineno: e, colno: n})
    };</script>
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
        }
    </style>
</head>
<body>
<div class="code-html tui-doc-contents">
    <div id="editor"></div>
</div>
<!-- Added to check demo page in Internet Explorer -->
<script src="./babel.min.js"></script>
<!--<script src="./data/md-default.js"></script>-->
<!-- Editor -->
<script src="./toastui-editor-all.js"></script>
<script type="text/babel" class="code-js">
    let attachmentUrlReplacementPlugin;
    let processTableCellImages;

    // 添加辅助方法处理表格单元格中的图片
    processTableCellImages = function(cellNode) {
        const walker = cellNode.walker();
        let event;

        while ((event = walker.next())) {
            const node = event.node;
            if (event.entering) {
                if (node.literal && node.literal.indexOf('<img') !== -1) {
                    node.literal = attachmentUrlReplacementPlugin(node.literal);
                }
            }
        }
    }

    const editor = new toastui.Editor({
        el: document.querySelector('#editor'),
        previewStyle: 'vertical',
        // height: '500px',
        initialValue: '',
        plugins: [
            function () {
                return {
                    toHTMLRenderers: {
                        image(node) {
                            const { destination, altText } = node;
                            let url = destination;

                            if (attachmentUrlReplacementPlugin) {
                                url = attachmentUrlReplacementPlugin(destination);
                            }
                            console.log('原始：', destination)
                            // console.log('替换后：', destination.replace('/attachment-base-url/', 'http://localhost:3000/'))

                            return {
                                type: 'openTag',
                                tagName: 'img',
                                attributes: {
                                    src: url, //destination.replace('/attachment-base-url/', 'http://localhost:3000/'),
                                    alt: altText || '',
                                    // style: 'max-width: 100%; height: auto;'
                                }
                            };
                        },
                        link(node, { entering, origin }) {
                            const result = origin();
                            let url;

                            if (attachmentUrlReplacementPlugin) {
                                url = attachmentUrlReplacementPlugin(node.destination);
                                result.attributes.href = url;
                            }
                            else {
                                result.attributes.href = node.destination;
                            }

                            result.attributes.target = '_blank';
                            result.attributes.rel = 'noopener noreferrer';
                            result.classNames = ['custom-file-link'];

                            // if (entering && node.destination.includes('/attachment-base-url/')) {
                            //     result.attributes.href = node.destination.replace('/attachment-base-url/', baseUrl);
                            //     result.attributes.target = '_blank';
                            //     result.attributes.rel = 'noopener noreferrer';
                            //     result.classNames = ['custom-file-link'];
                            // }
                            return result;
                        },
                        // 添加表格单元格渲染器来处理表格中的图片
                        tableCell(node, { entering, origin }) {
                            const result = origin();

                            // 如果是进入节点且包含图片，处理其中的图片URL
                            if (entering && node.firstChild) {
                                processTableCellImages(node);
                            }

                            return result;
                        },

                    }
                }
            }
        ]
    });

    function setAttachmentUrlReplacementPlugin(plugin) {
        attachmentUrlReplacementPlugin = plugin;
    }

    function resize() {
        editor.setHeight(`${document.documentElement.offsetHeight - 3}px`);
    }

    function setHeight(val) {
        editor.setHeight(val);
    }

    function setMarkdown(val, cursorToEnd, opts = {}) {
        editor.setMarkdown(val, cursorToEnd);
        if (opts.scrollTop != null) {
            setTimeout(() => {
                editor.moveCursorToStart();
            }, 50);
        }
    }

    /**
     * 生成右上角悬浮按钮栏
     * @param btns {Array} 按钮集合，例：[{ label: '关闭', func: function() {} }]
     */
    function addTopRightButtons(btns) {
        // 移除已存在的按钮容器
        const existingContainer = document.querySelector('.top-right-buttons');
        if (existingContainer) {
            existingContainer.remove();
        }

        if (!btns || btns.length === 0) {
            return;
        }

        // 创建按钮容器
        const container = document.createElement('div');
        container.className = 'top-right-buttons';
        container.style.cssText = `
            position: fixed;
            top: 8px;
            right: 10px;
            z-index: 1000;
            display: flex;
            gap: 8px;
            flex-direction: column;
            align-items: flex-end;
        `;

        // 创建按钮
        btns.forEach((btn, index) => {
            const button = document.createElement('button');
            button.textContent = btn.label;
            button.style.cssText = `
                padding: 8px 12px;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                white-space: nowrap;
                transition: background-color 0.2s;
            `;

            button.addEventListener('mouseenter', () => {
                button.style.background = 'rgba(0, 0, 0, 0.9)';
            });

            button.addEventListener('mouseleave', () => {
                button.style.background = 'rgba(0, 0, 0, 0.7)';
            });

            if (btn.func && typeof btn.func === 'function') {
                button.addEventListener('click', btn.func);
            }

            container.appendChild(button);
        });

        document.body.appendChild(container);
    }

    function isReady() {
        return true;
    }

    window.addEventListener('resize', () => {
        resize();
    });

    resize();
</script>
</body>
</html>
