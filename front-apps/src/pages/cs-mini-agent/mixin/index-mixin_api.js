import {queryString} from "../../../code/util/web-util.js";

export default {
  data() {
    return {
      abortController: null,
      chatStreamStateId: null,
    }
  },
  methods: {
    // 滚动到底部
    scrollToBottom() {
      const chatHistoryArea = this.$refs.chatHistoryArea0;
      if (chatHistoryArea) {
        chatHistoryArea.scrollTop = chatHistoryArea.scrollHeight;
      }
    },
    // 切换右侧交互面板的显示状态
    toggleInteractionPanel() {
      if (this.interactionPanelSettings.visible) {
        this.closeInteractionPanel();
      } else {
        this.openInteractionPanel();
      }
    },
    // 打开互动面板
    openInteractionPanel(settings) {
      if (!this.interactionPanelSettings.visible) {
        if (settings) {
          settings.visible = true;
          this.$refs.interactionPanel0.setSettings(settings);
          this.onResize();
        }

        this.interactionPanelSettings.visible = true;
        // 触发面板打开事件，以便父组件可以监听
        this.$emit('on-interaction-panel-opened');
      }
    },
    // 关闭互动面板
    closeInteractionPanel() {
      if (this.interactionPanelSettings.visible) {
        this.interactionPanelSettings.visible = false;
        // 触发面板关闭事件，以便父组件可以监听
        this.$emit('on-interaction-panel-closed');
      }
    },
    // 更新对方最新历史消息
    updateOtherLatestHistoryMessage(message) {
      // 新增
      if (this.currentSession.chatHistory.length === 0 || this.currentSession.chatHistory[this.currentSession.chatHistory.length - 1].isMe === true) {
        // 判定是否要展开
        if (message.reasoningContent && message.reasoningContent.length > 0) {
          if (message.isReasoningExpanded == null) message.isReasoningExpanded = true;
        }
        this.currentSession.chatHistory.push(message);
      }
      // 修改
      else {
        const latestMessage = this.currentSession.chatHistory[this.currentSession.chatHistory.length - 1];
        for (const prop in message) {
          latestMessage[prop] = message[prop];
        }
        if (message.reasoningContent && message.reasoningContent.length > 0) {
          if (latestMessage.isReasoningExpanded == null) latestMessage.isReasoningExpanded = true;
        }
      }
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    // 清空输入框
    clearInputMessage() {
      this.inputMessage = '';
    },
    saveChatHistory() {
      if (this.currentSession) {
        this.currentSession.chatHistory = this.chatHistory;
      }
    },
    // 发送消息
    async sendMessage(pars = {}) {
      const self = this;
      let canAddSendMessageToHistory = true;
      let canAddReplyMessageToHistory = true;
      let sendMessageType;
      let newMessageId;
      let setConfigFields;

      // 开启发送状态
      this.sending = true;
      sendMessageType = pars.sendMessageType || this.agentFrontSettings.sendMessageType;
      if (this.debug) console.log(`send... (type: ${sendMessageType})`);

      // 构造历史消息
      newMessageId = Date.now().toString();

      // 清理输入的回车
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.inputMessage.endsWith('\n')) {
            const t = this.inputMessage.slice(0, -1);
            this.inputMessage = t;
          }
        }, 100);
      });

      // 生成消息内容
      let toSendMessageContent = pars.sendMessageContent || this.inputMessage;
      if (toSendMessageContent.endsWith('\n')) {
        toSendMessageContent = toSendMessageContent.slice(0, -1);
      }

      // 构造发送消息
      const sendMessage = {
        id: newMessageId,
        content: toSendMessageContent,
        sessionId: this.currentSession.id,
        chatHistorySessionId: this.getChatHistorySessionId(),
        senderId: (await this.getCtx().loginService.getUser()).username,
        receiverId: null,
        time: Date.now(),
        opts: {
          ...this.agentValues,
          attachments: this.$refs.chatInputFiles0.getFileList(),
        },
      };
      // console.log('【调试】发送消息：', sendMessage);

      if (this.currentSession.otherMembers.length === 1) {
        sendMessage.receiverId = this.currentSession.otherMembers[0].id;
      }
      else if (this.currentSession.otherMembers.length > 1) {
        this.sending = false;
        throw new Error('暂不支持群聊会话');
      }
      else {
        this.sending = false;
        throw new Error('会话成员为空');
      }

      if (this.agentFrontSettings) {
        if (this.agentFrontSettings.validateToSendMessage) {
          const r = this.agentFrontSettings.validateToSendMessage({
            action: pars.action,
            message: sendMessage,
          });
          if (r != null) {
            this.sending = false;
            throw new Error(r);
          }
        }

        if (this.agentFrontSettings.setQueryToConfig) {
          setConfigFields = {};
          for (const prop in this.agentFrontSettings.setQueryToConfig) {
            const value = this.agentFrontSettings.setQueryToConfig[prop];
            setConfigFields[value] = queryString(prop);
          }
        }
      }

      // console.log('agentFrontSettings:', this.agentFrontSettings)
      // console.log('setConfigFields:', setConfigFields)

      // 加入发送消息到历史记录
      let sendHistoryMessage;
      {
        const historyMessage = {
          id: newMessageId,
          content: sendMessage.content,
          isMe: true,
          role: 'user',
          time: Date.now(),
        };
        if (historyMessage.content == null || historyMessage.content.length == 0) {
          if (sendMessage.opts.attachments) {
            historyMessage.content = `（传入${sendMessage.opts.attachments.length}个附件）`;
          }
        }
        this.chatHistory.push(historyMessage);
        this.saveChatHistory();
        sendHistoryMessage = historyMessage;
      }

      this.updateOtherLatestHistoryMessage({
        isMe: false,
        role: 'agent',
        time: Date.now(),
        thinking: true,
      });

      this.$nextTick(() => {
        this.scrollToBottom();
      });

      let receiveMessage;
      // 发送消息并接收回复
      // 流式输出
      if (sendMessageType === 'stream') {
        receiveMessage = await this.getCtx().chatService.sendMessage(sendMessage, {
          debug: self.debug,
          stream: true,
          setConfigFields,
          initCb: (pars) => {
            self.abortController = pars.abortController;
            self.chatStreamStateId = pars.chatStreamStateId;
          },
          reasoningCb: (totalReasoning) => {
            self.updateOtherLatestHistoryMessage({
              reasoningContent: totalReasoning,
              isMe: false,
              role: 'agent',
              time: Date.now(),
              thinking: null,
              saying: true,
            });
          },
          changeCb: (totalReply) => {
            self.updateOtherLatestHistoryMessage({
              content: totalReply,
              isMe: false,
              role: 'agent',
              time: Date.now(),
              thinking: null,
              saying: true,
            });
          },
          doneCb: (totalReply) => {
            self.updateOtherLatestHistoryMessage({
              content: totalReply,
              isMe: false,
              role: 'agent',
              time: Date.now(),
              thinking: null,
              saying: null,
            });
          },
          thinkStateChangeCb: (val) => {
            self.updateOtherLatestHistoryMessage({
              content: null,
              isMe: false,
              role: 'agent',
              time: Date.now(),
              thinking: true,
              saying: null,
              thinkState: val,
            });
          },
        });
        if (this.agentFrontSettings && this.agentFrontSettings.onGetReceiveMessage) {
          receiveMessage = await this.agentFrontSettings.onGetReceiveMessage(receiveMessage);
        }
      }
      // 全量输出
      else {
        receiveMessage = await this.getCtx().chatService.sendMessage(sendMessage, {
          debug: self.debug,
          setConfigFields,
          reasoningCb: (totalReasoning) => {
            self.updateOtherLatestHistoryMessage({
              reasoningContent: totalReasoning,
              isMe: false,
              role: 'agent',
              time: Date.now(),
              thinking: null,
              saying: null,
            });
          },
        });
        if (this.agentFrontSettings && this.agentFrontSettings.onGetReceiveMessage) {
          receiveMessage = await this.agentFrontSettings.onGetReceiveMessage(receiveMessage);
        }
      }

      // 打开互动面板
      if (receiveMessage.interactionPanelSettings) {
        this.openInteractionPanel(receiveMessage.interactionPanelSettings);
      }

      // 加入回复消息到历史记录
      let receiveHistoryMessage;
      if (receiveMessage) {
        const historyMessage = {
          id: receiveMessage.id,
          content: receiveMessage.reply,
          time: receiveMessage.time,
          interactionPanelSettings: receiveMessage.interactionPanelSettings,

          isMe: false,
          role: 'agent',
          thinking: null,
        };
        // this.chatHistory.push(historyMessage);
        this.updateOtherLatestHistoryMessage(historyMessage);
        this.saveChatHistory();
        receiveHistoryMessage = historyMessage;
      }

      // 加入发送消息到历史记录（服务端）
      if (canAddSendMessageToHistory) {
        await this.getCtx().chatMapper.pushChatHistoryItem(this.getChatHistorySessionId(), sendHistoryMessage, {});
      }

      // 加入回复消息到历史记录（服务端）
      if (canAddReplyMessageToHistory) {
        await this.getCtx().chatMapper.pushChatHistoryItem(this.getChatHistorySessionId(), receiveHistoryMessage, {});
      }

      // 清空输入框
      this.clearInputMessage();
      // 关闭发送状态
      this.sending = false;

      this.$nextTick(() => {
        this.scrollToBottom();
        // 让输入框重新获得焦点
        this.$refs.messageInput.focus();
      });
    },
    getChatHistorySessionId() {
      if (this.agentFrontSettings && this.agentFrontSettings.setChatHistorySessionId) {
        let t = this.agentFrontSettings.setChatHistorySessionId.replaceAll('{sessionid}', this.currentSession.id);
        let matches = t.matchAll(/{query:([^}]+)}/g);
        if (matches) {
          for (const match of matches) {
            t = t.replace(match[0], queryString(match[1]));
          }
        }
        return t;
      }
      return this.currentSession.id;
    }
  }
}