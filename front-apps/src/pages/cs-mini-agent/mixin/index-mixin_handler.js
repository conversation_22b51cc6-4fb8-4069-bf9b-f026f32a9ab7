export default {
    methods: {
        /**
         * 选择会话
         * @param session {MyChatSession}
         */
        async handleSelectSession(session) {
            this.currentSession = session;

            // 重置
            this.agentFrontSettings = {
                ...this.agentFrontSettings_default,
            };
            for (const prop in this.agentValues) {
                this.agentValues[prop] = null;
            }

            // 加载用户客户端数据
            await this.loadUserClientDataBySession(session.id);

            const agentFrontSettings = await this.getCtx().agentMapper.getAgentFrontSettings(session.otherMembers[0].id);
            if (agentFrontSettings) {
                // 重置交互面板配置
                for (const prop in this.interactionPanelSettings_default) {
                    this.interactionPanelSettings[prop] = this.interactionPanelSettings_default[prop];
                }
                // 加载交互面板配置
                if (agentFrontSettings.interactionPanelSettings) {
                    for (const prop in agentFrontSettings.interactionPanelSettings) {
                        this.interactionPanelSettings[prop] = agentFrontSettings.interactionPanelSettings[prop];
                    }
                }
                // 加载输入区内部工具
                if (agentFrontSettings.inputInnerTools) {
                    for (const tool of agentFrontSettings.inputInnerTools) {
                        if (tool.enabled !== false) {
                            if (this.agentValues[tool.name] == null) {
                                this.agentValues[tool.name] = tool.value;
                            }
                        }
                    }
                }
                // 加载初始提示词
                for (const prop in this.initialPromptSettings_default) {
                    this.initialPromptSettings[prop] = this.initialPromptSettings_default[prop];
                }
                if (agentFrontSettings.getInitialPrompt) {
                    const initialPrompt = await agentFrontSettings.getInitialPrompt({
                        loginUser: await this.getCtx().loginService.getUser()
                    });
                    if (initialPrompt) {
                        for (const prop in initialPrompt) {
                            this.initialPromptSettings[prop] = initialPrompt[prop];
                        }
                    }
                }
                // 加载能否上传
                if (agentFrontSettings.canUploadFile != null) {
                    this.canUploadFile = agentFrontSettings.canUploadFile;
                }

                this.agentFrontSettings = agentFrontSettings;
            }

            this.checkIsSACSessionSelected();

            // 加载对话历史
            await this.loadChatHistory();

            // 触发对话历史为空事件
            if (this.chatHistory.length === 0) {
                // ...
                if (this.agentFrontSettings && this.agentFrontSettings.onChatListEmpty) {
                    const r = this.agentFrontSettings.onChatListEmpty({});
                    if (r) {
                        if (r.toSendContent && r.toSendContent.length > 0) {
                            this.sendMessage({
                                action: 'onChatListEmpty',
                                sendMessageContent: r.toSendContent
                            });
                        }
                    }
                }
            }
        },

        // 点击发送按钮
        async handleChatInputSendButtonClick() {
            if ((this.inputMessage && this.inputMessage.length > 0) || (this.$refs.chatInputFiles0.getFileList().length > 0)) {
                try {
                    await this.sendMessage();
                } catch (exc) {
                    this.$alert(exc.message, { type: 'error' });
                }
            }
        },

        // 点击停止按钮
        async handleChatInputStopButtonClick() {
            // if (this.abortController) {
            //     this.sending = false;
            //     this.updateOtherLatestHistoryMessage({
            //         isMe: false,
            //         role: 'agent',
            //         time: Date.now(),
            //         thinking: false,
            //         saying: null,
            //         thinkState: null,
            //     });
            //     this.abortController.abort();
            // }
            await this.getCtx().chatMapper.abortSendMessageStream(this.chatStreamStateId);
        },

        // 处理回车键
        async handleKeydown(e) {
            if (e.keyCode === 13) {
                // shift + 回车
                if (e.shiftKey) {
                }
                else {
                    e.preventDefault();
                    e.stopPropagation();

                    await this.handleChatInputSendButtonClick();
                    // 阻止换行
                    e.returnValue = false;
                    return false;
                }
            }
        },

        // 输入框内部工具改变
        async handleInputInnerToolsChange() {
            if (this.currentSession) {
                await this.saveUserClientDataBySession(this.currentSession.id);
            }
        },

        // 点击聊天区
        handleChatInputAreaClick(e) {
            // 触发textarea的focus事件
            // this.$refs.messageInput.focus();
        },

        // 黏贴文件
        handleInputPaste(event) {
            const self = this;
            const items = event.clipboardData.items;
            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                if (item.kind === "file") {
                    const file = item.getAsFile();
                    // 允许黏贴图片
                    if (file.type.startsWith('image/')) {
                        self.$refs.chatInputFiles0.addFile(file);
                    }
                }
            }
        },
        handleUploadChange(file) {
            const self = this;
            if (file && file.raw) {
                // 将选择的文件添加到 chatInputFiles0 组件中
                self.$refs.chatInputFiles0.addFile(file.raw);
            }
        },
        // 新建对话
        async handleNewChat() {
            this.deleteMessage(true);
            // ...
            if (this.agentFrontSettings && this.agentFrontSettings.onChatListEmpty) {
                const r = this.agentFrontSettings.onChatListEmpty({});
                if (r) {
                    if (r.toSendContent && r.toSendContent.length > 0) {
                        this.sendMessage({
                            action: 'onChatListEmpty',
                            sendMessageContent: r.toSendContent
                        });
                    }
                }
            }
        },
        // 登出
        handleLogout() {
            // 调用登出服务
            this.getCtx().loginService.logout().then(() => {
                // 登出成功后重定向到登录页
                window.location.reload();
            });
        },
        // 点击深度思考折叠
        handleReasoningToggle(msg) {
            if (this.$set) {
                this.$set(msg, 'isReasoningExpanded', !msg.isReasoningExpanded);
            }
            else {
                msg.isReasoningExpanded = !msg.isReasoningExpanded
            }
        },
        // 语音输入-add
        handleSpeechInputAdd(text) {
            // textarea
            const textarea = this.$refs.messageInput;
            if (textarea) {
                const startPos = textarea.selectionStart;
                const endPos = textarea.selectionEnd;
                const beforeText = this.inputMessage.substring(0, startPos);
                const afterText = this.inputMessage.substring(endPos);
                this.inputMessage = beforeText + text + afterText;

                // 设置光标位置到插入文本之后
                this.$nextTick(() => {
                    textarea.focus();
                    textarea.setSelectionRange(startPos + text.length, startPos + text.length);
                    this.adjustTextareaHeight();
                });
            }
        },
    }
}