<template>
  <div class="kb-selector">
    <div class="kb-header">
      <h3 class="kb-title">知识库选择</h3>
      <el-button type="primary" size="small" @click="loadData">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <div class="kb-search">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索知识库..."
        clearable
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <div class="kb-list" v-loading="loading">
      <div
        v-for="kb in filteredKnowledgeBases"
        :key="kb.id"
        :class="['kb-item', { 'kb-item-selected': selectedKb && selectedKb.id === kb.id }]"
        @click="selectKnowledgeBase(kb)"
      >
        <div class="kb-icon">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <div class="kb-info">
          <div class="kb-name">{{ kb.label }}</div>
        </div>
      </div>

      <div v-if="filteredKnowledgeBases.length === 0 && !loading" class="kb-empty">
        <el-icon class="empty-icon"><Document /></el-icon>
        <p>{{ searchKeyword ? '未找到匹配的知识库' : '暂无知识库' }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { Search, Refresh, FolderOpened, Document } from '@element-plus/icons-vue'
import {KbDataSourceMapper} from "../../../code/module/cs-mini-kb/mapper/KbDataSourceMapper.js";

/**
 * 知识库选择器
 */
export default {
  name: 'kb-selector',
  components: {
    Search,
    Refresh,
    FolderOpened,
    Document
  },
  props: {
    selectedKbId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['kb-selected', 'kb-changed'],
  data() {
    return {
      loading: false,
      searchKeyword: '',
      // 选中的知识库
      selectedKb: null,
      // 知识库列表
      kbList: [
        // {
        //   label: '绿化指导站',
        //   id: '20250604112651723-740db3'
        // },
        // {
        //   label: '绿化工程',
        //   id: '20241023172616342-6e7218'
        // },
        // {
        //   label: '隧道网',
        //   id: '20250902170331804-3b9e4f'
        // },
      ],
    }
  },
  computed: {
    filteredKnowledgeBases() {
      if (!this.searchKeyword) {
        return this.kbList
      }
      return this.kbList.filter(kb =>
        kb.label.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    async onShowed() {
      await this.loadData();
    },

    async loadData() {
      this.loading = true
      const r = await this.getCtx().kbDataSourceMapper.getMyAccessKBList();

      this.kbList = r.map((n) => {
        return {
          id: n.id,
          label: n.name,
        }
      });

      this.loading = false;
    },

    // async refreshKnowledgeBases() {
    //   this.loading = true
    //   try {
    //     // 这里应该调用实际的API
    //     // const response = await this.knowledgeBaseApi.getList()
    //     // this.kbList = response.data
    //
    //     // 模拟API调用
    //     await new Promise(resolve => setTimeout(resolve, 1000))
    //     this.$message.success('知识库列表已刷新')
    //   } catch (error) {
    //     this.$message.error('刷新失败：' + error.message)
    //   } finally {
    //     this.loading = false
    //   }
    // },

    getSelectedKb() {
      return this.selectedKb;
    },

    handleSearch() {
      // 搜索逻辑已在computed中处理
    },

    selectKnowledgeBase(kb) {
      this.selectedKb = kb
      this.$emit('kb-selected', kb)
      this.$emit('kb-changed', kb.id)
    }
  },
  watch: {
    selectedKbId: {
      handler(newId) {
        if (newId) {
          const kb = this.kbList.find(item => item.id === newId)
          if (kb) {
            this.selectedKb = kb
          }
        } else {
          this.selectedKb = null
        }
      },
      immediate: true
    }
  },
  async mounted() {
    const ctx = this.getCtx();
    if (ctx.kbDataSourceMapper == null) ctx.kbDataSourceMapper = new KbDataSourceMapper(ctx);

    // 组件挂载时可以自动加载知识库列表
    // this.refreshKnowledgeBases()
  }
}
</script>

<style scoped lang="less">
.kb-selector {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e0e0e0;

  .kb-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    border-bottom: 1px solid #f0f0f0;

    .kb-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .kb-search {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .kb-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;

    .kb-item {
      display: flex;
      align-items: center;
      padding: 6px 12px;
      margin-bottom: 8px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      &:hover {
        background-color: #f8f9fa;
        border-color: #e0e0e0;
      }

      &.kb-item-selected {
        background-color: #e6f7ff;
        border-color: #1890ff;

        .kb-name {
          color: #1890ff;
          font-weight: 600;
        }
      }

      .kb-icon {
        margin-right: 12px;
        display: flex;
        align-items: center;

        .el-icon {
          font-size: 18px;
          color: #666;
        }
      }

      .kb-info {
        flex: 1;
        display: flex;
        align-items: center;

        .kb-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          line-height: 1;
          margin: 0;
        }
      }
    }

    .kb-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #999;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 12px;
        opacity: 0.5;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}
</style>