<template>
  <div class="index_project-view" v-loading="loading">
    <!-- 上半部分区域 - 项目配置区 -->
    <div class="up-part-area">
      <div class="up-part_left-part">
        <el-form :model="form" label-width="80px" class="project-form">
          <el-row :gutter="20">
            <el-col :span="8" style="max-width: 500px">
              <el-form-item label="项目名称" required>
                <el-input v-model="form.name" placeholder="请输入项目名称" @change="onFieldChange('name')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7" style="max-width: 500px">
              <el-form-item label="长文类型" required>
                <el-select v-model="form.article_type" placeholder="请选择长文类型"
                           @change="onFieldChange('article_type')"
                           style="width: 100%">
                  <template v-for="item in articleTypeOptions">
                    <el-option :label="item.label" :value="item.value"></el-option>
                  </template>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="9" style="max-width: 500px">
              <el-form-item label="选择模型" required>
                <el-select v-model="form.model" placeholder="请选择模型" @change="onFieldChange('model')"
                           style="width: 100%">
                  <template v-for="item in modelOptions">
                    <el-option :label="item.label" :value="item.value"></el-option>
                  </template>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="项目需求" required>
            <el-input
                v-model="form.project_req" @change="onFieldChange('project_req')"
                type="textarea"
                :rows="6"
                placeholder="例：写一个公园管理和建设的应用方案，考虑AI如何在公园这层面赋能"
                style="width: 100%">
            </el-input>
          </el-form-item>

          <el-form-item label="大纲要求">
            <el-input
                v-model="form.outline_req" @change="onFieldChange('outline_req')"
                type="textarea"
                :rows="6"
                placeholder="例：章节数量不要多于2个，每个章节中的段落不能少于3个"
                style="width: 100%">
            </el-input>
          </el-form-item>
        </el-form>
        <div class="generation-cards">
          <div class="generation-card step-card">
            <div class="card-header">
              <el-icon class="card-icon">
                <List/>
              </el-icon>
              <h3 class="card-title">分步生成</h3>
            </div>
            <div class="card-content">
              <p class="card-description">先生成大纲并可自行修改，再生成长文</p>
              <div class="card-actions">
                <el-button type="primary" @click="generateOutline" class="action-btn" :disabled="!canGenerate">
                  <el-icon>
                    <Document/>
                  </el-icon>
                  生成大纲
                </el-button>
                <el-button type="primary" @click="continueGenerateArticle" class="action-btn" :disabled="!canGenerate">
                  <el-icon>
                    <EditPen/>
                  </el-icon>
                  继续生成长文
                </el-button>
              </div>
            </div>
          </div>

          <div class="generation-card complete-card">
            <div class="card-header">
              <el-icon class="card-icon">
                <Lightning/>
              </el-icon>
              <h3 class="card-title">完整生成</h3>
            </div>
            <div class="card-content">
              <p class="card-description">直接生成大纲和长文</p>
              <div class="card-actions">
                <el-button type="primary" @click="generateArticle" class="action-btn full-width"
                           :disabled="!canGenerate">
                  生成长文
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="width: 10px;">&nbsp;</div>
      <div class="up-part_right-part">
        <!-- 参考资料区 -->
        <index_project-ref-area ref="refArea0" :projectForm="form"></index_project-ref-area>
      </div>
    </div>

    <!-- 下半部分区域 - 项目执行区 -->
    <div class="down-part-area" style="height: 100%;">
      <index_project-down-area
          ref="downArea0" @on-outline-change="handleOutlineChange"
          @view-long-article="handleViewLongArticle"
          @download-long-article="handleDownloadLongArticle"></index_project-down-area>
    </div>

    <!-- 长文查看弹窗 -->
    <el-dialog
        title="查看长文"
        v-model="longArticleDialogVisible"
        width="80%"
        :close-on-click-modal="false" :show-close="false"
        class="long-article-dialog"
        top="5vh"
    >
      <div v-loading="longArticleLoading" class="long-article-content">
        <div v-if="temp.latestLongArticleContent" class="article-text">
          <pre class="article-pre">{{ temp.latestLongArticleContent }}</pre>
        </div>
        <div v-else class="no-article">
          <el-icon class="no-article-icon">
            <Document/>
          </el-icon>
          <p class="no-article-text">还没有生成长文</p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="longArticleDialogVisible = false">关闭</el-button>
          <el-button v-if="temp.latestLongArticleContent" type="primary" @click="copyLongArticle">
            <el-icon>
              <CopyDocument/>
            </el-icon>
            复制内容
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import {defineComponent} from 'vue'
import {Document, List, EditPen, Lightning, CopyDocument} from '@element-plus/icons-vue'
import Index_projectDownArea from "./index_project-down-area.vue";
import Index_projectRefArea from "./index_project-ref-area.vue";

export default defineComponent({
  name: "index_project-view",
  components: {
    Index_projectRefArea,
    Index_projectDownArea,
    Document,
    List,
    EditPen,
    Lightning,
    CopyDocument
  },
  watch: {
    canGenerate: function (newVal) {
      this.$refs.downArea0.setIsTaskDoing(!newVal);
    },
  },
  data() {
    return {
      loading: false,
      canGenerate: false,

      // 长文查看弹窗相关
      longArticleDialogVisible: false,
      longArticleLoading: false,

      articleTypeOptions: [
        {label: '报告', value: '报告'},
        {label: '方案', value: '方案'},
        {label: '项目方案', value: '项目方案'},
      ],

      modelOptions: [
        {label: 'DeepSeek-V3.1-满血（ALiBL）', value: 'albl_deepseek-v3.1'},
        {label: 'DeepSeek-V3-满血（ALiBL）', value: 'albl_deepseek-v3'},
        {label: 'DeepSeek-V3.1-满血（官网）', value: 'ds_deepseek-chat'},
        {label: 'Qwen3-235b（ALiBL）', value: 'albl_qwen3-235b-a22b|no_think|'},
        {label: 'GLM-4.5', value: 'zhipu_glm-4.5|no_think|'},
        {label: 'GLM-4.5-Air', value: 'zhipu_glm-4.5-air|no_think|'},
        {
          label: 'MiniMax-Text-01（MiniMax）',
          value: 'minimax_MiniMax-Text-01',
          title: '上下文1000k',
          canVision: true,
          canToolCall: true,
        },
        {
          label: 'Kimi-k2-0711-preview',
          value: 'msa_kimi-k2-0711-preview',
          title: '上下文13w',
          canToolCall: true,
        },
        {label: 'GPT-4.1（Brain）', value: 'br_gpt-4.1'},
        {label: 'GPT-4o（Brain）', value: 'br_gpt-4o'},
        {label: 'GPT-OSS-20b（公司）', value: 'ollama_gpt-oss:20b'},
        {label: 'Qwen3-32b（公司）', value: 'ollama_qwen3:32b|no_think|'},
      ],

      form: {
        id: null,
        // 项目名称
        name: null,
        // 长文类型
        article_type: '报告',
        // 模型【选择器】
        model: null,
        // 项目需求
        project_req: null,
        // 大纲要求
        outline_req: null,
      },

      temp: {
        inputProject: null,
        latestLongArticleContent: null,
      }
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    reset() {
      this.form.id = null;
      this.form.name = null;
      this.form.article_type = '报告';
      this.form.project_req = null;
      this.form.outline_req = null;
    },
    async onShowed(pars = {}) {
      const self = this;
      this.loading = true;
      this.temp.inputProject = pars.project;

      self.reset();
      const details = await this.getCtx().longArticleProjectMapper.getProjectDetails(pars.project.id);
      for (const prop in details) {
        this.form[prop] = details[prop];
      }
      this.loading = false;

      await self.getTaskResult();

      if (this.form.id && this.form.id.length > 0) {
        await this.startHookTaskState();
      }

      await this.$refs.refArea0.onShowed();
    },
    /**
     * 校验字段
     * name, model, project_req 不能为空
     */
    validateForm() {
      const errors = [];

      if (!this.form.name || this.form.name.trim() === '') {
        this.$message.error('项目名称不能为空');
      } else if (!this.form.article_type) {
        this.$message.error('请选择长文类型');
      } else if (!this.form.model) {
        this.$message.error('请选择模型');
      } else if (!this.form.project_req || this.form.project_req.trim() === '') {
        this.$message.error('项目需求不能为空');
      } else {
        return true;
      }

      return false;
    },
    /**
     * 生成大纲
     */
    async generateOutline() {
      const self = this;
      if (!this.validateForm()) {
        return;
      }

      self.canGenerate = false;
      self.loading = true;
      await this.getCtx().longArticleManagerMapper.assignTask('only-outline',
          this.form.model, this.form.id, this.form.project_req, this.form.outline_req);
      self.loading = false;

      self.$message({message: '已开始生成，请在下方生成预览区查看', type: 'success'});

      await self.startHookTaskState();
    },
    /**
     * 继续生成长文
     */
    async continueGenerateArticle() {
      const self = this;
      if (!this.validateForm()) {
        return;
      }
      if ((this.$refs.downArea0.getOutline() ?? '').trim() === '') {
        this.$message.error('大纲不能为空');
        return;
      }

      self.canGenerate = false;
      self.loading = true;
      await this.getCtx().longArticleManagerMapper.assignTask('only-article',
          this.form.model, this.form.id, this.form.project_req, this.form.outline_req);
      self.loading = false;

      self.$message({message: '已开始生成，请在下方生成预览区查看', type: 'success'});

      await self.startHookTaskState();
    },
    /**
     * 生成长文
     */
    async generateArticle() {
      const self = this;
      if (!this.validateForm()) {
        return;
      }

      self.canGenerate = false;
      self.loading = true;
      await this.getCtx().longArticleManagerMapper.assignTask(null,
          this.form.model, this.form.id, this.form.project_req, this.form.outline_req);
      self.loading = false;

      self.$message({message: '已开始生成，请在下方生成预览区查看', type: 'success'});

      await self.startHookTaskState();
    },

    /**
     * 开始监控任务状态
     */
    async startHookTaskState() {
      const self = this;
      // 取消监控
      await this.getCtx().longArticleManagerMapper.cancelUserHookTaskState();
      // 开启监控
      self.$refs.downArea0.clearContentPreview()
      this.getCtx().longArticleManagerMapper.hookTaskState(this.form.id, (str) => {
        const stateKeys = [
          'PENDING',
          'GENERATING-OUTLINE', 'GENERATE-OUTLINE-END',
          'GENERATING-ARTICLE', 'GENERATE-ARTICLE-END',
          'ALL-DONE',
          'FAILED', 'CANCEL-HOOK'
        ];
        // 状态变动反馈
        if (str.startsWith('[LA][STATE]')) {
          self.log(`hookTaskState.onData ${str}`)

          for (const stateKey of stateKeys) {
            if (str.startsWith(`[LA][STATE]${stateKey}`)) {
              // 设置状态键
              self.$refs.downArea0.setState(stateKey);
              // 生成完毕-获取结果
              if (stateKey === 'GENERATE-OUTLINE-END' || stateKey === 'ALL-DONE') {
                self.getTaskResult();
              }
              // 停止状态
              if (stateKey === 'PENDING' || stateKey === 'ALL-DONE' || stateKey === 'CANCEL-HOOK' || stateKey === 'FAILED') {
                self.canGenerate = true;
              } else {
                self.canGenerate = false;
              }

              let str1 = str.replace(`[LA][STATE]${stateKey}`, '');
              if (str1.length > 0) {
                self.$refs.downArea0.pushContentPreviewChunk(str1);
              }
              break;
            }
          }
        }
        // 错误反馈
        else if (str.startsWith('[LA][ERROR]')) {
          self.log(`hookTaskState.onData ${str}`)
          self.$refs.downArea0.setState('FAILED');

          self.$refs.downArea0.pushContentPreviewChunk(`\n\n\n*** 错误 ***\n${str.substring('[LA][ERROR]'.length)}`);
        } else {
          self.$refs.downArea0.pushContentPreviewChunk(str);
        }
      }, () => {
      }, (err) => {
      })
    },

    async getTaskResult() {
      const taskResult = await this.getCtx().longArticleManagerMapper.getTaskResult(this.form.id);
      if (taskResult) {
        this.$refs.downArea0.setOutline(taskResult.outline);
      }
    },

    async onFieldChange(name) {
      const value = this.form[name];
      // console.log(name, value)

      if (name === 'name') {
        this.temp.inputProject.name = value;
      }

      const form = {};
      form[name] = value;
      this.getCtx().longArticleProjectMapper.saveProjectFields(this.temp.inputProject.id, form)
    },

    async handleOutlineChange(e) {
      this.loading = true;
      await this.getCtx().longArticleManagerMapper.saveOutline(this.temp.inputProject.id, e);
      this.loading = false;
    },

    /**
     * 查看最新长文
     */
    async handleViewLongArticle() {
      this.longArticleLoading = true;
      this.longArticleDialogVisible = true;

      try {
        this.temp.latestLongArticleContent = await this.getCtx().longArticleManagerMapper
            .loadLatestLongArticle(this.temp.inputProject.id);
      } catch (error) {
        this.$message.error('加载长文失败：' + error.message);
        this.temp.latestLongArticleContent = null;
      } finally {
        this.longArticleLoading = false;
      }
    },

    async handleDownloadLongArticle() {
      await this.getCtx().longArticleManagerMapper.loadLatestLongArticleToWord(this.temp.inputProject.id);
    },

    /**
     * 复制长文内容
     */
    async copyLongArticle() {
      if (!this.temp.latestLongArticleContent) {
        this.$message.warning('没有内容可复制');
        return;
      }

      try {
        await navigator.clipboard.writeText(this.temp.latestLongArticleContent);
        this.$message.success('内容已复制到剪贴板');
      } catch (error) {
        // 如果现代API失败，尝试传统方法
        try {
          const textArea = document.createElement('textarea');
          textArea.value = this.temp.latestLongArticleContent;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          this.$message.success('内容已复制到剪贴板');
        } catch (fallbackError) {
          this.$message.error('复制失败，请手动复制');
        }
      }
    },

    log(msg) {
      console.log(`[index_project-view] ${msg}`)
    },

    /**
     * 获取参考资料
     */
    getRefMaterials() {
      return this.$refs.refArea0?.getUnderstoodItems() || []
    },

    /**
     * 清空参考资料
     */
    clearRefMaterials() {
      this.$refs.refArea0?.clearAll()
    }
  }
})
</script>

<style scoped lang="less">
.index_project-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  .up-part-area {
    flex-shrink: 0;
    padding: 16px 16px 16px 16px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    margin: 12px 16px 16px 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);

    display: flex;
    flex-direction: row;

    .up-part_left-part {
      flex: 1;

      .project-form {
        width: 100%;
        margin-bottom: 0;

        :deep(.el-form-item) {
          margin-bottom: 12px;

          .el-form-item__label {
            font-weight: 600;
            color: #2d3748;
            font-size: 14px;
          }

          .el-input {
          }

          .el-select {
          }

          .el-textarea {
          }
        }
      }

      .generation-cards {
        display: flex;
        gap: 24px;
        margin-top: 5px;

        .generation-card {
          flex: 1;
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
          border-radius: 16px;
          padding: 24px;
          border: 1px solid rgba(0, 0, 0, 0.08);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
          }

          &:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border-color: rgba(102, 126, 234, 0.3);

            &::before {
              transform: scaleX(1);
            }
          }

          .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .card-icon {
              font-size: 28px;
              color: #667eea;
              margin-right: 12px;
              transition: all 0.3s ease;
            }

            .card-title {
              font-size: 20px;
              font-weight: 700;
              color: #2d3748;
              margin: 0;
              line-height: 1.2;
            }
          }

          .card-content {
            .card-description {
              font-size: 14px;
              color: #718096;
              margin-bottom: 10px;
            }

            .card-actions {
              display: flex;
              flex-direction: row;
              gap: 12px;

              .action-btn {
                border-radius: 10px;
                padding: 12px 20px;
                font-weight: 600;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                flex: 1;

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
                }

                &.full-width {
                  width: 100%;
                  padding: 16px 20px;
                  font-size: 16px;
                }

                .el-icon {
                  margin-right: 8px;
                }

                &.is-disabled {
                  background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%) !important;
                  opacity: 0.6;
                  cursor: not-allowed;
                  box-shadow: none !important;

                  &:hover {
                    transform: none;
                    box-shadow: none !important;
                  }
                }
              }
            }
          }

          &.step-card {
            .card-header .card-icon {
              color: #48bb78;
            }

            &::before {
              background: linear-gradient(90deg, #48bb78 0%, #38a169 100%);
            }

            .card-actions .action-btn {
              background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
              box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);

              &:hover {
                box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
              }

              &.is-disabled {
                background: linear-gradient(135deg, rgba(72, 187, 120, 0.3) 0%, rgba(56, 161, 105, 0.3) 100%) !important;
                opacity: 0.6;
                cursor: not-allowed;
                box-shadow: none !important;

                &:hover {
                  transform: none;
                  box-shadow: none !important;
                }
              }
            }
          }

          &.complete-card {
            .card-header .card-icon {
              color: #ed8936;
            }

            &::before {
              background: linear-gradient(90deg, #ed8936 0%, #dd6b20 100%);
            }

            .card-actions .action-btn {
              background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
              box-shadow: 0 4px 15px rgba(237, 137, 54, 0.3);

              &:hover {
                box-shadow: 0 8px 25px rgba(237, 137, 54, 0.4);
              }

              &.is-disabled {
                background: linear-gradient(135deg, rgba(237, 137, 54, 0.3) 0%, rgba(221, 107, 32, 0.3) 100%) !important;
                opacity: 0.6;
                cursor: not-allowed;
                box-shadow: none !important;

                &:hover {
                  transform: none;
                  box-shadow: none !important;
                }
              }
            }
          }
        }
      }
    }

    .up-part_right-part {
      width: 300px;
    }
  }

  .down-part-area {
    flex: 1;
    padding: 16px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    margin: 0 16px 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
  }

  /* 长文查看弹窗样式 */

  :deep(.long-article-dialog) {
    .el-dialog {
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
      max-height: 90vh;
    }

    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px;

      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: #e7e7e7;
      }

      .el-dialog__close {
        color: white;
        font-size: 20px;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    .el-dialog__body {
      padding: 0;
      background: #fafafa;
      max-height: 70vh;
      overflow: hidden;

      .long-article-content {
        height: 70vh;
        overflow: auto;
        padding: 24px;

        .article-text {
          .article-pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #2d3748;
            background: white;
            padding: 24px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            margin: 0;
          }
        }

        .no-article {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #718096;

          .no-article-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
          }

          .no-article-text {
            font-size: 18px;
            margin: 0;
          }
        }
      }
    }

    .el-dialog__footer {
      background: #fafafa;
      padding: 24px;
      border-top: 1px solid #e2e8f0;

      .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;

        .el-button {
          padding: 12px 24px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;

          &.el-button--primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            }

            .el-icon {
              margin-right: 8px;
            }
          }

          &:not(.el-button--primary) {
            border: 2px solid #e2e8f0;
            color: #4a5568;

            &:hover {
              border-color: #cbd5e0;
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }
}
</style>