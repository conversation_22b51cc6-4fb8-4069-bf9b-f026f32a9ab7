import CBAIOMapper from "../../../mapper/CBAIOMapper.js";

export class LongArticleManagerMapper {

    constructor(ctx) {
        this.ctx = ctx;
        this.cbaioMapper = new CBAIOMapper(ctx)
    }

    async assignTask(type, model, project_id, project_req, outline_req) {
        const r = await this.cbaioMapper.reqByPost1('/api/la/assignTask', {
            type, model, project_id, project_req, outline_req,
        });
        if (r.success === false) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    async hookTaskState(project_id, onData, onDone, onError) {
        await this.cbaioMapper.reqByPostStream('/api/la/hookTaskState', {
            project_id
        }, (str) => {
            if (onData) onData(str);
        }, () => {
            if (onDone) onDone();
        }, (err) => {
            if (onError) onError();
        });
    }

    async cancelUserHookTaskState() {
        const r = await this.cbaioMapper.reqByPost1('/api/la/cancelUserHookTaskState', {
        });
        if (r.success === false) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    async getTaskResult(project_id) {
        const r = await this.cbaioMapper.reqByPost1('/api/la/getTaskResult', {
            project_id
        });
        if (r.success === false) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    async saveOutline(project_id, outline) {
        const r = await this.cbaioMapper.reqByPost1('/api/la/saveOutline', {
            project_id, outline
        });
        if (r.success === false) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    async loadLatestLongArticle(project_id) {
        const r = await this.cbaioMapper.reqByPost1('/api/la/loadLatestLongArticle', {
            project_id
        });
        if (r.success === false) {
            throw new Error(r.msg);
        }
        return r.data;
    }

    async loadLatestLongArticleToWord(project_id) {
        await this.cbaioMapper.downloadFile('/api/la/loadLatestLongArticleToWord', {
            project_id
        });
    }
}