import path from 'path';
import {defineConfig, loadEnv} from 'vite';
import vue from '@vitejs/plugin-vue'
// import vue2 from '@vitejs/plugin-vue2';
// import { createVuePlugin } from 'vite-plugin-vue2'
// import babel from '@rollup/plugin-babel';

export default ({mode}) => {
    const {VITE_PORT, VITE_BASE_URL} = loadEnv(mode, process.cwd());

    return defineConfig({
        base: './',
        plugins: [
            vue({
                template: {
                    compilerOptions: {
                        isCustomElement: tag => tag === 'webview'
                    }
                }
            }),
            // vue2(),
            // createVuePlugin(),
            // babel({
            //   babelHelpers: 'bundled',
            //   exclude: 'node_modules/**', // 确保排除 node_modules
            // }),
        ],
        resolve: {
            alias: [
                // 注释掉是为了取消ide自动导入时用的绝对路径
                // {
                //   find: '@',
                //   replacement: path.resolve(__dirname, 'src') // 指向 src 目录
                // },

                // // 修复element与vite不兼容问题
                // {
                //   find: 'vue',
                //   replacement: 'vue/dist/vue.esm.js'
                // }
            ]
            // alias: {
            //   '@': path.resolve(__dirname,'src')
            // }
        },
        server: {
            port: 4000, // 这里设置你想要的固定端口号
            strictPort: true, // 如果端口已被占用，则直接退出而不是尝试下一个可用端口
            host: '0.0.0.0', // 允许局域网访问
            // proxy: {
            //   '/child-page': {
            //     target: 'http://127.0.0.1:4001',
            //     changeOrigin: true,
            //     rewrite: (path) => path.replace(/^\/child-page/, '')
            //   }
            // },
            // cors: {
            //     origin: 'http://localhost:4001', // 允许 iframe 来源
            //     methods: ['GET', 'POST', 'OPTIONS'],
            //     allowedHeaders: ['Content-Type']
            // }
        }
    });
}