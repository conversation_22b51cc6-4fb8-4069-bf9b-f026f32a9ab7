@echo off
:: *** 打包并复制 ***
::准备
set tgtPath="..\..\cs-ai-client\src\renderer\src"
::打包
@REM call npm run build
::删除目录
call del /s /q "%tgtPath%\code\*"
call rd /s /q "%tgtPath%\code\*"

call del /s /q "%tgtPath%\pages\*"
call rd /s /q "%tgtPath%\pages\*"

call del /s /q "%tgtPath%\store\*"
call rd /s /q "%tgtPath%\store\*"

::复制
call xcopy ".\src\code\*" "%tgtPath%\code\"  /E /Y
call xcopy ".\src\pages\*" "%tgtPath%\pages\"  /E /Y
call xcopy ".\src\store\*" "%tgtPath%\store\"  /E /Y

call xcopy ".\public\*" "%tgtPath%\..\public\"  /E /Y