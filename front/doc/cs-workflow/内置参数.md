
# 参数占位符
- {wf-label} 获取工作流标签
- {input-data} 前一个节点的输出内容
- {input-length} 前一个节点的输出内容长度
- {input-index} 遍历序号（如果之前有遍历节点）
- {input-list-size} 遍历列表长度（如果之前有遍历节点）
- {input:xxx} 读取参数名为xxx的参数值（从preNodeParams获取）


# 开发说明
startParams.__workflow_label 本工作流标签（需要在start时候传入到启动参数中）

preNodeParams.__data 前一个节点的输出内容
preNodeParams.__length 一个节点的输出内容长度
preNodeParams.__index 遍历序号（如果之前有遍历节点）
preNodeParams.__list_size 遍历列表长度（如果之前有遍历节点）

result.params.__data 输出本节点内容
result.params.__length 输出本节点内容长度
result.params.__error 输出本节点错误信息

result.toNextResults 用于分发给多个下个节点


# 用户输入设计
```json5
{
  "direction": "row | column",
  "items": [
    { 
      "direction": "row | column",
      "items": [
        { "label": "xxx", "name": "name", "type": "text" }
      ]
    }
  ]
}
```