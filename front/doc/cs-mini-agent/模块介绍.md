# 基本描述
- 模块名称：cs-mini-agent 虚拟专员
- 模块描述：虚拟专员，是基于AI技术，为客服提供智能辅助的工具。


# 页面介绍
- 主页面：src/pages/cs-mini-agent/index.vue
- 主页面共分为三个区域：
    - 左栏：虚拟专员会话列表，类似微信的会话列表
    - 中栏：聊天区，类似微信的聊天区（聊天历史列表 + 聊天输入框）
    - 右栏：交互面板，用于展示信息和接收用户输入的区域
- 页面布局：左栏和中栏是固定宽度，右栏宽度是自适应


# 数据层级
- 会话列表 ChatSessionList
    - 会话 ChatSession
        - 会话成员列表 ChatMemberList
            - 会话成员 ChatMember
        - 聊天历史 ChatHistory
            - 聊天消息 ChatMessage
        - 聊天发送消息 ChatSendMessage
        - 聊天接收消息 ChatReceiveMessage


# 数据对象
## 会话成员 ChatMember
- 类型：Object，例：{ id, name, avatar }
- 字段：
    - id：{string} 成员ID
    - name：{string} 成员名称
    - avatar：{string} 成员头像

## 会话 ChatSession
- 类型：Object，例：{ id, members, title, summary }
- 字段：
    - id：{string} 会话ID
    - members: {Array<ChatMember>} 会话成员列表
    - title：{string} 会话标题
    - summary：{string} 会话摘要

## 会话列表 ChatSessionList
- 类型：Array<ChatSession>

## 聊天消息 ChatHistoryItem
- 类型：Object，例：{ id, message, isMe, role, time }
- 字段：
    - id：{string} 消息ID
    - message：{string} 消息内容
    - isMe：{boolean} 是否是当前用户发送的消息
    - role：{string} 消息角色，取值：user、assistant、system、other-user
    - time：{number} 消息发送时间（时间戳）

## 聊天历史 ChatHistory
- 类型：Array<ChatHistoryItem>

## 工具调用 ToolCall
- 类型：Object，例：{ name, args }
- 字段：
    - name：{string} 工具名称
    - args：{Object} 工具参数

## 聊天发送消息 ChatSendMessage
- 类型：Object，例：{ sessionId, memberId, time, message }
- 字段：
    - message：{string} 消息内容
    - sessionId：{string} 会话ID
    - memberId{string} 发送人ID
    - time：{number} 消息发送时间（时间戳）

## 聊天接收消息 ChatReceiveMessage
- 类型：Object，例：{ sessionId, memberId, time, message, toolCalls }
- 字段：
    - reply：{string} 回复内容
    - sessionId：{string} 会话ID
    - memberId{string} 发送人ID
    - time：{number} 消息接收时间（时间戳）
    - toolCalls：{Array<ToolCall>} 工具调用
