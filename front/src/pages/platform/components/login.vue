<template>
  <div class="login-container">
    <div class="login-form">
      <div class="form-item username-container">
        <el-input v-model="username" :disabled="loading" placeholder="请输入用户名" prefix-icon="User"
          @keyup.enter.native="handleLogin" style="flex: 2" />
        <span class="at-symbol">@</span>
        <el-input v-model="sac" :disabled="loading" placeholder="请输入域名" @keyup.enter.native="handleLogin"
          style="flex: 1" />
      </div>
      <div class="form-item" style="margin-bottom: 10px;">
        <el-input v-model="password" :disabled="loading" type="password" placeholder="请输入密码" prefix-icon="Lock"
          @keyup.enter.native="handleLogin" />
      </div>
      <div class="form-item" style="text-align: right;margin-bottom: 10px;padding-right: 10px;">
        <el-checkbox v-model="jzmm" label="记住密码" />
      </div>
      <div class="form-item">
        <el-button type="primary" :loading="loading" @click="handleLogin" style="width: 100%">
          登<span style="margin-left: 8px;">&nbsp;</span>录
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    return {
      sac: '',
      username: '',
      password: '',
      // 记住密码
      jzmm: null,

      loading: false
    }
  },
  methods: {
    setForm(form) {
      if (form) {
        // this.sac = form.sac;
        // this.username = form.username;
        // this.jzmm = form.jzmm;
        for (const prop in form) {
          this[prop] = form[prop];
        }
      }
    },
    handleLogin() {
      const self = this;
      if (!this.username || !this.password || !this.sac) {
        this.$message.warning('请输入用户名、密码和域名')
        return
      }
      this.loading = true
      // 触发登录事件
      this.$emit('login', {
        sac: this.sac,
        username: this.username,
        password: this.password,
        jzmm: this.jzmm,
        doneCallback: (r) => {
          if (!r.success) {
            self.$message.error(r.message || '登录失败，请检查用户名和密码');
          }
          self.loading = false;
        }
      })
    }
  }
}
</script>

<style scoped>
.login-container {
  width: 100%;
  padding: 0;
}

.login-form {
  width: 100%;
  max-width: 350px;
  margin: 0 auto;
}

.form-item {
  margin-bottom: 20px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.username-container {
  display: flex;
  align-items: center;
}

.at-symbol {
  margin: 0 5px;
  color: #606266;
}
</style>