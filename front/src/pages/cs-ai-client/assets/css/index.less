body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  height: 100vh;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #1D3FAA;
}

.cs-ai-client {
  display: flex;
  flex-direction: column;

  .left-bar {

    .top-bar {
      line-height: 60px;
      color: white;
      display: flex;
      flex-direction: column;
      align-items: center;

      .logo {
        font-size: 18px;
        font-weight: bold;
        text-align: center;
      }
    }

    .bottom-bar {
      height: 50px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      padding: 0;
      color: silver;

      .user-label {

        img {
          width: 25px;
          height: 25px;
          vertical-align: middle;
          margin-right: 3px;
        }

        span {
          vertical-align: middle;
          font-size: 13px;
        }
      }

      .logout-btn {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 8px 0 8px 8px;
        border-radius: 4px;
        transition: background-color 0.2s;

        //&:hover {
        //  background-color: #28b7ef;
        //}

        img, i {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          font-size: 20px;
        }

        span {
          color: #666;
          font-size: 14px;
        }
      }
    }
  }

  .left-right-main-area {
    flex-shrink: 0;

    .inner-border-bar-left {
      background-color: #F1F5F8;
      -webkit-border-radius: 16px 0 0 16px;
      -moz-border-radius: 16px 0 0 16px;
      margin: 10px 0 10px 0;
    }

    .inner-border-bar-right {
      background-color: #F1F5F8;
      -webkit-border-radius: 0 16px 16px 0;
      -moz-border-radius: 0 16px 16px 0;
      margin: 10px 0 10px 0;
    }

    .top-bar {
      line-height: 35px;
      background-color: #F1F5F8;
    }

    .tab-bar {
      background-color: #F1F5F8;

      .el-tabs__header {
        padding: 0;
        margin: 0 0 10px;
      }

      .el-tabs__content {
        height: 0;
        padding: 0;
      }

      .el-tabs__item {
        padding: 0 10px;
      }

      .right-bar {
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 50px;

        .alert {
          height: 35px;
          cursor: pointer;
        }
      }
    }

    .content-area {
      background-color: #F1F5F8;
      flex: 1;
      overflow: hidden;
      position: relative;
    }

    .content-area-bottom {
      background-color: #F1F5F8;
      height: 10px;
    }
  }
}