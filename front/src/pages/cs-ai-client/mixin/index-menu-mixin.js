// import Vue from "vue";

import { waitGetObj } from "../../../code/util/code-util";

export default {
    data() {
        return {
            activeApp: null,
            activeAppCacheType: null,

            leftMenus: [],
            openMenus: [],
            openMenuHistory: [],
            firstOpenMenus: [],
        }
    },
    methods: {
        // 收藏
        async collect(pars) {
            const myResidentAppResult = await this.getCtx().myResidentAppService.collect(pars)
            await this.loadLeftMenus(myResidentAppResult);
        },
        // 取消收藏
        async discard(pars) {
            const myResidentAppResult = await this.getCtx().myResidentAppService.discard(pars)
            await this.loadLeftMenus(myResidentAppResult)
        },
        // 打开应用
        async openApp(name, opts = {}) {
            let debug = false;

            // 修正传入name
            if (name.indexOf('/') !== -1) {
                name = name.replace('/', '_');
            }

            const allApps = this.getCtx().$appCenterList
            // 从应用库中找
            let findApp = allApps.find(a => a.name === name);
            // 从我的常驻应用找（收藏的子应用）
            if (findApp == null) findApp = this.getCtx().myResidentAppResult.items.find(a => a.name === name);
            if (debug) console.log('openApp: ', name, findApp);

            if (name === 'empty') {
                this.activeApp = null;
                this.activeAppCacheType = null;
                this.$router.push(`/cac/app/empty`);
                return;
            }

            if (findApp == null) {
                console.error(`未找到应用：${name}`);
                this.$router.push(`/cac/app/empty`);
                return;
            }

            // 验证路由
            if (findApp.start_url && findApp.start_url.length > 0) {
                // URL应用无需检查
            }
            else {
                const findRouteResult = this.$router.resolve(`/cac/app/${name.replace('_', '/')}`);

                if (findRouteResult.matched.length === 0) {
                    console.error(`未找到路由：${`/cac/app/${name.replace('_', '/')}`}`);
                    this.$router.push(`/cac/app/empty`);
                    return;
                }
            }

            // 记录当前打开的应用
            this.activeApp = findApp;
            this.activeAppCacheType = findApp.cache_type;

            // 找到父级路由
            if (findApp.start_url && findApp.start_url.length > 0) {
            }
            else if (findApp.name === findApp.app_comp_name) {
            }
            else {
                const parentApp = this.findAppByName(findApp.app_comp_name);
                this.activeAppCacheType = parentApp.cache_type;
            }

            if (debug) console.log(`打开应用（name: ${this.activeApp.name}, cacheType: ${this.activeAppCacheType ?? ''}）：`, findApp);

            // 记录打开历史
            this.openMenuHistory.push(name);

            // 显示或激活Tab
            let isFirstOpenInTabBar = true; // 是否为首次打开
            {
                let findTab = false;
                for (const openMenu of this.openMenus) {
                    if (openMenu.name === name) {
                        findTab = true;
                        isFirstOpenInTabBar = false;
                        break;
                    }
                }
                if (!findTab) {
                    this.openMenus.push({
                        type: 'item',
                        closeable: true,
                        ...findApp
                    });
                }
                // 更新Tab栏
                this.$refs.indexTabBar0.setActiveTab(name);
                // 更新左栏菜单
                this.$refs.indexLeftMenuBar0.setSelectedByName(name);
            }

            // 显示应用内容
            if (this.activeAppCacheType === 'ica') {
                // 更新内容
                const obj = await waitGetObj(this.$refs, 'indexContentArea0');
                obj.onShowed({
                    name: findApp.name,
                    app_comp_name: findApp.app_comp_name,
                    params: findApp.params,
                    ctx: this.getCtx(),
                })
            }

            // 切换路由
            // 打开子应用
            if (findApp.name.indexOf('_') !== -1) {
                if (debug) console.log(`打开应用（name: ${this.activeApp.name}）-切换路由-打开子应用`);
                const arr = findApp.name.split('_');
                this.$router.push(`/cac/app/${findApp.app_comp_name}/${arr[1]}`);
                // 激活现有Tab
                this.$nextTick(async () => {
                    const findAppCache = await waitGetObj(this.appCache, findApp.name);
                    if (findAppCache && findAppCache.view.onActivated) {
                        findAppCache.view.onActivated({
                            childAppId: arr[1],
                        });
                    }
                })
            }
            // 打开URL应用
            else if (findApp.start_url && findApp.start_url.length > 0) {
                if (debug) console.log(`打开应用（name: ${this.activeApp.name}）-切换路由-打开URL应用`);
                this.$router.push(`/cac/url-app/${findApp.name}`);
                // 激活现有Tab
                this.$nextTick(async () => {
                    const findAppCache = await waitGetObj(this.appCache, findApp.name);
                    if (findAppCache) {
                        if (findAppCache.view instanceof HTMLIFrameElement) {
                            if (findAppCache.view.contentWindow.onActivated) {
                                findAppCache.view.contentWindow.onActivated({
                                    appName: findApp.name,
                                });
                            }
                        }
                        else if (findAppCache.view.onActivated) {
                            findAppCache.view.onActivated({
                                appName: findApp.name,
                            });
                        }
                    }
                })
            }
            // 打开主应用
            else {
                if (debug) console.log(`打开应用（name: ${this.activeApp.name}）-切换路由-打开主应用`);
                this.$router.push(`/cac/app/${findApp.name}`);
                // 激活现有Tab
                this.$nextTick(async () => {
                    const findAppCache = await waitGetObj(this.appCache, findApp.name);
                    if (findAppCache && findAppCache.view.onActivated) {
                        findAppCache.view.onActivated({});
                    }
                })
            }

        },
        handleTabChange(name) {
            this.getCtx().indexMenuTabViewModel.onTabChange(this, name)
        },
        handleRemoveTab(name) {
            this.getCtx().indexMenuTabViewModel.onRemoveTab(this, name)
        },
    }
}