import { sleep } from "../../../code/util/thread-util"

export default {
    methods: {
        async waitLoadDataDone() {
            for (let i = 0; i < 30; i++) {
                if (this.loadDataDone === true) {
                    break;
                }
                else {
                    await sleep(500);
                }
            }
        },
        getLeftMenuByName(name) {
            return this.leftMenus.find(n => n.name === name);
        },
        findAppByName(name) {
            return this.getCtx().$appCenterList.find(a => a.name === name);
        }
    }
}