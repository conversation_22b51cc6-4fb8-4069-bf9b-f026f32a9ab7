<template>
    <el-tabs ref="tabs0" v-model="activeTab" :closable="null" @tab-remove="handleRemoveTab"
        @tab-change="handleTabChange">
        <template :key="openMenu.name" v-for="openMenu in openMenus">
            <el-tab-pane :name="openMenu.name" :closable="openMenu.closeable">
                <template #label>
                    <span style="padding: 0 3px">{{ openMenu.label }}</span>
                </template>
            </el-tab-pane>
        </template>
    </el-tabs>
</template>

<script>
export default {
    name: 'index-tab-bar',
    props: {
        openMenus: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            activeTab: null,
        }
    },
    methods: {
        onShowed() {
        },
        getActiveTab() {
            return this.activeTab;
        },
        setActiveTab(v) {
            // console.log(`【index-tab-bar】setActiveTab: ${v} | ${JSON.stringify(this.openMenus)}`)
            this.activeTab = v;
            // 修正Tab下划线宽度
            this.$nextTick(() => {
                this.$nextTick(() => {
                    const activeTab = this.$refs.tabs0.$el.querySelector('.el-tabs__item.is-active');
                    if (activeTab) {
                        // 样式用减掉的总宽度
                        const changeWidth = 20;
                        const textWidth = activeTab.offsetWidth - changeWidth;

                        const activeBar = this.$el.querySelector('.el-tabs__active-bar');
                        if (activeBar) {
                            activeBar.style.width = `${textWidth}px`;
                        }
                    }
                });
            });
        },
        handleTabChange(name) {
            console.log(`【index-tab-bar】handleTabChange: ${name}`)
            this.$emit('on-tab-change', name);
        },
        handleRemoveTab(name) {
            console.log(`【index-tab-bar】handleRemoveTab: ${name}`)
            this.$emit('on-remove-tab', name);
        }
    }
}
</script>