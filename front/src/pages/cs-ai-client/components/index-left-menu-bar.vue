<template>
  <div class="left-menu-bar">
    <draggable v-model="menuItems" :animation="200" @change="handleDragChange" :move="checkMove">
      <template v-for="item in menuItems">
        <div v-if="item.type == 'menu-item'" :class="{ menu: true, menu_selected: isSelected(item) }"
          @click="handleMenuItemClick(item)">
          <div style="width: 15px;">&nbsp;</div>
          <div class="body">
            <div class="icon" style="width: 30px;text-align: center;">
              <img :src="getIcon(item)" alt="" />
              <!-- <i :class="getIcon(item)" style="vertical-align: middle;"></i> -->
            </div>
            <div style="flex-grow: 1;">
              <el-tooltip :content="item.label" :disabled="!isTextOverflowed" placement="top">
                <div class="label" :style="`width:${ui.labelMaxWidth}px;`" @mouseenter="handleMouseEnter">
                  <span>{{ item.label }}</span>
                </div>
              </el-tooltip>
            </div>
            <div class="drag-handle" v-if="item.name !== 'app-center'">
              <img src="../assets/img/tuodong.svg" alt="拖动" />
            </div>
            <div v-else style="width: 24px;margin-right: 8px;">
              &nbsp;
            </div>
            <div class="style-block-top_0" v-if="isSelected(item)">&nbsp;</div>
            <div class="style-block-top_1" v-if="isSelected(item)">&nbsp;</div>
            <div class="style-block-bottom_0" v-if="isSelected(item)">&nbsp;</div>
            <div class="style-block-bottom_1" v-if="isSelected(item)">&nbsp;</div>
          </div>
        </div>
      </template>
    </draggable>
  </div>
</template>

<script>
import { toRaw } from 'vue'
import { VueDraggableNext } from 'vue-draggable-next'
import appConfig from "../../../config/app-config.js";

export default {
  name: "index-left-menu-bar",
  components: {
    draggable: VueDraggableNext
  },
  props: {
    // modelValue: Object
  },
  data() {
    return {
      debug: true,
      isTextOverflowed: false,
      selectedMenuItemName: null,
      menuItems: [
        // {
        //   type: 'menu-item',
        //   label: '应用中心',
        //   name: 'app-center',
        //   app_comp_name: 'app-center',
        //   icon: 'el-icon-menu',
        //   closeable: true
        // }
      ],
      ui: {
        labelMaxWidth: 80
      }
    }
  },
  methods: {
    onShowed(pars) {
      this.menuItems = pars.items;

      this.$nextTick(() => {
        if (this.$route.path) {
          if (this.$route.path.startsWith('/cac/app/')) {
            let str = this.$route.path.replace('/cac/app/', '');
            this.setSelectedByName(str);
          }
        }
      })
    },
    isSelected(item) {
      return item.name === this.selectedMenuItemName;
    },
    getIcon(item) {
      if (item.icon) {
        if (item.icon.startsWith('~/')) {
          return item.icon.replace('~/', `${appConfig.backRootUrl}`)
        }
      }
      return `./assets/imgs/app/${item.app_comp_name}.svg`;
      // return new URL(`../assets/img/${item.app_comp_name}.svg`, import.meta.url).href
    },
    setSelectedByName(name) {
      // if (this.debug) console.log(`【index-left-menu-bar】setSelectedByName：${name}`)
      let find = false;
      for (const item of this.menuItems) {
        if (item.name === name) {
          find = true;
          break;
        }
      }

      if (find) {
        this.selectedMenuItemName = name;
      }
      else {
        this.selectedMenuItemName = null;
      }
    },
    handleMouseEnter(event) {
      this.isTextOverflowed = event.target.scrollWidth > event.target.clientWidth;
    },
    handleMenuItemClick(item) {
      this.selectedMenuItemName = item.name;
      this.$emit('on-open-menu', {
        name: item.name,
      })
    },
    handleDragChange(evt) {
      // if (this.debug) console.log('【index-left-menu-bar】handleDragChange: ', `oldIndex: ${evt.moved.oldIndex}, newIndex: ${evt.moved.newIndex}`, evt)

      // vue-draggable-next已经自动更新了menuItems数组，直接使用即可
      this.$nextTick(() => {
        const menuItems = toRaw(this.menuItems)
        // if (this.debug) console.log('【index-left-menu-bar】handleDragChange: 菜单顺序已更新', menuItems)
        this.$emit('on-menu-order-change', menuItems)
      })
    },
    checkMove(evt) {
      // if (this.debug) console.log('【index-left-menu-bar】checkMove: ', evt)
      // 不允许拖动到 app-center 之前
      const draggedItem = evt.draggedContext.element
      const relatedItem = evt.relatedContext.element

      if (relatedItem && relatedItem.name === 'app-center') {
        return false
      }

      // 不允许拖动 app-center
      if (draggedItem.name === 'app-center') {
        return false
      }

      return true
    },
  },
  mounted() {
    const self = this;
    // setInterval(() => {
    //   console.log(self.menuItems);
    // }, 1000)
  }
}
</script>

<style scoped lang="less">
.left-menu-bar {

  .menu {
    display: flex;
    flex-direction: row;
    height: 40px;
    color: silver;
    cursor: pointer;
    font-size: 15px;
    font-family: "Microsoft Himalaya";

    .label {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .body {
      display: flex;
      align-items: center;
      flex-grow: 1;
      flex-direction: row;
      position: relative;

      .icon {

        img {
          width: 15px;
          vertical-align: middle;
          // filter: invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
        }
      }
    }

    .drag-handle {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: move;
      margin-right: 8px;
      opacity: 0;
      transition: opacity 0.3s;

      img {
        width: 16px;
        height: 16px;
      }
    }

    &:hover {
      .drag-handle {
        opacity: 1;
      }
    }

    .style-block-top_0 {
      position: absolute;
      top: -10px;
      right: 0;
      width: 10px;
      height: 10px;
      background-color: #F1F5F8;
    }

    .style-block-top_1 {
      position: absolute;
      top: -10px;
      right: 0;
      width: 10px;
      height: 10px;
      background-color: #1D3FAA;
      -webkit-border-radius: 0 0 16px 0;
      -moz-border-radius: 0 0 16px 0;
    }

    .style-block-bottom_0 {
      position: absolute;
      bottom: -10px;
      right: 0;
      width: 10px;
      height: 10px;
      background-color: #F1F5F8;
    }

    .style-block-bottom_1 {
      position: absolute;
      bottom: -10px;
      right: 0;
      width: 10px;
      height: 10px;
      background-color: #1D3FAA;
      -webkit-border-radius: 0 16px 0 0;
      -moz-border-radius: 0 16px 0 0;
    }
  }

  .menu_selected {

    .body {
      color: black;
      background-color: #F1F5F8;
      -webkit-border-radius: 12px 0 0 12px;
      -moz-border-radius: 12px 0 0 12px;
    }
  }
}
</style>