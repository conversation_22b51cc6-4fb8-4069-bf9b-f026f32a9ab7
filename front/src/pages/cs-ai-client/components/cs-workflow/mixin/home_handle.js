import {ElMessage} from "element-plus";

export default {
  methods: {
    // 工具栏查询变动
    handleToolBarQueryChange(e) {
    },
    // 执行
    handleExecuteClick(e) {
      const self = this;
      this.$confirm(`确定要执行吗？`, '确认提示', { type: 'warning' }).then(() => {
        // 点击确认
        self.$emit('on-execute', {
          designData: self.buildDesignData(),
          runtimeData: self.buildRuntimeData(),
        });
      }).catch(() => {
        // 点击取消
      });
    },
    // 保存
    handleSaveClick(e) {
      this.$emit('on-save', {
        from: e.from,
        designData: this.buildDesignData(),
        runtimeData: this.buildRuntimeData(),
      });
    },
    // 导入设计数据
    async handleImportDesignDataClick() {
      const self = this;
      // this.importDialog.visible = true;
      this.$confirm('确定要从剪贴板中导入设计吗？', '确认提示', { type: 'warning' }).then(async () => {
        // 点击确认
        try {
          const text = await navigator.clipboard.readText();
          const data = JSON.parse(text);
          if (typeof data !== 'object' || !data) throw new Error('剪贴板内容不是有效的JSON');
          if (data.graphData == null) {
            ElMessage.error('导入的设计无效');
          }
          else {
            self.setDesignData(data);
            ElMessage.success('导入设计完毕');
          }
        } catch (e) {
          ElMessage.error('导入失败: ' + (e.message || e));
        }
      }).catch(() => {
        // 点击取消
      });
    },
    // 导出设计数据
    handleExportDesignDataClick() {
      const designData = this.buildDesignData();
      navigator.clipboard.writeText(JSON.stringify(designData))
        .then(() => {
          this.$message.success('设计数据已复制到剪贴板');
        })
        .catch(err => {
          console.error('复制到剪贴板失败:', err);
          this.$message.error('复制到剪贴板失败');
        });
    },
    // 导出运行数据
    handleExportRuntimeDataClick() {
      const runtimeData = this.buildRuntimeData();
      navigator.clipboard.writeText(JSON.stringify(runtimeData))
        .then(() => {
          this.$message.success('运行数据已复制到剪贴板');
        })
        .catch(err => {
          console.error('复制到剪贴板失败:', err);
          this.$message.error('复制到剪贴板失败');
        });
    },
    // 当节点选中时触发
    handleNodeClick(e) {
      this.resetSelected();

      if (this.onlySee === true) {
        return;
      }

      this.selectedNode = e;
      for (const field of this.getNodeFields()) {
        field.value = null;

        if (field.name === 'label') {
          field.value = e.data.text.value;
        }
        else {
          if (this.nodeFormMap[this.selectedNode.data.id]) {
            if (this.nodeFormMap[this.selectedNode.data.id][field.name]) {
              field.value = this.nodeFormMap[this.selectedNode.data.id][field.name];
            }
          }
        }
      }
    },
    // 点击复制节点按钮
    handleCopyNodeClick() {
      if (this.selectedNode) {
        const nodeData = {
          type: this.selectedNode.data.type,
          x: this.selectedNode.data.x + 100,  // Offset the copied node position
          y: this.selectedNode.data.y + 100,
          text: this.selectedNode.data.text.value,
          properties: {
            tool: {
              ...this.selectedNode.data.properties.tool
            }
          }
        };
        const t = this.lf.graphModel.addNode(nodeData);

        const old = this.nodeFormMap[this.selectedNode.data.id]
        if (old) {
          this.nodeFormMap[t.id] = JSON.parse(JSON.stringify(old))
        }
      }
    },
    // 点击删除节点按钮
    handleDeleteNodeClick() {
      const self = this;
      if (self.selectedNode) {
        self.$confirm(`确定要删除节点“${self.selectedNode.data.text.value}”吗？`, '确认提示', { type: 'warning' }).then(() => {
          // 点击确认
          self.lf.deleteNode(self.selectedNode.data.id);
          self.selectedNode = null;
        }).catch(() => {
          // 点击取消
        });
      }
    },
    // 点击边
    handleEdgeClick(e) {
      // if (this.debug) console.log('edge click: ', e);
      this.resetSelected();
      this.selectedEdge = e;

      for (const field of this.getEdgeFields()) {
        field.value = null;

        if (this.edgeFormMap[this.selectedEdge.data.id]) {
          if (this.edgeFormMap[this.selectedEdge.data.id][field.name]) {
            field.value = this.edgeFormMap[this.selectedEdge.data.id][field.name];
          }
        }
      }
    },
    // 点击删除边按钮
    handleDeleteEdgeClick() {
      const self = this;
      if (self.selectedEdge) {
        self.$confirm(`确定要删除边吗？`, '确认提示', { type: 'warning' }).then(() => {
          // 点击确认
          self.lf.deleteEdge(self.selectedEdge.data.id);
          self.selectedEdge = null;
        }).catch(() => {
          // 点击取消
        });
      }
    },
    // 当画布被点击
    handleCanvasClick(e) {
      // if (this.debug) console.log('画布点击:', e);
      this.resetSelected();
    },
    // 工作流字段改变
    handleWorkflowFieldChange(field, e) {
      this.workflowForm[field.name] = e;
    },
    // 节点字段改变
    handleNodeFieldChange(field, e) {
      if (this.selectedNode) {
        const nodeId = this.selectedNode.data.id;
        if (field.name === 'label') {
          this.lf.updateText(nodeId, e);
        }

        if (this.nodeFormMap[nodeId] == null) {
          this.nodeFormMap[nodeId] = {};
        }
        this.nodeFormMap[nodeId][field.name] = e;
      }
    },
    // 边字段改变
    handleEdgeFieldChange(field, e) {
      if (this.selectedEdge) {
        const edgeId = this.selectedEdge.data.id;

        if (this.edgeFormMap[edgeId] == null) {
          this.edgeFormMap[edgeId] = {};
        }
        this.edgeFormMap[edgeId][field.name] = e;
      }
    },
  }
}