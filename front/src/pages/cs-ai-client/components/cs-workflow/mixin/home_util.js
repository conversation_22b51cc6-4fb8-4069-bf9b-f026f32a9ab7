import { convertFlowDesignDataToRuntimeData } from "../code/util/flow-design-util.js";
export default {
    methods: {
        buildDesignData() {
            const graphData = this.lf.getGraphData();

            return {
                graphData,
                workflowForm: this.workflowForm,
                nodeFormMap: this.nodeFormMap,
                edgeFormMap: this.edgeFormMap,
            };
        },
        buildRuntimeData() {
            const graphData = this.lf.getGraphData();

            const runtimeData = convertFlowDesignDataToRuntimeData({
                graphData,
                workflowForm: this.workflowForm,
                nodeFormMap: this.nodeFormMap,
                edgeFormMap: this.edgeFormMap,
            }, {
                debug: this.debug,
            });

            return runtimeData;
        },
    }
}