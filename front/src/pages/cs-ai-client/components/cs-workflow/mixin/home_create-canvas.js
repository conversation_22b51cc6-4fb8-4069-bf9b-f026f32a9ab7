import LogicFlow from "@logicflow/core";
import { PolylineEdge, PolylineEdgeModel } from "@logicflow/core";
import "@logicflow/core/dist/index.css";

// *** 引用下面会导致electron中报错 ***
// import { Menu } from "@logicflow/extension";
// import "@logicflow/extension/lib/style/index.css";

import ImageNodeModelCreator from "../code/model/image-node-model";

export default {
    methods: {
        // 创建画布
        createCanvas() {
            const self = this;
            // Register the Menu plugin with custom configuration
            // LogicFlow.use(Menu);

            this.lf = new LogicFlow({
                container: this.$refs.container,
                snapline: false,
                grid: {
                    type: 'dot', // 点状网格
                    size: 20,    // 网格间距
                    visible: true,
                    config: {
                        color: '#d9d9d9'
                    },
                },
                textEdit: false, // 全局禁用文本编辑
                isSilentMode: false,
                // grid: true,
                // plugins: [Menu],
            });
            this.lf.register(ImageNodeModelCreator.create(self));

            const { editConfigModel } = this.lf.graphModel;
            editConfigModel.updateEditConfig({
                nodeTextEdit: false,
                edgeTextEdit: false,
                stopScrollGraph: true, // 禁止鼠标滚动移动画布
                adjustEdgeStartAndEnd: true,
            });

            // 重写线条（修改颜色）
            class MyEdgeView extends PolylineEdge { }
            class MyEdgeModel extends PolylineEdgeModel {
                getEdgeStyle() {
                    const style = super.getEdgeStyle();

                    if (self.edgeFormMap[this.id]) {
                        if (self.edgeFormMap[this.id]['effect']) {
                            const edgeFieldValue = self.edgeFormMap[this.id]['effect'];
                            if (edgeFieldValue === 1) {
                                style.stroke = "#00CC99";
                                style.strokeOpacity = 0.5;
                            }
                            else if (edgeFieldValue === -1) {
                                style.stroke = "#FF3366";
                                style.strokeOpacity = 0.2;
                            }
                        }
                    }

                    return style;
                }
            }
            this.lf.register({
                type: 'polyline',
                view: MyEdgeView,
                model: MyEdgeModel,
            })

            // 注册节点选中事件
            this.lf.on('node:click', this.handleNodeClick);
            this.lf.on('edge:click', this.handleEdgeClick);
            this.lf.on('blank:click', this.handleCanvasClick);

            this.lf.render();
        },

        // 创建节点
        createNode(toolItem, x, y) {
            // 在控制台输出工具数据
            // if (this.debug) console.log('创建节点:', toolItem, '位置:', { x, y });

            this.lf.addNode({
                type: 'image-node',
                x: x,
                y: y,
                text: toolItem.label,
                properties: {
                    tool: {
                        ...toolItem
                    },
                }
            });
        },

    }
}