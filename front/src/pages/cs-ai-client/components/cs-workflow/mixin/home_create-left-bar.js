
export default {
    methods: {
        // 创建左栏
        createLeftBar() {
            // console.log('createLeftBar')
            const list = [
                {
                    label: '开始',
                    type: 'start',
                    icon: `${this.iconDirUrl}node-start.svg`,
                },
                // {
                //     label: '结束',
                //     type: 'end',
                //     icon: `${this.iconDirUrl}node-end.svg`,
                // },
                // {
                //     label: '节点1',
                //     type: 'node1',
                //     icon: `${this.iconDirUrl}node-0.svg`,
                // },
            ];

            if (this.customToolBar) {
                list.push(...this.customToolBar.items);
            }

            this.leftBar.toolBar.items = list;

            // 在下一个事件循环中初始化拖拽功能，确保DOM已经渲染
            this.$nextTick(() => {
                this.initDragEvents();
            });
        },

        // 初始化拖拽事件
        initDragEvents() {
            // console.log('initDragEvents')
            // 获取所有工具栏项
            const toolItems = document.querySelectorAll('.tool-bar .item');
            
            toolItems.forEach((item, index) => {
                // 设置为可拖拽
                item.setAttribute('draggable', 'true');
                
                // 拖拽开始事件
                item.addEventListener('dragstart', (event) => {
                    // 存储被拖拽的工具项索引
                    event.dataTransfer.setData('toolIndex', index);
                    // 设置拖拽效果
                    event.dataTransfer.effectAllowed = 'copy';
                });
            });
            
            // 为画布容器添加放置相关事件
            const container = this.$refs.container;
            
            // 阻止默认行为以允许放置
            container.addEventListener('dragover', (event) => {
                event.preventDefault();
                event.dataTransfer.dropEffect = 'copy';
            });
            
            // 处理放置事件
            container.addEventListener('drop', (event) => {
                event.preventDefault();
                
                // 获取拖拽的工具项索引
                const toolIndex = event.dataTransfer.getData('toolIndex');
                const toolItem = this.leftBar.toolBar.items[toolIndex];
                
                if (toolItem) {
                    // 获取放置位置相对于画布的坐标
                    const containerRect = container.getBoundingClientRect();
                    const x = event.clientX - containerRect.left;
                    const y = event.clientY - containerRect.top;
                    
                    // 创建节点
                    this.createNode(toolItem, x, y);
                }
            });
        },
    }
}