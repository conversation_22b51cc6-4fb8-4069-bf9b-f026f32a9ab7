export default {
    data() {
        return {
            importDialog: {
                visible: false,
                content: '',
            }
        }
    },
    methods: {
        handleImportDesignClick() {
            // if (this.importDialog.content == null || this.importDialog.content.length === 0) {
            //     this.$alert('导入设计内容不能为空', { type: 'error' });
            // }
            // else {
            //     this.setDesignData(JSON.parse(this.importDialog.content));
            //     this.importDialog.visible = false;
            //     this.$message({ message: '导入设计完毕', type: 'success' });
            // }
        },
        handleImportDialogClose() {
            this.importDialog.visible = false;
        },
    }
}