<template>
  <div class="ditto-field">
    <div class="label" :style="{ width: `${labelWidth}px` }">{{ value.label }}：</div>
    <div class="body">
      <template v-if="value.type === 'text'">
        <el-input v-model="value.value" :placeholder="value.placeholder ?? '请输入'" size="small"
                  @change="handleChange" @input="handleInput"
                  :readonly="value.readonly" :disabled="value.disabled"></el-input>
      </template>
      <template v-if="value.type === 'number'">
        <el-input-number v-model="value.value" :placeholder="value.placeholder ?? '请输入'" size="small"
                         @change="handleChange" @input="handleInput"
                         :readonly="value.readonly" :disabled="value.disabled"></el-input-number>
      </template>
      <template v-if="value.type === 'select'">
        <el-select v-model="value.value" :placeholder="value.placeholder ?? '请选择'" size="small" clearable
                   @change="handleChange"
                   style="width: 100%" :disabled="value.disabled">
          <el-option v-for="item in value.items" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </template>
      <template v-if="value.type === 'textarea'">
        <el-input v-model="value.value" :placeholder="value.placeholder ?? '请输入'" size="small" clearable
                  class="cs-wf-ditto-field_textarea" style="max-width: 600px"
                  @change="handleChange" @input="handleInput"
                  :readonly="value.readonly" :disabled="value.disabled">
          <template #append>
            <div @click="handleTextMaxClick" style="cursor: pointer;text-align: center;width:30px;">...</div>
          </template>
        </el-input>
        <!-- <el-input type="textarea" v-model="value.value" placeholder="请输入" size="small" @change="handleChange"
            @input="handleInput" :autosize="{ minRows: 3, maxRows: 8 }" style="width: 100%;"></el-input> -->
      </template>
      <template v-if="value.type === 'textarea-a'">
        <el-input type="textarea" v-model="value.value" :placeholder="value.placeholder ?? '请输入'" size="small" @change="handleChange"
                  :rows="getTextareaRows(value)"
                  @input="handleInput" style="width: 100%;"></el-input>
      </template>
    </div>
    <div style="width: 10px;">&nbsp;</div>
    <el-dialog :custom-class="dialog0.className" :body-class="dialog0.className" :visible.sync="dialog0.visible"
               v-model="dialog0.visible" :title="dialog0.title" :width="dialog0.width" top="30px"
               :before-close="handleDialog0Close" :close-on-click-modal="false" :append-to-body="true">
      <div v-loading="dialog0.loading"
           :style="`display: flex; flex-direction: row; width: 100%; height:${dialog0.height}; border: 1px solid #ddd;`">
        <el-input type="textarea" v-model="value.value" placeholder="请输入" size="small" @change="handleChange"
                  @input="handleInput" :autosize="false"></el-input>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DittoField',
  props: {
    labelWidth: {
      type: Number,
      default: 90
    },
    value: {
      type: Object,
      default: () => {
        return null;
      }
    },
  },
  data() {
    return {
      dialog0: {
        className: 'cs-wf-ditto-field_text-max-dialog',
        visible: false,
        title: null,
        width: '800px',
        height: '500px',
        loading: false
      }
    }
  },
  methods: {
    getTextareaRows(value) {
      let t = value.rows ?? 6;
      return t;
    },
    handleChange(e) {
      this.$emit('on-change', e);
    },
    handleInput(e) {
      this.$emit('on-input', e);
    },
    handleTextMaxClick() {
      this.dialog0.width = document.documentElement.clientWidth - 100 + 'px'
      this.dialog0.height = document.documentElement.clientHeight - 180 + 'px'
      this.dialog0.title = this.value.label;
      this.dialog0.visible = true;
    },
    handleDialog0Close() {
      this.dialog0.visible = false;
    },
  },
  mounted() {
  }
}
</script>

<style lang="less" scoped>
.ditto-field {
  display: flex;
  flex-direction: row;
  align-items: center;
  // justify-content: center;
  font-size: 13px;

  .label {
    text-align: right;
  }

  .body {
    flex-grow: 1;
    width: 0;
  }
}
</style>

<style>
.cs-wf-ditto-field_textarea {
  .el-input-group__append {
    padding-left: 1px;
    padding-right: 1px;
  }
}

.cs-wf-ditto-field_text-max-dialog {
  .el-dialog__body {
    padding-top: 0;
  }

  textarea {
    height: 100%;
    border: 0;
  }
}
</style>