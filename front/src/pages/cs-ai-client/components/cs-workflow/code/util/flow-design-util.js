import {generateRandomId} from "./str-id-util.js";

/**
 *
 * @param flowDesignData
 */
export function convertFlowDesignDataToRuntimeData(flowDesignData, opts = {}) {
    const runtimeData = {
        nodes: [],
        links: [],
    };
    let nodeIdMaps = [];
    const graphData = flowDesignData.graphData;
    const workflowForm = flowDesignData.workflowForm;
    const nodeFormMap = flowDesignData.nodeFormMap;
    const edgeFormMap = flowDesignData.edgeFormMap;

    if (graphData.nodeIdMaps) {
        nodeIdMaps = graphData.nodeIdMaps;
    }

    graphData.nodes.forEach(node => {
        let findNodeId;

        for (const i in nodeIdMaps) {
            if (nodeIdMaps[i].dId === node.id) {
                findNodeId = nodeIdMaps[i].rId;
                break;
            }
        }

        if (!findNodeId) {
            findNodeId = generateRandomId(3);
            nodeIdMaps.push({ dId: node.id, rId: findNodeId });
        }

        const nodeForm = nodeFormMap[node.id];
        const nodePars = {
            ...nodeForm,
        };

        nodePars.label = node.text.value;
        // delete nodePars.label;

        runtimeData.nodes.push({
            id: findNodeId,
            type: node.properties.tool.type,
            pars: nodePars,
        });
    });

    graphData.edges.forEach(edge => {
        let findFromId;
        let findToId;

        for (const i in nodeIdMaps) {
            if (nodeIdMaps[i].dId === edge.sourceNodeId) {
                findFromId = nodeIdMaps[i].rId;
                break;
            }
        }

        for (const i in nodeIdMaps) {
            if (nodeIdMaps[i].dId === edge.targetNodeId) {
                findToId = nodeIdMaps[i].rId;
                break;
            }
        }

        if (!findFromId) {
            findFromId = generateRandomId(3);
        }

        if (!findToId) {
            findToId = generateRandomId(3);
        }

        const edgeForm = edgeFormMap[edge.id];
        const edgePars = {
            ...edgeForm,
        };

        runtimeData.links.push({
            fromId: findFromId,
            toId: findToId,
            pars: edgePars,
        });
    });

    graphData.nodeIdMaps = nodeIdMaps;

    return runtimeData;
}