import { RectNode, RectNodeModel, h } from '@logicflow/core'

export default {
    create(self) {

        class ImageNodeModel extends RectNodeModel {
            initNodeData(data) {
                super.initNodeData(data)
                this.width = 80
                this.height = 80
        
                // 调整标签位置到底部
                data.text = {
                    ...data.text,
                    y: data.y + this.height / 2 + 20
                };
            }
        
            getNodeStyle() {
                const style = super.getNodeStyle()
                style.fill = 'transparent'
                style.stroke = '#1E90FF'
                style.strokeWidth = 1
                return style
            }
        
            // Add custom context menu support
            setAttributes() {
                this.menu = [
                    {
                        text: '删除节点',
                        callback: (node) => {
                            this.graphModel.deleteNode(this.id);
                        }
                    },
                    // {
                    //     text: '编辑文本',
                    //     callback: (node) => {
                    //         // You can implement your own text editing logic here
                    //         const text = prompt('请输入新的文本：', this.text.value);
                    //         if (text !== null) {
                    //             this.text.value = text;
                    //             // Trigger a graph update
                    //             this.graphModel.eventCenter.emit('graph:change');
                    //         }
                    //     }
                    // },
                    {
                        text: '复制节点',
                        callback: (node) => {
                            const nodeData = {
                                type: this.type,
                                x: this.x + 100,  // Offset the copied node position
                                y: this.y + 100,
                                text: this.text.value,
                                properties: {
                                    tool: {
                                        ...this.properties.tool
                                    }
                                }
                            };
                            const t = this.graphModel.addNode(nodeData);

                            const old = self.nodeFormMap[this.id]
                            if (old) {
                                self.nodeFormMap[t.id] = JSON.parse(JSON.stringify(old))
                            }
                        }
                    },
                ]
            }
        }
        
        class ImageNode extends RectNode {
            getShape() {
                const { x, y, width, height } = this.props.model
                const style = this.props.model.getNodeStyle()
        
                return h('g', {}, [
                    h('rect', {
                        ...style,
                        x: x - width / 2,
                        y: y - height / 2,
                        width,
                        height,
                        rx: 5,
                        ry: 5,
                        fill: '#fff'
                    }),
                    h('image', {
                        x: x - width / 2 + 5,
                        y: y - height / 2 + 5,
                        width: width - 10,
                        height: height - 10,
                        href: this.props.model.properties.tool.icon,
                    }),
                    // h('text', {
                    //     x,
                    //     y: y + height / 2 + 20,
                    //     textAnchor: 'middle',
                    //     fontSize: 14,
                    //     fill: '#333'
                    // }, this.props.model.text.value)
                ])
            }
            
            moveText(x, y) {
                super.moveText(x, y + 50)
            }
        }

        return {
            type: 'image-node',
            view: ImageNode,
            model: ImageNodeModel,
        }
    }
}