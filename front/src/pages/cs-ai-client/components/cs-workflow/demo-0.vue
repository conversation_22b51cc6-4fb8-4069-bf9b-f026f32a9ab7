<template>
  <div style="display: flex;flex-direction: row;height: 100%;">
    <left-tool-flow-editor ref="leftToolFlowEditor0" :debug="true" :showExecuteButton="true" :showExportButton="true" :customToolBar="customToolBar"
    :customWorkflowFields="customWorkflowFields"
      :customNodeFieldsMap="customNodeFieldsMap" style="flex-grow: 1;" @on-save="handleSave"></left-tool-flow-editor>
    <div style="width: 500px;display: flex;flex-direction: column;">
      <div style="display: flex;flex-direction: row;flex-grow: 1;">
        <textarea style="flex-grow: 1;" v-model="designData"></textarea>
      </div>
      <div style="display: flex;flex-direction: row;flex-grow: 1;">
        <textarea style="flex-grow: 1;" v-model="runtimeData"></textarea>
      </div>
      <div style="height: 50px;display: flex;flex-direction: row;">
        <button style="flex-grow: 1;" @click="handleLoad">加载</button>
      </div>
    </div>
  </div>
</template>

<script>
import { defineAsyncComponent, ref } from 'vue';
import { waitGetObj } from './code/util/code-util';
import leftToolFlowEditor from './left-tool-flow-editor.vue';

export default {
  props: {
    iconDirUrl: {
      type: String,
      default: './assets/imgs/'
    }
  },
  components: {
    // 'left-tool-flow-editor': defineAsyncComponent(() => import('./left-tool-flow-editor.vue')),
    'left-tool-flow-editor': leftToolFlowEditor,
  },
  data() {
    return {
      customToolBar: {
        items: [
          {
            label: '节点1',
            type: 'node1',
            icon: `${this.iconDirUrl}node-0.svg`,
            summary: 'xxxxx'
          },
          {
            label: '状态',
            type: 'state',
            icon: `${this.iconDirUrl}node-state.svg`,
            summary: '判断状态是否匹配'
          },
        ]
      },
      customWorkflowFields: [
        { name: 'name', label: '名称', type: 'text', value: null },
      ],
      customNodeFieldsMap: {
        'node1': [
          { name: 'label', label: '标签', type: 'text', value: null },
          { name: 'param1', label: '参数1', type: 'text', value: null },
          { name: 'param2', label: '参数2', type: 'select', value: null, items: [{ value: 0, label: '默认' }, { value: 1, label: '一级' }] },
          { name: 'param3', label: '系统提示词', type: 'textarea', value: null },
        ]
      },
      designData: '',
      runtimeData: '',
    }
  },
  methods: {
    async onShowed() {
      const leftToolFlowEditor0 = await waitGetObj(this.$refs, 'leftToolFlowEditor0');
      leftToolFlowEditor0.onShowed();

      this.designData = localStorage.getItem('designData');
      this.runtimeData = localStorage.getItem('runtimeData');

      if (this.designData && this.designData.length > 0) {
        this.$refs.leftToolFlowEditor0.setDesignData(JSON.parse(this.designData));
      }
    },
    handleSave(e) {
      console.log(e.from)
      this.designData = JSON.stringify(e.designData);
      this.runtimeData = JSON.stringify(e.runtimeData);

      localStorage.setItem('designData', this.designData);
      localStorage.setItem('runtimeData', this.runtimeData);
    },
    handleLoad() {
      this.$refs.leftToolFlowEditor0.setDesignData(JSON.parse(this.designData));
    }
  },
  async mounted() {
    this.onShowed();
  },
  updated() {
    this.onShowed();
  }
}
</script>