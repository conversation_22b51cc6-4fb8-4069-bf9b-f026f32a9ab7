<template>
  <div class="page-container">
    <!-- 左栏 -->
    <div class="left-panel">
      <div class="search-box">
        <el-input v-model="leftBar.query.kw" @input="handleToolBarQueryChange" placeholder="请输入关键字" :clearable="true"
          size="small" style="flex-grow: 1;" />
      </div>
      <!-- 左侧工具栏 -->
      <div class="tool-bar">
        <!-- 工具 -->
        <template v-for="item in getToolBarItems()">
          <div class="item" :title="item.summary" :disabled="onlySee === true">
            <div style="width: 10px;">&nbsp;</div>
            <div class="icon-area">
              <img :src="getToolBarIconUrl(item)" />
            </div>
            <div style="width: 20px;">&nbsp;</div>
            <div class="label-area">
              <span>{{ item.label }}</span>
            </div>
          </div>
        </template>
      </div>
      <div>
        <!-- 导入按钮 -->
        <div v-if="showExportButton" class="save-button" style="display: flex;flex-direction: row;">
          <div style="flex-grow: 1;">
            <el-button @click="handleImportDesignDataClick()" size="small" style="width: 90%;">导入设计</el-button>
          </div>
        </div>
        <!-- 导出按钮 -->
        <div v-if="showExportButton" class="save-button" style="display: flex;flex-direction: row;">
          <div style="flex-grow: 1;">
            <el-button @click="handleExportDesignDataClick()" size="small" style="width: 90%;">导出设计</el-button>
          </div>
          <div style="flex-grow: 1;">
            <el-button @click="handleExportRuntimeDataClick()" size="small" style="width: 90%;">导出运行</el-button>
          </div>
        </div>
        <!-- 保存按钮 -->
        <div class="save-button" v-if="showExecuteButton" style="">
          <div style="width: 3px;">&nbsp;</div>
          <div style="flex-grow: 1;display: flex;flex-basis: content;">
            <el-button type="success" @click="handleExecuteClick">执行</el-button>
          </div>
          <div style="flex-grow: 5;display: flex;flex-basis: content;">
            <el-button type="primary" @click="handleSaveClick({ from: 'btn' })" :disabled="onlySee">保存</el-button>
          </div>
          <div style="width: 3px;">&nbsp;</div>
        </div>
        <div class="save-button" v-else>
          <el-button type="primary" @click="handleSaveClick({ from: 'btn' })" :disabled="onlySee">保存</el-button>
        </div>
      </div>
    </div>
    <!-- 画布 -->
    <div class="container" ref="container"></div>
    <!-- 右栏 -->
    <div class="right-panel" :style="{ width: `${rightBarWidth}px` }">
      <!-- 工作流信息 -->
      <div class="group-bar" style="flex-grow: 1;height: 0;">
        <div class="header">工作流信息</div>
        <div class="body">
          <div style="height: 10px;">&nbsp;</div>
          <template v-for="field in getWorkflowFields()">
            <ditto-field :value="field" @on-change="handleWorkflowFieldChange(field, $event)"
              @on-input="handleWorkflowFieldChange(field, $event)"></ditto-field>
            <div style="height: 10px;">&nbsp;</div>
          </template>
        </div>
      </div>
      <!-- 节点信息 -->
      <div class="group-bar" style="flex-grow: 2;height: 0;" v-if="selectedNode != null">
        <div class="header">节点信息</div>
        <div class="body">
          <div style="height: 10px;">&nbsp;</div>
          <template v-for="field in getNodeFields()">
            <ditto-field :value="field" @on-change="handleNodeFieldChange(field, $event)"
              @on-input="handleNodeFieldChange(field, $event)"></ditto-field>
            <div style="height: 10px;">&nbsp;</div>
          </template>
        </div>
      </div>
      <!-- 边信息 -->
      <div class="group-bar" style="flex-grow: 2;height: 0;" v-else-if="selectedEdge != null">
        <div class="header">边信息</div>
        <div class="body">
          <div style="height: 10px;">&nbsp;</div>
          <template v-for="field in getEdgeFields()">
            <ditto-field :value="field" :rightBarLabelWidth="rightBarLabelWidth"
              @on-change="handleEdgeFieldChange(field, $event)"
              @on-input="handleEdgeFieldChange(field, $event)"></ditto-field>
            <div style="height: 10px;">&nbsp;</div>
          </template>
        </div>
      </div>
      <!-- 节点功能 -->
      <div class="group-bar" style="height: 80px;" v-if="selectedNode != null">
        <div class="header">节点功能</div>
        <div class="body">
          <div class="button-row">
            <el-button size="small" @click="handleCopyNodeClick">复制</el-button>
            <el-button type="danger" size="small" @click="handleDeleteNodeClick">删除</el-button>
          </div>
        </div>
      </div>
      <!-- 边功能 -->
      <div class="group-bar" style="height: 80px;" v-else-if="selectedEdge != null">
        <div class="header">边功能</div>
        <div class="body">
          <div class="button-row">
            <el-button type="danger" size="small" @click="handleDeleteEdgeClick">删除</el-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 导入弹框 -->
    <el-dialog v-model="importDialog.visible" title="导入设计" width="500" :before-close="handleImportDialogClose">
      <div>
        <el-input v-model="importDialog.content" style="width: 100%;" :rows="8" type="textarea" placeholder="Please input" />
      </div>
      <div style="height: 12px;">&nbsp;</div>
      <div style="text-align: center;">
        <el-button type="primary" @click="handleImportDesignClick">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 导入样式
import "@logicflow/core/dist/index.css";
import home_createLogicFlow from "./mixin/home_create-canvas";
import home_createLeftBar from "./mixin/home_create-left-bar";
import home_handle from "./mixin/home_handle";
import home_import from "./mixin/home_import";

import dittoField from "./components/ditto-field.vue";
import home_util from "./mixin/home_util";

export default {
  mixins: [home_createLogicFlow, home_createLeftBar, home_handle, home_util, home_import],
  props: {
    debug: {
      type: Boolean,
      default: false,
    },
    iconDirUrl: {
      type: String,
      default: () => {
        return './assets/imgs/';
      }
    },
    // 显示执行按钮
    showExecuteButton: {
      type: Boolean,
      default: false,
    },
    // 显示导出按钮
    showExportButton: {
      type: Boolean,
      default: false,
    },
    customToolBar: {
      type: Object,
      default: () => { },
    },
    customWorkflowFields: {
      type: Array,
      default: () => [],
    },
    customNodeFieldsMap: {
      type: Object,
      default: () => { },
    },

    rightBarWidth: {
      type: Number,
      default: 300,
    },
    rightBarLabelWidth: {
      type: Number,
      default: 80,
    },
  },
  components: {
    'ditto-field': dittoField
  },
  data() {
    return {
      lf: null,
      initDone: false,

      leftBar: {
        query: {
          kw: '',
        },
        toolBar: {
          items: [
          ],
        }
      },
      rightBar: {
      },
      selectedNode: null,
      selectedEdge: null,
      workflowFields: [
        { name: 'name', label: '名称', type: 'text', value: null },
      ],
      nodeFieldsMap: {
        // 'node1': [
        //   { name: 'label', label: '标签', type: 'text', value: null },
        //   { name: 'param1', label: '参数1', type: 'text', value: null },
        // ]
      },
      edgeFieldsMap: {
        'default': [
          { name: 'effect', label: '影响', type: 'select', value: null, items: [{ label: '积极', value: 1 }, { label: '消极', value: -1 }] },
        ]
      },
      workflowForm: {
      },
      nodeFormMap: {
      },
      edgeFormMap: {
      },

      onlySee: null,
    }
  },
  methods: {
    onShowed(pars = {}) {
      if (pars.workflowForm) {
        this.workflowForm = pars.workflowForm;
      }
      if (pars.nodeFormMap) {
        this.nodeFormMap = pars.nodeFormMap;
      }

      if (this.customWorkflowFields) {
        this.workflowFields = this.customWorkflowFields;
      }
      if (this.customNodeFieldsMap) {
        this.nodeFieldsMap = this.customNodeFieldsMap;
      }

      if (!this.initDone) {
        this.initDone = true;
        this.createCanvas();
        this.createLeftBar();
      }
    },
    setOnlySee(val) {
      this.onlySee = val;
    },
    resetSelected() {
      this.selectedNode = null;
      this.selectedEdge = null;
    },
    // 由外部设置设计数据
    setDesignData(designData) {
      if (designData) {
        // ...
        if (designData.workflowForm) {
          this.workflowForm = designData.workflowForm;

          for (const field of this.workflowFields) {
            field.value = this.workflowForm[field.name];
          }
        }
        // ...
        if (designData.nodeFormMap) {
          this.nodeFormMap = designData.nodeFormMap;
        }
        // ...
        if (designData.edgeFormMap) {
          this.edgeFormMap = designData.edgeFormMap;
        }
        // ...
        if (designData.graphData) {
          this.lf.render(designData.graphData);
        }
      }
    },
    getToolBarIconUrl(item) {
      return `${item.icon}`;//${import.meta.env.BASE_URL}
    },
    getWorkflowFields() {
      return this.workflowFields;
    },
    getNodeFields() {
      if (this.selectedNode) {
        if (this.selectedNode.data.properties && this.selectedNode.data.properties.tool) {
          const fields = this.nodeFieldsMap[this.selectedNode.data.properties.tool.type];
          if (fields) {
            return fields;
          }
        }

        const newFields = [
          { name: 'label', label: '标签', type: 'text', value: null },
        ];

        // 兼容vue2和3
        if (this.$set) {
          this.$set(this.nodeFieldsMap, this.selectedNode.data.properties.tool.type, newFields);
        }
        else {
          this.nodeFieldsMap[this.selectedNode.data.properties.tool.type] = newFields;
        }

        return newFields;
      }

      return [];
    },
    getEdgeFields() {
      if (this.selectedEdge) {
        const fields = this.edgeFieldsMap['default'];
        if (fields) {
          return fields;
        }
      }
      return [];
    },
    getToolBarItems() {
      if (this.leftBar.query.kw && this.leftBar.query.kw.length > 0) {
        return this.leftBar.toolBar.items.filter(item => item.label.includes(this.leftBar.query.kw));
      }
      return this.leftBar.toolBar.items;
    },
  },
  mounted() {
    const self = this;
    window.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        self.handleSaveClick({
          from: 'ctrlS'
        });
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.handleKeyDown);
  },
}
</script>

<style scoped lang="less">
.page-container {
  display: flex;
  flex-direction: row;
  //width: 100%;
  //height: 100vh;
  //position: absolute;
  //top: 0;
  //left: 0;
}

.left-panel {
  width: 250px;
  height: 100%;
  border-right: 1px solid #ddd;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .search-box {
    padding: 10px;
    border-bottom: 1px solid #ddd;
    display: flex;
    flex-direction: row;

    input {
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      outline: none;

      &:focus {
        border-color: #1890ff;
      }
    }
  }

  .tool-bar {
    flex: 1;
    overflow-y: auto;
    padding: 10px;

    .item {
      display: flex;
      flex-direction: row;
      cursor: pointer;
      padding: 10px 5px;

      &:hover {
        background-color: rgba(64, 158, 255, 0.1);
      }

      .icon-area {

        img {
          width: 30px;
          height: 30px;
        }
      }

      .label-area {
        flex-grow: 1;

        span {
          line-height: 30px;
        }
      }
    }

    .item[disabled=true] {
      pointer-events: none; /* 禁止鼠标交互，包括拖动 */
      cursor: not-allowed; /* 显示禁止拖动的光标 */
      user-select: none; /* 禁止文本选择 */
      opacity: 0.6; /* 可选：降低透明度以示禁用状态 */
    }
  }

  .save-button {
    width: 100%;
    text-align: center;
    display: flex;
    flex-direction: row;

    .el-button {
      flex-grow: 1;
      margin: 5px 5px 5px 5px;
    }
  }
}

.right-panel {
  height: 100%;
  border-left: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .group-bar {
    display: flex;
    flex-direction: column;

    .header {
      font-size: 13px;
      line-height: 30px;
      padding-left: 12px;
      font-weight: bold;
      background-color: #f8f8f8;
    }

    .body {
      flex-grow: 1;
      height: 0;
      overflow-y: auto;

      .button-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        height: 100%;
      }
    }
  }
}

.container {
  flex-grow: 1;
  width: 0;
  height: 100%;
  position: relative;
}
</style>