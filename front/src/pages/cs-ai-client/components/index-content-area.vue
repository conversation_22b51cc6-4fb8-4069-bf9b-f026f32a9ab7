<template>
  <div class="index-content-area" :update-code="updateCode" style="height: 100%;">
    <template v-for="appComp in cacheAppComps" :key="appComp.name">
      <template v-if="appComp.app_comp_name === 'home'">
        <index-home v-show="activeName === appComp.name" :open-app-name="appComp.name"
        @on-ready="handleCompReady(appComp)"></index-home>
      </template>
      <template v-else-if="appComp.app_comp_name === 'app-center'">
        <app-center-list v-show="activeName === appComp.name" :open-app-name="appComp.name"
                         :ref="el => { if (el) refs[appComp.name] = el }" @on-ready="handleCompReady(appComp)" @on-open-app="handleOpenApp"></app-center-list>
      </template>
<!--      <template v-else-if="appComp.app_comp_name === 'cs-mini-agent'">-->
<!--        <cs-mini-agent v-show="activeName === appComp.name" :open-app-name="appComp.name"-->
<!--                       :ref="el => { if (el) refs[appComp.name] = el }" :auto-show="false" @on-ready="handleCompReady(appComp)"></cs-mini-agent>-->
<!--      </template>-->
      <template v-else-if="appComp.app_comp_name === 'ai-multi-chat'">
        <ai-multi-chat v-show="activeName === appComp.name" :open-app-name="appComp.name"
                       :ref="el => { if (el) refs[appComp.name] = el }" @on-ready="handleCompReady(appComp)"></ai-multi-chat>
      </template>
      <template v-else-if="appComp.rawData.start_url && appComp.rawData.start_url.length > 0">
        <iframe v-show="activeName === appComp.name"
                :ref="el => { if (el) refs[appComp.name] = el }"
                :src="getAppUrl(appComp)" @load="handleIframeLoad(appComp)"
                style="width:100%;height:100%;border:0;background-color: transparent;" />
      </template>
    </template>
  </div>
</template>

<script>
import appConfig from "../../../config/app-config.js";
import IndexHome from "./index-home.vue";
import AppCenterList from "../app-comps/app-center-list.vue";
import { defineAsyncComponent, ref } from 'vue';
import {waitGetObj} from "../../../code/util/code-util.js";

let ctx;
export default {
  name: 'index-content-area',
  components: {
    AppCenterList,
    IndexHome,
    // 'cs-mini-agent': defineAsyncComponent(() => import('../../cs-mini-agent/index.vue')),
    'ai-multi-chat': defineAsyncComponent(() => import('../app-comps/ai-multi-chat.vue'))
  },
  data() {
    return {
      debug: true,
      updateCode: 0,
      activeName: null,
      activeAppCompName: null,
      activeParams: null,
      // compCache: {},
      cacheAppComps: [
        // { name: 'cs-mini-agent', app_comp_name: '' }
      ],
      refs: ref({})
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    onShowed(pars) {
      // if (this.debug) console.log('index-content-area.showed: ', pars.name, pars.app_comp_name, pars.params)
      const name = pars.name;
      let app_comp_name = pars.app_comp_name || name;
      const params = pars.params;
      ctx = pars.ctx;

      if (this.cacheAppComps.find((n) => { return n.name === name; }) == null) {
        const findApp = ctx.cacIndexView.findAppByName(name);
        this.cacheAppComps.push({
          name, app_comp_name, rawData: findApp,
        });
      }

      this.activeName = name;
      this.activeAppCompName = app_comp_name;
      this.activeParams = params;

      // this.updateCode++;
    },
    getAppUrl(cacheAppComp) {
      if (cacheAppComp.rawData.start_url_dev && cacheAppComp.rawData.start_url_dev.length > 0) {
        if (process.env.NODE_ENV === 'production') {}
        else {
          return cacheAppComp.rawData.start_url_dev;
        }
      }
      if (cacheAppComp.rawData.start_url.startsWith('~/')) {
        return cacheAppComp.rawData.start_url.replace('~/', `${appConfig.backRootUrl}`);
      }
    },
    destroyComp(name) {
      for (const i in this.cacheAppComps) {
        const cacheAppComp = this.cacheAppComps[i];
        if (cacheAppComp.name === name) {
          this.cacheAppComps.splice(parseInt(i), 1);
          break;
        }
      }

      this.activeName = null;
      // this.updateCode++;
    },
    handleCompReady(comp) {
      // if (this.debug) console.log('index-content-area.handleCompReady: ', comp.name, this.activeParams)
      
      // let morePars = {
      //   ...this.activeParams
      // }
      // if (comp.name === 'cs-mini-agent') {
      //   morePars.showLeftBottomBar = false
      // }

      // this.refs[comp.name].onShowed({
      //   ctx: ctx, openerName: 'cs-ai-client', ...morePars
      // });
    },
    handleOpenApp(e) {
      this.$emit('on-open-menu', e)
    },
    async handleIframeLoad(appComp) {
      const iframeEl = this.refs[appComp.name];
      // console.log(`iframe load: `, appComp)
      // console.log(iframeEl.contentWindow)
      const cacIndexView = await waitGetObj(this.getCtx(), 'cacIndexView');
      // 注册
      await cacIndexView.registerAppStartPage({
        name: 'ai-multi-chat',
        openAppName: appComp.name,
        view: iframeEl,
      });
    }
  }
}
</script>

<style scoped lang="less">

</style>