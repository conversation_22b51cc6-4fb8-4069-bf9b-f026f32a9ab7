<template>
  <div class="app-center-list app-start-view_card" :update-code="updateCode" v-loading="loading">
    <div style="display: flex;width: 100%;height: 0;flex-grow: 1;">
      <div class="app-item-list">
        <template v-for="item in list.items">
          <div class="app-item">
            <div style="width: 10px;">&nbsp;</div>
            <div class="icon" @click="handleClick(item)">
              <img :src="getIcon(item)" alt="" />
              <!-- <i :class="item.icon"></i> -->
            </div>
            <div style="width: 15px;">&nbsp;</div>
            <div class="right-area" @click="handleClick(item)">
              <div style="height: 10px;">&nbsp;</div>
              <div class="label">{{ item.label }}</div>
              <div class="summary" :title="item.summary">{{ item.summary }}</div>
            </div>
            <div style="width: 5px;">&nbsp;</div>
            <div class="action">
              <div style="height: 5px;">&nbsp;</div>
              <div style="flex-grow: 1;">
                <input type="button" value="显示" v-if="!item.enabled" @click="handleEnable(item)" />
                <input type="button" value="隐藏" v-if="item.enabled" @click="handleDisable(item)" />
              </div>
              <div style="flex-grow: 1;">
                <input type="button" value="配置" @click="handleSetting(item)" />
              </div>
              <div style="height: 5px;">&nbsp;</div>
            </div>
            <div style="width: 20px;">&nbsp;</div>
          </div>
        </template>
        <div style="clear: both;"></div>
      </div>
    </div>
    <div style="height: 10px;">&nbsp;</div>
  </div>
</template>

<script>
import { waitGetObj } from '../../../code/util/code-util'
import './assets/css/app-start-view_card.less'
import appConfig from "../../../config/app-config.js";
// import Vue from "vue";

export default {
  name: "app-center-list",
  props: {
    openAppName: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      debug: false,
      updateCode: 0,
      list: {
        // [{ id, name, label, icon, summary, app_comp_name, enabled }]
        items: []
      },
      loading: false,
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    async onShowed() {
      // console.log(`【app-center-list】 onShowed ...`)
      await this.loadData()
    },
    async onActivated(pars = {}) {
    },
    getIcon(item) {
      if (item.icon && item.icon.length > 0) {
        if (item.icon.startsWith('~/')) {
          return item.icon.replace('~/', `${appConfig.backRootUrl}`)
        }
        if (item.start_url && item.start_url) {
          let url = item.start_url.substring(0, item.start_url.lastIndexOf('/') + 1);
          url = url.replace('~/', `${appConfig.backRootUrl}`)
          return `${url}${item.icon}`;
        }
      }
      return `./assets/imgs/app/${item.app_comp_name}.svg`;
      // return new URL(`../assets/img/${item.app_comp_name}.svg`, import.meta.url).href
    },
    async loadData() {
      this.loading = true

      // 常驻应用
      const residentApp = await this.getCtx().residentAppService.loadData()

      this.list.items = this.getCtx().$appCenterList.filter((n) => {
        if (n.hideInAppCenter) return false
        return true
      }).map((n) => {
        let enabled = false;

        const findApp = residentApp.items.find(a => a.id === n.id)
        if (findApp) {
          enabled = true;
        }

        return {
          ...n,
          enabled: enabled
        }
      })

      // // 测试数据
      // for (let i = 0; i < 99; i++) {
      //   this.list.items.push({
      //     ...this.list.items[0]
      //   })
      // }

      this.updateCode++
      this.loading = false
    },
    handleClick(appItem) {
      this.getCtx().cacIndexView.openApp(appItem.name)
      this.$emit('on-open-app', {
        name: appItem.name,
      })
    },
    // 启用
    async handleEnable(appItem) {
      if (this.debug) console.log('启用：', appItem)
      appItem.enabled = true
      this.updateCode++

      this.loading = true
      await this.getCtx().residentAppService.enableApp(appItem)
      this.loading = false
      this.getCtx().cacIndexView.loadLeftMenus()
    },
    // 停用
    async handleDisable(appItem) {
      if (this.debug) console.log('停用：', appItem)
      appItem.enabled = false
      this.updateCode++

      this.loading = true
      await this.getCtx().residentAppService.disableApp(appItem)
      this.loading = false
      this.getCtx().cacIndexView.loadLeftMenus()
    },
    // 配置
    handleSetting(appItem) {
      if (this.debug) console.log('配置：', appItem)
    }
  },
  async mounted() {
    const self = this;
    // console.log(`【app-center-list】 mounted ...`)
    self.$emit('on-ready');

    const cacIndexView = await waitGetObj(self.getCtx(), 'cacIndexView');
    // 注册
    await cacIndexView.registerAppStartPage({
      name: 'app-center',
      openAppName: self.openAppName,
      view: self,
    });
    self.onShowed();
  }
}
</script>

<style scoped lang="less">
.app-center-list {

  .app-item-list {

    .app-item {

      .action {

        input[type="button"] {
          padding: 3px 12px;
          border-radius: 4px;
          border: none;
          cursor: pointer;
          font-size: 13px;
          transition: all 0.3s ease;

          &[value="显示"] {
            background-color: #409EFF;
            color: white;

            &:hover {
              background-color: #66b1ff;
            }
          }

          &[value="隐藏"] {
            background-color: #F56C6C;
            color: white;

            &:hover {
              background-color: #f78989;
            }
          }

          &[value="配置"] {
            background-color: #909399;
            color: white;

            &:hover {
              background-color: #a6a9ad;
            }
          }
        }
      }
    }
  }
}
</style>