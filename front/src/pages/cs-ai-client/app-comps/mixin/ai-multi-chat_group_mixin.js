export default {
  data() {
    return {
      formMode: 0,
      form: {
        label: '',
        note: ''
      },
      groupDialogVisible: false,
      groupCollectState: {},
    }
  },
  methods: {
    async loadGroupData() {
      const self = this

      this.loading = true

      this.data = await this.getCtx().aiMultiChatService.loadData()
      this.data.items.forEach(group => {
        group.selected = false
        if (group.items) {
          group.items = group.items.filter(n => n.label != null && n.label.length > 0)
        }

        self.groupCollectState[group.id] = self.isGroupCollectSelected(group)
      })

      this.loading = false

      this.updateCode++
    },
    getSelectedGroup() {
      const selected = this.data.items.find(i => i.selected)
      return selected
    },
    // *** 更新 ***
    showNewForm() {
      const self = this;
      this.groupDialogVisible = true
      this.form = {
        name: '',
        remark: ''
      }
      this.formMode = 0;
    },
    showEditForm(form) {
      this.groupDialogVisible = true
      this.form = {
        ...form
      }
      this.formMode = 1;
    },
    handleSelectGroup(item) {
      this.data.items.forEach(i => {
        i.selected = false
      })
      item.selected = true

      this.updateCode++
    },
    handleSelectGroupById(id) {
      // console.log('handleSelectGroupById |', id, '|', JSON.stringify(this.data.items))
      for (const group of this.data.items) {
        if (group.id === id) {
          this.handleSelectGroup(group)
          break
        }
      }
    },
    async handleGroupSave() {
      if (!this.form.label) {
        this.$message.warning('请输入分组名称')
        return
      }

      this.loading = true
      if (this.formMode === 0) {
        this.form.id = String(Date.now())
        this.data.items.push(this.form)
      }
      else {
        if (this.form.id == null) this.form.id = String(Date.now())
        for (const item of this.data.items) {
          if (item.id === this.form.id) {
            item.label = this.form.label
            item.note = this.form.note
            if (this.form.items == null) this.form.items = []
            item.items = this.form.items
            break
          }
        }
      }

      await this.getCtx().aiMultiChatService.saveData(this.data)
      this.groupDialogVisible = false
      this.loading = false
    },
    // *** 排序 ***
    async handleGroupDragChange(evt) {
      // if (this.debug) console.log('【ai-multi-chat】handleDragChange: ', `oldIndex: ${evt.moved.oldIndex}, newIndex: ${evt.moved.newIndex}`, evt)
      this.loading = true
      await this.getCtx().aiMultiChatService.saveData(this.data)
      this.loading = false
    },
    checkGroupMove(evt) {
      // if (this.debug) console.log('【ai-multi-chat】checkMove: ', evt)
      // 不允许拖动到 app-center 之前
      const draggedItem = evt.draggedContext.element
      const relatedItem = evt.relatedContext.element

      // if (relatedItem && relatedItem.name === 'app-center') {
      //   return false
      // }

      // // 不允许拖动 app-center
      // if (draggedItem.name === 'app-center') {
      //   return false
      // }

      return true
    },
    // *** 收藏 ***
    isGroupCollectSelected(group) {
      const t = this.getCtx().myResidentAppService.isCollect({
        name: `ai-multi-chat_${group.id}`
      })

      return t
    },
    async handleCollectGroup(group) {
      const self = this

      self.loading = true
      if (this.isGroupCollectSelected(group)) {
        // 取消收藏
        await this.getCtx().cacIndexView.discard({
          name: `ai-multi-chat_${group.id}`,
        })
        // const myResidentAppResult = await this.getCtx().myResidentAppService.discard({
        //   name: `ai-multi-chat_${group.id}`,
        // })
        // await this.getCtx().cacIndexView.loadLeftMenus(myResidentAppResult)
      }
      else {
        // 收藏
        await this.getCtx().cacIndexView.collect({
          name: `ai-multi-chat_${group.id}`,
          app_comp_name: 'ai-multi-chat',
          label: group.label,
          icon: null,
          params: { hideLeftBar: true, selectedGroupId: group.id }
        })
        // const myResidentAppResult = await this.getCtx().myResidentAppService.collect({
        //   name: `ai-multi-chat_${group.id}`,
        //   app_comp_name: 'ai-multi-chat',
        //   label: group.label,
        //   icon: null,
        //   params: { hideLeftBar: true, selectedGroupId: group.id }
        // })
        // await this.getCtx().cacIndexView.loadLeftMenus(myResidentAppResult)
      }

      self.groupCollectState[group.id] = self.isGroupCollectSelected(group)

      this.$nextTick(() => {
        // 手动触发更新，强制组件重新渲染
        this.updateCode++
      })
      
      self.loading = false
    },
  }
}