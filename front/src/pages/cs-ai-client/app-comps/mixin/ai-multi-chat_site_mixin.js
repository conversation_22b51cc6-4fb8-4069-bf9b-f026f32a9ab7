
export default {
    data() {
        const defaultSiteForm = {
            id: '',
            label: '',
            url: '',
            width: 800,
            icon: null,
            viewportType: null,
        };

        return {
            defaultSiteForm: defaultSiteForm,
            siteFormMode: 0,
            siteForm: {
                ...defaultSiteForm
            },
            siteDialogVisible: false,

            allAIToolsQuery: {
                ask: null,
                picture: null,
                video: null,
            },
            allAITools: [],
            aiToolSelector0: null,
        }
    },
    methods: {
        handleNewSite() {
            const self = this;

            this.siteFormMode = 0;
            this.siteForm = {
                ...self.defaultSiteForm
            };
            this.siteDialogVisible = true;
        },
        handleEditSite(form) {
            const self = this;

            this.siteFormMode = 1;
            this.siteForm = {
                ...form
            };
            this.siteDialogVisible = true;
        },
        async handleDeleteSite(group, site) {
            for (const i in group.items) {
                if (group.items[i].id === site.id) {
                    group.items.splice(i, 1)

                    this.loading = true
                    await this.getCtx().aiMultiChatService.saveData(this.data)
                    this.loading = false
                    break
                }
            }
        },
        async handleSiteSave() {
            const group = this.getSelectedGroup()
            if (group.items == null) group.items = []

            if (this.siteFormMode === 0) {
                group.items.push({
                    id: String(Date.now()),
                    label: this.siteForm.label,
                    url: this.siteForm.url,
                    width: this.siteForm.width,
                    icon: this.siteForm.icon,
                    viewportType: this.siteForm.viewportType,
                });
            } else {
                const item = group.items.find(i => i.id === this.siteForm.id)
                item.label = this.siteForm.label;
                item.url = this.siteForm.url;
                item.width = this.siteForm.width;
                item.icon = this.siteForm.icon;
                item.viewportType = this.siteForm.viewportType;
            }

            this.loading = true
            await this.getCtx().aiMultiChatService.saveData(this.data)
            this.siteDialogVisible = false
            this.loading = false
        },
        handleSiteDragChange(evt) {
            // console.log('【ai-multi-chat】handleDragChange: ', `oldIndex: ${evt.moved.oldIndex}, newIndex: ${evt.moved.newIndex}`, evt)
        },
        handleAIToolItemClick(tool) {
            this.siteForm.label = tool.name;
            this.siteForm.url = tool.url;
            this.siteForm.icon = tool.icon_url;
        }
    }
}