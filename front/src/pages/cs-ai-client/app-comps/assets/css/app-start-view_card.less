.app-start-view_card {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 100%;

  .app-item-list {
    flex-grow: 1;
    width: 0;
    height: 100%;
    overflow-y: auto;

    .app-item {
      float: left;
      display: flex;
      flex-direction: row;
      cursor: pointer;
      margin-bottom: 15px;
      margin-right: 12px;
      padding: 10px;
      border-radius: 6px;
      transition: all 0.6s ease;
      background-color: rgba(179, 196, 214, 0.1);

      &:hover {
        background-color: rgba(64, 158, 255, 0.1);
      }

      .icon {
        display: flex;
        align-items: center;

        img {
          width: 60px;
          height: 60px;
          filter: invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
        }
      }

      .right-area {

        .label {
          line-height: 30px;
          font-size: 15px;
        }

        .summary {
          color: gray;
          font-size: 13px;
          width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .action {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;

        > div {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        input[type="button"] {
          padding: 3px 12px;
          border-radius: 4px;
          border: none;
          cursor: pointer;
          font-size: 13px;
          transition: all 0.3s ease;

          &[value="执行"] {
            background-color: rgba(36, 161, 63, 0.6);
            color: white;

            &:hover:not(:disabled) {
              background-color: rgba(36, 161, 63);
            }
            
            &:disabled {
              background-color: rgba(36, 161, 63, 0.3);
              cursor: not-allowed;
              opacity: 0.7;
            }
          }

          &[value="编辑"] {
            background-color: rgba(64, 158, 255, 0.6);
            color: white;

            &:hover:not(:disabled) {
              background-color: rgba(64, 158, 255);
            }
            
            &:disabled {
              background-color: rgba(64, 158, 255, 0.3);
              cursor: not-allowed;
              opacity: 0.7;
            }
          }

          &[value="删除"] {
            background-color: rgba(245, 108, 108, 0.6);
            color: white;

            &:hover:not(:disabled) {
              background-color: rgba(245, 108, 108);
            }
            
            &:disabled {
              background-color: rgba(245, 108, 108, 0.3);
              cursor: not-allowed;
              opacity: 0.7;
            }
          }

          &[value="配置"] {
            background-color: rgb(153, 153, 153, 0.6);
            color: white;

            &:hover:not(:disabled) {
              background-color: rgb(153, 153, 153);
            }
            
            &:disabled {
              background-color: rgba(153, 153, 153, 0.3);
              cursor: not-allowed;
              opacity: 0.7;
            }
          }

          &[value="查看"] {
            background-color: rgba(159, 122, 234, 0.6);
            color: white;

            &:hover:not(:disabled) {
              background-color: rgb(159, 122, 234);
            }
            
            &:disabled {
              background-color: rgba(159, 122, 234, 0.3);
              cursor: not-allowed;
              opacity: 0.7;
            }
          }
        }
      }
    }
  }
}