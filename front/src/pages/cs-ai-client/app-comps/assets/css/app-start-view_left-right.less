.app-start-view_left-right {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 100%;
    align-items: center;
  
    /* 左栏 */
    .left-bar {
      display: flex;
      flex-direction: column;
      width: 200px;
      height: 100%;
  
      .search-bar {
        height: 30px;
      }
  
      .left-list-area {
        flex-grow: 1;
        height: 0;
        overflow-y: auto;
  
        .list-item {
          line-height: 50px;
          opacity: 0.6;
          // transition: all 0.3s ease;
          border-radius: 8px;
          background-color: #2696e01c;
          margin-top: 5px;
          margin-bottom: 5px;
  
          .label-area {
            display: flex;
            flex-direction: row;
            align-items: center;
            cursor: pointer;
  
            .icon {
              font-size: 30px;
              padding-left: 10px;
              padding-right: 5px;
            }
  
            .label {
              flex-grow: 1;
              width: 0;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
  
            .drag-handle {
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: move;
              margin-right: 8px;
              opacity: 0;
              transition: opacity 0.3s;
  
              img {
                width: 20px;
                height: 20px;
              }
            }
          }
  
          &:hover {
            .drag-handle {
              opacity: 1;
            }
          }
  
          .ctrl-area {
            text-align: center;
            line-height: 50px;
  
            i {
              font-size: 20px;
            }
  
            img {
              width: 20px;
              height: 20px;
              cursor: pointer;
              vertical-align: middle;
            }
  
            div {
              display: inline-block;
              width: 10px;
            }
          }
  
        }
  
        .list-item_selected {
          background-color: #34a8dd50;
          opacity: 1;
        }

        .add-item {
          display: flex;
          align-items: center;
          padding: 10px 15px;
          margin: 5px 0;
          border-radius: 8px;
          background-color: #f0f9ff;
          border: 1px dashed #34a8dd;
          cursor: pointer;
          transition: all 0.3s ease;
          
          .plus-icon {
            font-size: 18px;
            color: #34a8dd;
            margin-right: 10px;
          }
          
          .add-text {
            color: #34a8dd;
            font-size: 14px;
          }
          
          &:hover {
            background-color: #e0f3ff;
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(52, 168, 221, 0.2);
          }
        }
  
      }
  
      .add-btn-area {
        text-align: center;
        line-height: 50px;
      }
    }
  
    /* 垂直分割线 */
    .separator-bar-v {
      width: 10px;
      height: 95%;
      border-right: 1px solid #e4d1d1;
    }
  
    .separator-bar-v-right {
      width: 10px;
      height: 100%;
    }
  
    /* 主区 */
    .main-area {
      flex-grow: 1;
      width: 0;
      height: 100%;
      display: flex;
      flex-direction: row;
      overflow-x: auto;
    }
  }