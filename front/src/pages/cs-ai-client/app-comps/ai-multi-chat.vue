<template>
  <div class="ai-multi-chat app-start-view_left-right" v-loading="loading" :update-code="updateCode">
    <!-- 左栏 -->
    <div class="left-bar" v-show="hideLeftBar !== true">
      <div class="search-bar">
        <el-input v-model="query.kw" placeholder="请输入分组名称"></el-input>
      </div>
      <div class="left-list-area">
        <div style="height: 10px">&nbsp;</div>
        <draggable v-model="data.items" :animation="200" @change="handleGroupDragChange" :move="checkGroupMove">
          <template v-for="item in data.items">
            <div :class="{ 'list-item': true, 'list-item_selected': item.selected }">
              <div style="height: 10px;">&nbsp;</div>
              <div class="label-area" :key="item.id" @click="handleSelectGroup(item)">
                <div class="icon">
                  <i class="el-icon-eleme"></i>
                </div>
                <div class="label">
                  <span>{{ item.label }}</span>
                </div>
                <div class="drag-handle" v-if="item.name !== 'app-center'">
                  <img src="../assets/img/tuodong.svg" alt="拖动" />
                </div>
              </div>
              <div class="ctrl-area" v-if="item.selected">
                <img :class="{ 'collect-button': true, 'collect-button_selected': groupCollectState[item.id] }"
                  src="./assets/img/collect.svg" title="收藏" @click="handleCollectGroup(item)" />
                <div style="width: 15px;">&nbsp;</div>
                <img src="./assets/img/edit.svg" title="编辑" @click="handleEdit(item)" />
                <div style="width: 15px;">&nbsp;</div>
                <img src="./assets/img/delete.svg" title="删除" @click="handleDelete(item)" />
                <div style="width: 15px;">&nbsp;</div>
                <span class="site-count">{{ getSiteCountStr(item) }}</span>
              </div>
              <div style="height: 10px;">&nbsp;</div>
            </div>
          </template>
        </draggable>
        <div style="height: 10px">&nbsp;</div>
        <!-- 新增分组 -->
        <div class="add-item" @click="handleNew">
          <el-icon class="plus-icon">
            <Plus />
          </el-icon>
          <span class="add-text">新增分组</span>
        </div>
      </div>
      <!-- <div class="add-btn-area">
        <el-button @click="handleNew">新增分组</el-button>
      </div> -->
    </div>

    <!-- 分隔栏 -->
    <div class="separator-bar-v" v-show="hideLeftBar !== true"></div>
    <div class="separator-bar-v-right" v-show="hideLeftBar !== true">&nbsp;</div>

    <!-- 主区 -->
    <template v-for="group in data.items">
      <div class="main-area" v-if="getSelectedGroup() != null && group.id === getSelectedGroup().id">
        <template v-for="(site, siteIndex) in group.items">
          <ai-multi-chat-site_tab-body :site="site" @on-edit="handleEditSite"
            @on-move-start="handleMoveStart(group, site, siteIndex)"
            @on-move-end="handleMoveEnd(group, site, siteIndex)" @on-move-left="handleMoveLeft(group, site, siteIndex)"
            @on-move-right="handleMoveRight(group, site, siteIndex)"
            @on-delete="handleDeleteSite(getSelectedGroup(), site)" />
        </template>
        <div class="add-btn">
          <img src="./assets/img/add.svg" alt="新增站点" @click="handleNewSite" title="新增站点" />
          <!-- <i class="el-icon-circle-plus-outline"></i> -->
        </div>
      </div>
    </template>

    <!-- 分组表单弹窗 -->
    <el-dialog :title="`${formMode === 0 ? '新增' : '编辑'}分组`" v-model="groupDialogVisible" width="800px"
      :close-on-click-modal="false">
      <div v-loading="loading">
        <div class="dialog-body" style="display: flex; flex-direction: row;">
          <el-form :model="form" label-width="80px" style="flex-grow: 1;">
            <el-form-item label="名称">
              <el-input v-model="form.label" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="备注">
              <el-input type="textarea" v-model="form.note" placeholder="请输入" :rows="3"></el-input>
            </el-form-item>
          </el-form>
          <div class="amc_site-list">
            <draggable v-model="form.items" :animation="200" @change="handleSiteDragChange">
              <template v-for="site in form.items">
                <div class="item">
                  <div class="label">
                    {{ site.label }}
                  </div>
                  <div class="sort">
                    <img src="../assets/img/tuodong.svg" alt="拖动" />
                  </div>
                </div>
              </template>
            </draggable>
          </div>
        </div>
        <div class="dialog-footer" style="text-align: center;">
          <el-button type="primary" @click="handleGroupSave">保 存</el-button>
          &emsp;
          <el-button @click="groupDialogVisible = false">返 回</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 站点表单弹窗 -->
    <el-dialog :title="`${formMode === 0 ? '新增' : '编辑'}站点`" v-model="siteDialogVisible" width="900px"
      :close-on-click-modal="false">
      <div v-loading="loading">
        <div style="display: flex; flex-direction: row;">
          <el-form :model="siteForm" label-width="80px" style="flex-grow: 1;">
            <el-form-item label="名称">
              <el-input v-model="siteForm.label" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="地址">
              <el-input type="textarea" v-model="siteForm.url" placeholder="请输入" :rows="5"></el-input>
            </el-form-item>
            <el-form-item label="宽度">
              <el-input-number v-model="siteForm.width" :min="800" />
            </el-form-item>
            <el-form-item label="图标">
              <el-input v-model="siteForm.icon" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="siteForm.viewportType"
                  placeholder="请选择"
                  style="width: 240px">
                <el-option label="默认" :value="null" />
                <el-option label="嵌入" value="1" />
              </el-select>
            </el-form-item>
          </el-form>
          <div style="width: 1px; border-left: 1px solid #e5e5e5; margin-left: 20px; margin-right: 20px;">&nbsp;</div>
          <ai-multi-chat-site_ai-tool-selector :ref="el => { if (el) el.onShowed(); }"
            @tool-click="handleAIToolItemClick" />
        </div>
        <div style="height: 30px;">&nbsp;</div>
        <div class="dialog-footer" style="text-align: center;">
          <el-button type="primary" @click="handleSiteSave">保 存</el-button>
          &emsp;
          <el-button @click="siteDialogVisible = false">返 回</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import Vue from 'vue'
import { VueDraggableNext } from 'vue-draggable-next'
import aiMultiChatGroupMixin from './mixin/ai-multi-chat_group_mixin.js'
import aiMultiChatSiteMixin from './mixin/ai-multi-chat_site_mixin.js'
import { defineAsyncComponent, ref } from 'vue';

import './assets/css/app-start-view_left-right.less'
import { waitFuncDone, waitGetObj } from '../../../code/util/code-util.js';

export default {
  name: "ai-multi-chat",
  mixins: [aiMultiChatGroupMixin, aiMultiChatSiteMixin],
  props: {
    openAppName: {
      type: String,
      default: null
    }
  },
  components: {
    draggable: VueDraggableNext,
    'ai-multi-chat-site_tab-body': defineAsyncComponent(() => import('./components/ai-multi-chat-site_tab-body.vue')),
    'ai-multi-chat-site_ai-tool-selector': defineAsyncComponent(() => import('./components/ai-multi-chat-site_ai-tool-selector.vue'))
  },
  data() {
    return {
      debug: false,
      onShowedDone: false,
      query: {
        kw: ''
      },
      data: {
        // [{ id, label, note, items: [{ label, url }] }]
        items: []
      },
      updateCode: 0,
      loading: false,
      hideLeftBar: false,
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    async onShowed(pars = {}) {
      console.log('ai-multi-chat.onShowed')
      await this.loadGroupData()

      if (pars.hideLeftBar != null) {
        this.hideLeftBar = pars.hideLeftBar
      }

      if (pars.selectedGroupId != null) {
        this.handleSelectGroupById(pars.selectedGroupId)
      }

      this.onShowedDone = true;
    },
    async onActivated(pars = {}) {
      await waitFuncDone(() => this.onShowedDone);

      console.log('ai-multi-chat.onActivated', pars)
      if (pars.childAppId && pars.childAppId.length > 0) {
        this.hideLeftBar = true;
        this.handleSelectGroupById(pars.childAppId);
      }
      else {
        this.hideLeftBar = false;
        this.handleSelectGroupById(null);
      }
    },
    getSiteCountStr(group) {
      if (group.items && group.items.length > 0) {
        return `(${group.items.length})`
      }
      return ''
    },
    handleNew() {
      this.showNewForm()
    },
    async handleEdit(item) {
      this.showEditForm(item)
    },
    async handleDelete(item) {
      this.$confirm('确定要删除吗？', '确认提示', { type: 'warning' }).then(async () => {
        // 点击确认
        this.data.items = this.data.items.filter(i => i.id !== item.id)
        this.updateCode++

        this.loading = true
        this.data = await this.getCtx().aiMultiChatService.saveData(this.data)
        this.loading = false
      }).catch(() => {
        // 点击取消
      });
    },
    async handleMoveStart(group, site, siteIndex) {
      if (siteIndex > 0) {
        group.items.splice(siteIndex, 1);
        group.items.splice(0, null, site);

        this.loading = true
        this.data = await this.getCtx().aiMultiChatService.saveData(this.data)
        this.loading = false
      }
    },
    async handleMoveEnd(group, site, siteIndex) {
      if (siteIndex < group.items.length - 1) {
        group.items.splice(siteIndex, 1);
        group.items.push(site);

        this.loading = true
        this.data = await this.getCtx().aiMultiChatService.saveData(this.data)
        this.loading = false
      }
    },
    async handleMoveLeft(group, site, siteIndex) {
      if (siteIndex > 0) {
        group.items.splice(siteIndex, 1);
        group.items.splice(siteIndex - 1, null, site);

        this.loading = true
        this.data = await this.getCtx().aiMultiChatService.saveData(this.data)
        this.loading = false
      }
    },
    async handleMoveRight(group, site, siteIndex) {
      if (siteIndex < group.items.length - 1) {
        group.items.splice(siteIndex, 1);
        if (siteIndex === group.items.length - 2) {
          group.items.push(site);
        }
        else {
          group.items.splice(siteIndex + 1, null, site);
        }

        this.loading = true
        this.data = await this.getCtx().aiMultiChatService.saveData(this.data)
        this.loading = false
      }
    },
  },
  async mounted() {
    const self = this;
    this.$emit('on-ready')

    await this.$nextTick(async () => {
      const cacIndexView = await waitGetObj(self.getCtx(), 'cacIndexView');
      // 注册
      await cacIndexView.registerAppStartPage({
        name: 'ai-multi-chat',
        openAppName: self.openAppName,
        view: self,
      });
      self.onShowed();
    });
  },
}
</script>

<style scoped lang="less">
.app-start-view_left-right {

  .left-bar {

    .ctrl-area {

      .collect-button {}

      .collect-button_selected {
        filter: invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
      }

      .site-count {
        vertical-align: middle;
        color: #9c9c9c;
      }
    }
  }

  /* 主区 */
  .main-area {
    background-color: white;

    .add-btn {
      flex-shrink: 0;
      text-align: center;
      line-height: 50px;
      font-size: 60px;
      width: 300px;

      display: flex;
      align-items: center;
      justify-content: center;

      opacity: 0.3;

      img {
        width: 90px;
        height: 90px;
        cursor: pointer;
      }
    }
  }
}

.amc_site-list {

  .item {
    display: flex;
    flex-direction: row;
    line-height: 35px;
    align-items: center;

    .label {
      margin-left: 30px;
    }

    .sort {

      img {
        height: 25px;
        vertical-align: middle;
        cursor: move;
      }
    }
  }
}
</style>