<template>
    <div>
        <div style="text-align: center;">
            <el-checkbox v-model="allAIToolsQuery.ask" label="问答" size="large" @change="handleQueryChange" />
            <el-checkbox v-model="allAIToolsQuery.picture" label="图片" size="large" @change="handleQueryChange" />
            <el-checkbox v-model="allAIToolsQuery.video" label="视频" size="large" @change="handleQueryChange" />
        </div>
        <div class="ai-tool-list">
            <template v-for="tool in allAITools">
                <div class="ai-tool-item" @click="handleAIToolItemClick(tool)">
                    <div style="width: 10px;">&nbsp;</div>
                    <div class="icon-area">
                        <img :src="tool.icon_url" alt="图标" />
                    </div>
                    <div style="width: 10px;">&nbsp;</div>
                    <div class="label-area">
                        {{ tool.name }}
                    </div>
                    <div style="width: 20px;">&nbsp;</div>
                </div>
            </template>
            <div style="clear: both;"></div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ai-multi-chat-site_ai-tool-selector',
    data() {
        return {
            allAIToolsQuery: {
                ask: null,
                picture: null,
                video: null,
            },
            loadedAITools: [],
            allAITools: [],
        }
    },
    methods: {
        getCtx() {
            return this.$ctx
        },
        async onShowed() {
            const self = this;

            self.loadedAITools = (await self.getCtx().dataSourceMapper.getAIToolList()).data;
            self.filter();
        },
        filter() {
            const self = this;
            if (self.loadedAITools) {
                self.allAITools = self.loadedAITools.filter(tool => {
                    return (!self.allAIToolsQuery.ask || tool.tags.includes('|问答|')) &&
                        (!self.allAIToolsQuery.picture || tool.tags.includes('|图片|')) &&
                        (!self.allAIToolsQuery.video || tool.tags.includes('|视频|'));
                });
            }
        },
        handleQueryChange() {
            this.filter();
        },
        handleAIToolItemClick(tool) {
            this.$emit('tool-click', tool)
        }
    }
}
</script>

<style>
.ai-tool-list {
    width: 430px;

    .ai-tool-item {
        float: left;
        display: flex;
        flex-direction: row;
        line-height: 35px;
        align-items: center;
        margin-bottom: 10px;
        cursor: pointer;

        .icon-area {

            img {
                height: 30px;
                vertical-align: middle;
            }
        }

        .label-area {
            width: 70px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

        }
    }
}
</style>