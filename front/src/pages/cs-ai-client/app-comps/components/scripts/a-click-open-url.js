/*** 复制点击的A标签内容 ***/
// 监听所有 <a> 点击
document.addEventListener('click', (e) => {
    const debug = false;
    if (debug) console.log('【调试】点击', e)

    const link = e.target.closest('a');
    if (!link) return;

    // 只要是 <a> 标签，就拦截
    // e.preventDefault();


    const attr_type = link.getAttribute('type');
    const attr_dataState = link.getAttribute('data-state');
    if (attr_type === 'button' && attr_dataState && attr_dataState.length > 0) {
        // 这是grok里的按钮
        return;
    }

    console.log(`【调试】location.href`, location.href)
    console.log(`【调试】link.href`, link.href)
    console.log(`【调试】link`, link.innerText)
    // 修正地址
    if (link.href === '/' || link.href.startsWith(location.href) || (link.innerText && link.innerText.indexOf('新建会话') != -1)) {
        // 忽略
    } else if (link.href.endsWith(')/')) {
        if (debug) console.log('【调试】点击了以 ) 结尾的链接', link.href);
        const newUrl = link.href.substring(0, link.href.length - 2);

        // 复制URL到剪贴板
        navigator.clipboard.writeText(newUrl).then(() => {
            if (debug) console.log('【调试】URL已复制到剪贴板', newUrl);
        }).catch(err => {
            console.error('复制到剪贴板失败:', err);
        });

        // const { ipcRenderer } = require('electron');
        // ipcRenderer.invoke('open-external-url', newUrl);
    } else {
        if (debug) console.log('【调试】点击了其他链接', link.href);

        // 复制URL到剪贴板
        navigator.clipboard.writeText(link.href).then(() => {
            if (debug) console.log('【调试】URL已复制到剪贴板', link.href);
        }).catch(err => {
            console.error('复制到剪贴板失败:', err);
        });

        // const { ipcRenderer } = require('electron');
        // ipcRenderer.invoke('open-external-url', link.href);
    }

    // // 如果是 target="_blank" 或 Ctrl+点击（新标签页）
    // if (link.target === '_blank' || e.ctrlKey || e.metaKey) {
    //     e.preventDefault();

    //     const { ipcRenderer } = require('electron');
    //     ipcRenderer.invoke('open-external-url', link.href);
    // }
}, true);


/*** 修正Grok遮挡问题 ***/
var toFindGrokBugThread;

function toFindGrokBug() {
    console.log(`修正Grok遮挡问题...`)
    // 创建一个新的<style>元素
    const styleElement = document.createElement('style');
    // 定义要覆盖的CSS样式
    const cssRules = `
  .\\@container\\/nav {
    container-type: inline-size;
    container-name: nav;
    z-index: 999 !important;
  }
`;
    // 将CSS规则添加到style元素
    styleElement.textContent = cssRules;
    // 将style元素添加到文档头部
    document.body.appendChild(styleElement);
}

var documentReadyCheckThread;
documentReadyCheckThread = setInterval(() => {
    if (document.readyState === 'complete') {
        if (location.href.indexOf('grok.com') !== -1) {
            toFindGrokBug()
        }

        clearInterval(documentReadyCheckThread)
    }
}, 1000)
