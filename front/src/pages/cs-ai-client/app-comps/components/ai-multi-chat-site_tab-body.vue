<template>
  <div class="webview-container" :style="{ 'width': getSiteWidth(), height: '100%' }">
    <div class="header">
      <el-icon title="移动到开始" @click="handleMoveStart">
        <DArrowLeft></DArrowLeft>
      </el-icon>
      <el-icon title="向左移动" @click="handleMoveLeft" style="margin-left: 20px;">
        <ArrowLeft></ArrowLeft>
      </el-icon>
      <img src="../../assets/img/refresh.svg" title="刷新" @click="handleRefreshClick"/>
      <div style="width:20px;">&nbsp;</div>
      <img v-if="site.icon && site.icon.length > 0" :src="site.icon" @click="openExternalLink(site.url)"
           style="height: 20px;margin-left:0;margin-right: 10px;"/>
      <span @click="openExternalLink(site.url)" style="cursor: pointer;">{{ site.label }}</span>
      <img src="../assets/img/edit.svg" title="编辑" @click="handleEditClick(site)"/>
      <img src="../assets/img/delete.svg" title="删除" @click="handleDeleteClick(site)"/>
      <el-icon title="向右移动" @click="handleMoveRight" style="margin-left: 20px;">
        <ArrowRight></ArrowRight>
      </el-icon>
      <el-icon title="移动到末尾" @click="handleMoveEnd" style="margin-left: 20px;">
        <DArrowRight></DArrowRight>
      </el-icon>
      <el-icon title="调试" @click="handleDebug" style="margin-left: 20px;">
        <Stopwatch />
      </el-icon>
    </div>
    <template v-if="site.viewportType === '1'">
      <iframe ref="webview0" class="body" :src="site.url" style="width: 100%; height: 100%;border: 0;"></iframe>
    </template>
    <template v-else>
      <webview ref="webview0" class="body" :src="site.url" style="width: 100%; height: 100%;" plugins
               :allowpopups="true" :disablewebsecurity="false"
               webpreferences="nodeIntegration=no, contextIsolation=yes, javascript=yes, webSecurity=yes">
      </webview>
    </template>
  </div>
</template>

<script>
import aClickOpenUrlScript from './scripts/a-click-open-url.js?raw';

export default {
  name: 'ai-multi-chat-site-tab-body',
  props: {
    site: Object,
  },
  data() {
    return {
      debug: false,
    }
  },
  methods: {
    getSiteWidth() {
      return `${(this.site.width || 800)}px`
    },
    handleRefreshClick() {
      if (this.$refs.webview0) {
        this.$refs.webview0.reload();
      }
    },
    handleEditClick(site) {
      this.$emit('on-edit', site)
    },
    handleDeleteClick(site) {
      this.$confirm('确定要删除吗？', '确认提示', {type: 'warning'}).then(() => {
        // 点击确认
        this.$emit('on-delete', site)
      }).catch(() => {
        // 点击取消
      });
    },
    handleMoveStart() {
      this.$emit('on-move-start', this.site)
    },
    handleMoveEnd() {
      this.$emit('on-move-end', this.site)
    },
    handleMoveLeft() {
      this.$emit('on-move-left', this.site)
    },
    handleMoveRight() {
      this.$emit('on-move-right', this.site)
    },
    handleDebug() {
      this.$refs.webview0.openDevTools();
    },
    // 打开外部链接的方法
    openExternalLink(url) {
      if (this.debug) console.log('尝试打开外部链接:', url);
      const {ipcRenderer} = require('electron');
      ipcRenderer.invoke('open-external-url', url);
    }
  },
  mounted() {
    const self = this;
    const webview = this.$refs.webview0;

    if (webview) {
      webview.addEventListener('dom-ready', () => {
        if (this.debug) console.log('Webview 已挂载');

        webview.addEventListener('console-message', (e) => {
          console.log('webview message: ', e.message)
        })

        // // 处理新窗口打开请求，使用系统默认浏览器打开链接
        // webview.addEventListener('new-window', (event) => {
        //     console.log('捕获到 new-window 事件:', event.url);
        //     event.preventDefault();
        //     self.openExternalLink(event.url);
        // });

        webview.executeJavaScript(aClickOpenUrlScript);
      })
    }
  }
}
</script>

<style scoped lang="less">
.webview-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-right: 1px solid #e4e7ed;
  flex-shrink: 0;

  .header {
    line-height: 30px;
    text-align: center;
    background-color: #f5f7fa;
    color: #333;
    font-weight: 600;
    font-size: 15px;
    border-bottom: 1px solid #e4e7ed;
    padding: 0 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      height: 15px;
      margin-left: 20px;
      cursor: pointer;
    }

    .el-icon {
      cursor: pointer;
    }
  }

  .body {
    flex-grow: 1;
  }
}
</style>