import ScrWorkflowService from "../../../../../../code/module/scr-workflow/service/ScrWorkflowService.js";
export default {
    methods: {
        buildService() {
            if (this.getCtx().scrWorkflowService == null) {
                this.getCtx().scrWorkflowService = new ScrWorkflowService(this.getCtx());
            }
        },
        async getList() {
            return await this.getCtx().scrWorkflowService.getList();
        },
        async getForm(id) {
            return await this.getCtx().scrWorkflowService.getForm(id);
        },
        async deleteForm(id) {
            return await this.getCtx().scrWorkflowService.deleteForm(id);
        },
        async saveForm(updType, form) {
            return await this.getCtx().scrWorkflowService.saveForm(updType, form);
        },
    }
}