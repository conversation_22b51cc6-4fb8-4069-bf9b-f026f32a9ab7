export default {
    data() {
        return {
            customToolBar: {
              items: [
                {
                  label: '状态',
                  type: 'state',
                  icon: `${this.iconDirUrl}node-state.svg`,
                  summary: '判断状态是否匹配'
                },
                {
                  label: '申请任务',
                  type: 'app-task',
                  icon: `${this.iconDirUrl}node-app-task.svg`,
                  summary: '申请任务并返回申请是否成功'
                },
                {
                  label: '执行任务',
                  type: 'exec-task',
                  icon: `${this.iconDirUrl}node-exec-task.svg`,
                  summary: '执行任务并返回任务是否完成'
                },
              ]
            },
            customWorkflowFields: [
              { name: 'name', label: '名称', type: 'text', value: null },
              {
                name: 'main_body', label: '主体', type: 'select', value: null,
                items: [
                  { value: 'Creep', label: 'Creep' },
                ]
              },
            ],
            customNodeFieldsMap: {
              'state': [
                { name: 'label', label: '标签', type: 'text', value: null },
                {
                  name: 'name', label: '状态名', type: 'select', value: null,
                  items: [
                    { value: 'state', label: '状态' },
                    // { value: 'collect_state', label: '采集状态' },
                  ]
                },
                { name: 'value', label: '状态值', type: 'text', value: null },
              ],
              'app-task': [
                { name: 'label', label: '标签', type: 'text', value: null },
                { name: 'name', label: '任务键', type: 'text', value: null },
                { name: 'max', label: '同事上限', type: 'number', value: null },
                { name: 'min_store', label: '最低储量', type: 'number', value: null },
              ],
              'exec-task': [
                { name: 'label', label: '标签', type: 'text', value: null },
                { name: 'name', label: '任务键', type: 'text', value: null },
              ],
            },
        }
    }
}