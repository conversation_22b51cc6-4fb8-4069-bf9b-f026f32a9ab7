
import { waitGetObj } from "../../../../../../code/util/code-util.js";

export default {
    data() {
        return {
            updType: null,
            loadedForm: null,

            formDialog: {
                title: '',
                visible: false,
                width: null,
                height: null,
                loading: false,
            },
        }
    },
    methods: {
        // 点击新增
        async handleAddClick() {
            const self = this;
            try {
                self.loading = true;
                this.updType = 0;

                this.loadedForm = {
                    id: null,
                    search: JSON.stringify({
                        name: '',
                        main_body: '',
                    }),
                    content: JSON.stringify({
                        name: '',
                        main_body: '',
                        design_data: '',
                        runtime_data: '',
                    }),
                    version: null,
                };

                this.formDialog.title = `新增工作流`
                this.onResize();
                this.formDialog.visible = true
                this.$nextTick(async () => {
                    const workflowEditor0 = await waitGetObj(self.$refs, 'workflowEditor0');
                    workflowEditor0.onShowed();
                    workflowEditor0.setDesignData({
                        workflowForm: {
                            name: '',
                            main_body: '',
                        }
                    });
                    self.loading = false;
                })
            } catch (exc) {
                this.$alert(exc.message, { type: 'error' });
            }
        },
        // 点击编辑
        async handleEditClick(item) {
            const self = this;
            try {
                self.loading = true;
                this.updType = 1;
                const form = await this.getForm(item.id)
                this.loadedForm = form;

                const cObj = JSON.parse(this.loadedForm.content);

                this.formDialog.title = `编辑工作流“${item.name}”`
                this.onResize();
                this.formDialog.visible = true
                this.$nextTick(async () => {
                    const workflowEditor0 = await waitGetObj(self.$refs, 'workflowEditor0');
                    workflowEditor0.onShowed();
                    workflowEditor0.setDesignData(JSON.parse(cObj.design_data));
                    self.loading = false;
                })
            } catch (exc) {
                this.$alert(exc.message, { type: 'error' });
            }
        },
        // 点击删除
        async handleDeleteClick(item) {
            const self = this;
            this.$confirm('确定要删除“' + item.name + '”吗？', '确认提示', { type: 'warning' }).then(async () => {
                // 点击确认
                try {
                    await self.deleteForm(item.id);
                    await self.loadList();
                } catch(exc) {
                    alert(exc.message)
                }
            }).catch(() => {
                // 点击取消
            });
        },
        // 关闭表单对话框
        handleFormDialogClose() {
            this.formDialog.visible = false
        },
        // 保存工作流
        async handleWorkflowSaveClick(e) {
            const self = this;
            try {
                const sObj = JSON.parse(this.loadedForm.search);
                const cObj = JSON.parse(this.loadedForm.content);

                // search
                sObj.name = e.designData.workflowForm.name;
                sObj.main_body = e.designData.workflowForm.main_body;

                // content
                cObj.name = e.designData.workflowForm.name;
                cObj.main_body = e.designData.workflowForm.main_body;
                cObj.design_data = JSON.stringify(e.designData);
                cObj.runtime_data = JSON.stringify(e.runtimeData);

                const form = {
                    ...this.loadedForm,
                    search: JSON.stringify(sObj),
                    content: JSON.stringify(cObj),
                };

                self.formDialog.loading = true;
                const saveResult = await this.saveForm(self.updType, form);
                self.loadedForm.id = saveResult.id;

                // 快捷键保存不会关闭窗口
                if (e.from === 'ctrlS') {
                    if (self.updType === 0) {
                        self.updType = 1;
                        self.loadedForm = await this.getForm(self.loadedForm.id);
                    }
                    else {
                        this.loadedForm.version++;
                    }
                    self.formDialog.loading = false;
                }
                // 点击保存按钮
                else {
                    self.formDialog.loading = false;
                    this.formDialog.visible = false
                    await this.loadList();
                }

                self.$message({ message: '保存成功', type: 'success' });
            } catch (exc) {
                this.$alert(exc.message, { type: 'error' });
            }
        },
    }
}