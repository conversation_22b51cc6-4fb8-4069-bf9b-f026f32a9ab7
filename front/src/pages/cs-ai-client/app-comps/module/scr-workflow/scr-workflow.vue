<template>
  <div class="scr-workflow app-start-view_card" :update-code="updateCode" v-loading="loading">
    <div style="display: flex;width: 100%;height: 0;flex-grow: 1;">
      <div class="app-item-list">
        <!-- 新增按钮 -->
        <div class="app-item" title="新增" @click="handleAddClick" v-if="list.items.length > 30">
          <div style="width: 10px;">&nbsp;</div>
          <img src="../../assets/img/add.svg" alt="新增" style="height: 71px;" />
          <div style="width: 10px;">&nbsp;</div>
        </div>
        <template v-for="item in list.items">
          <!-- 工作流列表项 -->
          <div class="app-item">
            <div style="width: 10px;">&nbsp;</div>
            <div class="icon" @click="handleClick(item)">
              <img :src="getIcon(item)" alt="" />
            </div>
            <div style="width: 15px;">&nbsp;</div>
            <div class="right-area" @click="handleClick(item)">
              <div style="height: 10px;">&nbsp;</div>
              <div class="label">{{ item.name }}</div>
              <div class="summary">{{ item.main_body }}（{{ item.createUser }}）</div>
            </div>
            <div style="width: 5px;">&nbsp;</div>
            <div class="action">
              <div style="height: 5px;">&nbsp;</div>
              <div style="flex-grow: 1;">
                <input type="button" value="编辑" @click="handleEditClick(item)" />
              </div>
              <div style="flex-grow: 1;">
                <input type="button" value="删除" @click="handleDeleteClick(item)" />
              </div>
              <div style="height: 5px;">&nbsp;</div>
            </div>
            <div style="width: 20px;">&nbsp;</div>
          </div>
        </template>
        <!-- 新增按钮 -->
        <div class="app-item" title="新增" @click="handleAddClick">
          <div style="width: 10px;">&nbsp;</div>
          <img src="../../assets/img/add.svg" alt="新增" style="height: 71px;" />
          <div style="width: 10px;">&nbsp;</div>
        </div>
        <div style="clear: both;"></div>
      </div>
    </div>
    <div style="height: 10px;">&nbsp;</div>
    <el-dialog v-model="formDialog.visible" :title="formDialog.title" :width="formDialog.width" top="30px"
      :before-close="handleFormDialogClose">
      <div v-loading="formDialog.loading"
        :style="`display: flex; flex-direction: row; width: 100%; height:${formDialog.height}; border: 1px solid #ddd;`">
        <workflow-editor ref="workflowEditor0" v-if="formDialog.visible" :debug="debug" 
        :iconDirUrl="iconDirUrl"
        :showExportButton="true" :customToolBar="customToolBar"
          :customWorkflowFields="customWorkflowFields" :customNodeFieldsMap="customNodeFieldsMap"
          @on-save="handleWorkflowSaveClick" style="width: 0;flex-grow: 1;"></workflow-editor>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { defineAsyncComponent, ref } from 'vue';
import '../../assets/css/app-start-view_card.less'
import { waitGetObj } from "../../../../../code/util/code-util.js";
import workflowEditor from '../../../components/cs-workflow/left-tool-flow-editor.vue'

import _api_mixin from './mixin/_api_mixin';
import _cfg_mixin from './mixin/_cfg_mixin';
import _form_mixin from './mixin/_form_mixin';

export default {
  name: "scr-workflow",
  mixins: [_api_mixin, _cfg_mixin, _form_mixin],
  props: {
    iconDirUrl: {
      type: String,
      default: './assets/imgs/scr-workflow/'
    }
  },
  components: {
    'workflow-editor': workflowEditor,
    // 'workflow-editor': defineAsyncComponent(() => import('../components/cs-workflow/left-tool-flow-editor.vue'))
  },
  data() {
    return {
      debug: true,
      updateCode: 0,
      list: {
        // [{ id, name, label, icon, summary, app_comp_name, enabled }]
        items: []
      },
      loading: false,
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    async onShowed() {
      this.buildService();
      await this.loadList();
    },
    async onActivated(pars = {}) {
      await this.loadList();
    },
    async loadList() {
      try {
        this.loading = true;
        this.list.items = await this.getList();
        this.loading = false;
      } catch (exc) {
        this.$alert(exc.message, { type: 'error' });
      }
    },
    getIcon(item) {
      return new URL(`./assets/img/workflow.svg`, import.meta.url).href
    },
    async handleClick(item) {
    },
    // ...
    onScroll() {
      const self = this;
    },
    onResize() {
      const self = this;
      this.formDialog.width = document.documentElement.clientWidth - 100 + 'px'
      this.formDialog.height = document.documentElement.clientHeight - 150 + 'px'
      self.onScroll();
    },
  },
  async mounted() {
    const self = this;
    // ...
    window.addEventListener('resize', self.onResize, false);
    window.addEventListener('scroll', self.onScroll, false);


    self.$emit('on-ready');

    const cacIndexView = await waitGetObj(self.getCtx(), 'cacIndexView');
    // 注册
    await cacIndexView.registerAppStartPage({
      name: 'scr-workflow',
      openAppName: self.openAppName,
      view: self,
    });
    self.onShowed();
  },
  destroyed: function () {
    const self = this;
    // ...
    window.removeEventListener('resize', self.onResize, false);
    window.removeEventListener('scroll', self.onScroll, false);
  }
}
</script>

<style scoped lang="less"></style>