export default {
    data() {
        return {
            customToolBar: {
              items: [
                {
                  label: '用户输入',
                  type: 'user-input',
                  icon: `${this.iconDirUrl}node-user-input.svg`,
                  summary: '执行前给用户输入参数的面板'
                },
                {
                  label: '大语言模型',
                  type: 'llm',
                  icon: `${this.iconDirUrl}node-llm.svg`,
                  summary: '通过大预言模型进行处理'
                },
                {
                  label: '拆分文本内容',
                  type: 'split-text',
                  icon: `${this.iconDirUrl}node-split-text.svg`,
                  summary: '根据特定算法对文本进行拆分'
                },
                {
                  label: '逻辑判断',
                  type: 'if-logic',
                  icon: `${this.iconDirUrl}node-if-logic.svg`,
                  summary: '使用if逻辑判断条件是否成立'
                },
                {
                  label: '遍历流转',
                  type: 'each-flow',
                  icon: `${this.iconDirUrl}node-each-flow.svg`,
                  summary: '将传入的列表进行遍历后流转到下个节点'
                },
                {
                  label: '读取文本',
                  type: 'read-text',
                  icon: `${this.iconDirUrl}node-read-text.svg`,
                  summary: '读取预先设置后的文本'
                },
                {
                  label: '读取本地文件',
                  type: 'read-local-file',
                  icon: `${this.iconDirUrl}node-read-local-file.svg`,
                  summary: '从本地文件读取内容'
                },
                {
                  label: '写入本地文件',
                  type: 'write-local-file',
                  icon: `${this.iconDirUrl}node-write-local-file.svg`,
                  summary: '写入内容到本地文件中'
                },
                {
                  label: '读取临时数据',
                  type: 'read-tmp-dat',
                  icon: `${this.iconDirUrl}node-read-tmp-dat.svg`,
                  summary: '从临时数据读取某字段值'
                },
                {
                  label: '写入临时数据',
                  type: 'write-tmp-dat',
                  icon: `${this.iconDirUrl}node-write-tmp-dat.svg`,
                  summary: '将某字段值写入临时数据'
                },
                {
                  label: '调试输出',
                  type: 'debug-output',
                  icon: `${this.iconDirUrl}node-debug-output.svg`,
                  summary: '输出前一个节点传入的内容'
                },
              ]
            },
            customWorkflowFields: [
              { name: 'name', label: '名称', type: 'text', value: null },
              { name: 'key', label: '键值', type: 'text', value: null },
              { name: 'id', label: 'ID', type: 'text', value: null, readonly: true },
            ],
            customNodeFieldsMap: {
              // 用户输入
              'user-input': [
                { name: 'label', label: '标签', type: 'text', value: null },
                { name: 'design', label: '设计', type: 'textarea', value: null },
              ],
              // 大语言模型
              'llm': [
                { name: 'label', label: '标签', type: 'text', value: null },
                { name: 'model', label: '模型', type: 'select', value: null,
                  items: [
                    { value: '', label: '' },
                    { value: 'ds_deepseek-chat', label: 'DeepSeek-V3-满血' },
                    { value: 'albl_qwen3-235b-a22b|no_think|', label: 'Qwen3-235b【不思考】（ALiBL）' },
                    { value: 'ollama_qwen3:32b|no_think|', label: 'Qwen3-32b【不思考】（公司）' },
                    { value: 'ollama_qwen3:14b|no_think|', label: 'Qwen3-14b【不思考】（公司）' },
                    { value: 'br_gpt-4o', label: 'GPT-4o（Brain）' },
                  ]
                },
                { name: 'model_1', label: '模型', type: 'text', value: null },
                { name: 'system_prompt', label: '系统提示词', type: 'textarea', value: null },
                { name: 'user_prompt', label: '用户提示词', type: 'textarea', value: null },
                { name: 'output_type', label: '输出类型', type: 'select', value: null,
                  items: [
                    { value: '-', label: '默认' },
                    { value: 'list', label: '列表' },
                  ]
                },
              ],
              // 拆分文本内容
              'split-text': [
                { name: 'label', label: '标签', type: 'text', value: null },
                { name: 'name', label: '算法', type: 'select', value: null,
                  items: [
                    { value: 'md_zj_a0', label: 'MD-章节-A0' },
                  ]
                },
              ],
              // if逻辑判断
              'if-logic': [
                { name: 'label', label: '标签', type: 'text', value: null },
                { name: 'expression', label: '表达式', type: 'textarea', value: null },
              ],
              // *** 读写 ***
              // 读取文本
              'read-text': [
                { name: 'label', label: '标签', type: 'text', value: null },
                { name: 'data', label: '文本内容', type: 'textarea', value: null },
              ],
              // 读取本地文件
              'read-local-file': [
                { name: 'path', label: '路径', type: 'textarea', value: null },
              ],
              // 写入本地文件
              'write-local-file': [
                { name: 'path', label: '路径', type: 'textarea', value: null },
                { name: 'src_name', label: '来源字段名', type: 'text', value: null },
                { name: 'transform', label: '转换类型', type: 'select', value: null,
                  items: [
                    { value: '-', label: '无' },
                    { value: 'md_to_doc', label: 'Markdown > Word' },
                  ]
                },
              ],
              // 读取临时数据
              'read-tmp-dat': [
                { name: 'label', label: '标签', type: 'text', value: null },
                { name: 'name', label: '存储字段名', type: 'text', value: null },
              ],
              // 写入临时数据
              'write-tmp-dat': [
                { name: 'label', label: '标签', type: 'text', value: null },
                { name: 'name', label: '存储字段名', type: 'text', value: null },
                { name: 'src_name', label: '来源字段名', type: 'text', value: null },
                { name: 'save_data', label: '存储数据', type: 'textarea', value: null },
                { name: 'mode', label: '写入模式', type: 'select', value: null,
                  items: [
                    { value: '-', label: '覆盖（默认）' },
                    { value: 'append', label: '添加' },
                  ]
                },
              ],
              // *** 其它 ***
            },
        }
    }
}