import ScrWorkflowService from "../../../../../../code/module/scr-workflow/service/ScrWorkflowService.js";
export default {
    methods: {
        buildService() {
            if (this.getCtx().cliWorkflowService == null) {
                this.getCtx().cliWorkflowService = new ScrWorkflowService(this.getCtx(), {
                    appKey: 'cli_gzl',
                });
            }
        },
        async getList() {
            return await this.getCtx().cliWorkflowService.getList();
        },
        async getForm(id) {
            return await this.getCtx().cliWorkflowService.getForm(id);
        },
        async deleteForm(id) {
            return await this.getCtx().cliWorkflowService.deleteForm(id);
        },
        async saveForm(updType, form) {
            return await this.getCtx().cliWorkflowService.saveForm(updType, form);
        },
    }
}