import {waitGetObj} from "../../../../../../code/util/code-util.js";

export default {
    data() {
        return {
            executeDialog: {
                title: '',
                visible: false,
                width: null,
                height: null,
                loading: false,
            },
        }
    },
    methods: {
        async handleExecuteClick(item) {
            const self = this;

            const func0 = async () => {
                try {
                    self.loading = true;
                    const form = await self.getForm(item.id)
                    self.loadedForm = form;

                    const cObj = JSON.parse(self.loadedForm.content);
                    const design_data = cObj.design_data;
                    const runtime_data = cObj.runtime_data;

                    self.executeDialog.title = `执行工作流“${item.name}”`
                    self.onResize();
                    self.executeDialog.visible = true
                    self.$nextTick(async () => {
                        const executePanel0 = await waitGetObj(self.refs, 'executePanel0');
                        executePanel0.onShowed({
                            id: item.id,
                            design_data: JSON.parse(design_data),
                            runtime_data: JSON.parse(runtime_data),
                        });
                        self.loading = false;
                    })
                } catch (exc) {
                    self.$alert(exc.message, {type: 'error'});
                }
            }

            if (item.has_user_input === true) {
                await func0();
            }
            else {
                this.$confirm(`确定执行“${item.name}”吗？`, '确认提示', {type: 'warning'}).then(async () => {
                    // 点击确认
                    await func0();
                }).catch(() => {
                    // 点击取消
                });
            }
        },
        async handleExecuteDialogClose() {
            const executePanel0 = await waitGetObj(this.refs, 'executePanel0');
            executePanel0.cancel();
            this.executeDialog.visible = false
        },
    }
}