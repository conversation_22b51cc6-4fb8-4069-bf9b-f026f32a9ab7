<template>
  <div class="execute-panel" v-if="viewType === 0">
    <div class="main-area">
      <div class="left-area">
        <template v-for="node in nodeList">
          <div :class="{ node: true, node_doing: node.state === 'doing' }">
            <div class="label">{{ node.label }}</div>
            <div class="summary">
              <span v-if="node.state === 'error'" style="color:red;">【异常】</span>
              <span v-else-if="node.state === 'done'" style="color:gray;">【完成】</span>
              <span v-else-if="node.state === 'doing'" style="color:green;">【执行】</span>
              <span>{{ node.summary }}</span>
            </div>
          </div>
        </template>
      </div>
      <div class="split-line">&nbsp;</div>
      <div class="right-area" ref="rightArea0">
        <pre>{{ outputText }}</pre>
      </div>
    </div>
    <div class="message-area">
      {{ message }}
    </div>
  </div>
  <user-input-panel ref="userInputPanel0" :store-key="storeKey"
                    v-else-if="viewType === 1" :design="userInputDesign"
                    @on-ok="handleUserInputOk"></user-input-panel>
</template>

<script>
import {waitGetObj} from "../../../../../../code/util/code-util.js";
import CLIWorkflowExecutor from "../../../../../../code/module/cli-workflow/CLIWorkflowExecutor.js";
import UserInputPanel from "../../../../app-comps/module/cli-workflow/components/user-input-panel.vue";

export default {
  name: "execute-panel",
  components: {UserInputPanel},
  data() {
    return {
      debug: false,

      workflowId: null,
      storeKey: null,

      // 0 默认，1 显示用户输入
      viewType: 0,
      userInputDesign: {items: []},
      userInputForm: {},
      userInputDone: false,
      userInputCancel: false,

      message: '准备中...',
      outputText: '',
      // nodePaths: [],
      nodeList: [
        // { label: '读取文本数据', summary: '读取中', state: '执行中' },
        // { label: '读取文本数据', summary: '读取中', state: '执行中' },
        // { label: '读取文本数据', summary: '读取中', state: '执行中' },
        // { label: '读取文本数据', summary: '读取中', state: '执行中' },
        // { label: '读取文本数据', summary: '读取中', state: '执行中' },
      ],
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    reset() {
      this.userInputDone = false;
      this.userInputCancel = false;
      this.message = '准备中...';
      this.outputText = '';
      this.nodeList = [];
    },
    async onShowed(pars) {
      const self = this;
      self.reset();

      self.workflowId = pars.id;
      const design_data = pars.design_data;
      const runtime_data = pars.runtime_data;

      self.storeKey = `cli-workflow_user-input_${self.workflowId}`;

      this.message = '执行中...';
      if (self.debug) console.log('runtime_data: ', runtime_data);

      try {
        const wfExecutor = new CLIWorkflowExecutor(this.getCtx(), runtime_data);
        await wfExecutor.start({
          __workflow_label: design_data.workflowForm.name
        }, {
          // 切换到用户输入面板并等待
          async waitUserInput(p) {
            self.userInputDesign = p.design;
            self.viewType = 1;
            const userInputPanel0 = await waitGetObj(self.$refs, 'userInputPanel0');
            userInputPanel0.onShowed();

            await new Promise(async resolve => {
              for (; ;) {
                if (self.userInputDone || self.userInputCancel === true) {
                  resolve();
                  break;
                }
                await new Promise(resolve => {
                  setTimeout(() => {
                    resolve();
                  }, 1000);
                });
              }
            });

            if (self.userInputCancel) {
              return false;
            }

            return self.userInputForm;
          },
          onNodeDoing(p) {
            self.pushNodeOutput('doing', p);
          },
          onNodeDone(p) {
            self.pushNodeOutput('done', p);
          },
          onNodeError(p) {
            self.pushNodeOutput('error', p);
          }
        })


        if (this.userInputCancel === true) {
          this.message = '工作流执行已取消';
          console.log('工作流执行已取消');
        } else {
          this.message = '工作流已执行完毕';
          this.$alert('工作流已执行完毕', {type: 'success'});
        }
      } catch (exc) {
        this.message = `工作流执行异常 -> ${exc.message}`;
        console.error(exc);
        this.$alert('工作流执行异常', {type: 'error'});
      }
    },
    cancel() {
      this.userInputCancel = true;
    },
    pushNodeOutput(type, pars) {
      let nodeLabel = pars.nodeLabel;
      if (pars.index) {
        nodeLabel += `【${pars.index}】`;
      }

      if (this.nodeList.length > 0 && this.nodeList[0].label === nodeLabel) {
        const find = this.nodeList[0];
        find.summary = pars.label;
        find.state = type;

        if (pars.data) {
          this.outputText += `${pars.data}`;
        }
      } else {
        // 不是首次增加节点，添加分隔符
        if (this.nodeList.length > 0) {
          this.outputText += `\n\n========================================\n`;
        }

        this.nodeList.splice(0, null, {
          label: nodeLabel,
          summary: pars.label,
          state: type
        })

        this.outputText += `节点开始“${nodeLabel}”...\n`;
        if (pars.data) {
          this.outputText += `${pars.data}`;
        }
      }

      if (type === 'done') {
        this.outputText += `\n节点结束“${nodeLabel}”。\n`;
      }

      if (this.$refs.rightArea0) {
        this.$refs.rightArea0.scrollTo(0, this.$refs.rightArea0.scrollHeight);

        this.$nextTick(() => {
          if (this.$refs.rightArea0) this.$refs.rightArea0.scrollTo(0, this.$refs.rightArea0.scrollHeight);
        })
      }
    },
    handleUserInputOk(e) {
      this.userInputForm = e.form;
      this.userInputDone = true;
      this.viewType = 0;
    }
  }
}
</script>

<style scoped lang="less">
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap');

.execute-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background: #f8fafc;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .main-area {
    flex: 1;
    display: flex;
    height: calc(100% - 50px);
    background: #fff;
    border-radius: 8px 8px 0 0;
    overflow: hidden;

    .left-area {
      width: 280px;
      height: 100%;
      overflow-y: auto;
      padding: 15px 0;
      background: #f5f7fa;
      border-right: 1px solid #e4e7ed;
      transition: all 0.3s ease;

      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      .node {
        position: relative;
        min-width: 200px;
        margin: 0 15px 10px 15px;
        padding: 12px 15px;
        background: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .label {
          color: #606266;
          font-size: 14px;
          font-weight: 500;
          line-height: 1.5;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          
          &::before {
            content: '';
            display: inline-block;
            width: 6px;
            height: 6px;
            background: #909399;
            border-radius: 50%;
            margin-right: 8px;
          }
        }

        .summary {
          color: #a4a4af;
          font-size: 12px;
          line-height: 1.4;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .node_doing {
        border-left: 3px solid #409eff;
        .label {
          color: #303133;
          font-weight: 600;
          &::before {
            background: #409eff;
            box-shadow: 0 0 5px rgba(64, 158, 255, 0.5);
          }
        }
      }
    }


    .right-area {
      flex: 1;
      height: 100%;
      padding: 20px;
      overflow-y: auto;
      background: #fff;
      color: #333;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 13px;
      line-height: 1.6;

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      pre {
        margin: 0;
        white-space: pre-wrap;
        word-wrap: break-word;
        color: #2c3e50;
      }

      .success {
        color: #67c23a;
      }
      .error {
        color: #f56c6c;
      }
      .warning {
        color: #e6a23c;
      }
      .info {
        color: #909399;
      }
    }
  }

  .message-area {
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 14px;
    color: #606266;
    background: #f5f7fa;
    border-top: 1px solid #e4e7ed;
    border-radius: 0 0 8px 8px;
    transition: all 0.3s;
    
    &:empty {
      display: none;
    }
  }
}

/* 分割线样式 */
.split-line {
  width: 1px;
  margin: 15px 0;
  background: linear-gradient(to bottom, transparent, #e4e7ed, transparent);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .main-area {
    flex-direction: column !important;
    
    .left-area {
      width: 100% !important;
      max-height: 200px;
      border-right: none !important;
      border-bottom: 1px solid #e4e7ed;
    }
    
    .right-area {
      width: 100% !important;
    }
  }
}
</style>