<template>
<div class="user-input-panel" v-if="_design != null">
  <div style="flex-grow: 1;display: flex;flex-direction: column; justify-content: center;">
    <div class="header-area">
      请填写参数
    </div>
    <template v-for="item in _design.items">
      <ditto-fieldset :value="item" v-if="item.type === 'fieldset'"></ditto-fieldset>
      <template v-else>
        <div style="height: 8px;">&nbsp;</div>
        <ditto-field :value="item" :label-width="_design.labelWidth" @on-change="handleFieldChange"></ditto-field>
      </template>
    </template>
    <div class="ctrl-area" style="line-height: 100px;">
      <el-button type="primary" @click="handleOkClick">确认并继续</el-button>
    </div>
  </div>
</div>
</template>
<script>
import DittoFieldset from "../../../../components/cs-workflow/components/ditto-fieldset.vue";
import DittoField from "../../../../components/cs-workflow/components/ditto-field.vue";

export default {
  name: "user-input-panel",
  components: {DittoField, DittoFieldset},
  props: {
    storeKey: null,
    design: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      updateCode: 0,
      _design: null,
    }
  },
  methods: {
    onShowed() {
      this._design = this.design;
      this.loadFromClient();
    },
    saveToClient() {
      const form = {};
      this._proc(this._design, form);

      if (localStorage) {
        // console.log(`保存到客户端: ${this.storeKey}`, form);
        localStorage.setItem(`${this.storeKey}`, JSON.stringify(form))
      }
    },
    loadFromClient() {
      if (localStorage) {
        const form = localStorage.getItem(`${this.storeKey}`);
        if (form) {
          this._proc_set(this._design, JSON.parse(form));
        }
      }
    },
    handleOkClick() {
      const form = {};
      this._proc(this._design, form);

      this.$emit('on-ok', {
        form
      })
    },
    handleFieldChange() {
      this.saveToClient()
    },
    // 递归获取表单数据
    _proc(node, form) {
      if (node.type == null && node.items) {
        for (const item of node.items) {
          this._proc(item, form);
        }
      }
      else {
        form[node.name] = node.value;
      }
    },
    // 递归设置表单数据
    _proc_set(node, form) {
      if (node.type == null && node.items) {
        for (const item of node.items) {
          this._proc_set(item, form);
        }
      }
      else {
        if (node.name) {
          node.value = form[node.name]
        }
      }
    }
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.user-input-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-area {
    font-size: 30px;
    text-align: center;
    line-height: 60px;
  }

  .ctrl-area {
    text-align: center;
  }
}
</style>