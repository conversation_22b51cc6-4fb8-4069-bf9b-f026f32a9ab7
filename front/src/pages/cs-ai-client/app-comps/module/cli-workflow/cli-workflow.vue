<template>
  <div class="cli-workflow app-start-view_card" :update-code="updateCode" v-loading="loading">
    <div style="display: flex;width: 100%;height: 0;flex-grow: 1;">
      <div class="app-item-list">
        <!-- 新增按钮 -->
        <div class="app-item" title="新增" @click="handleAddClick" v-if="list.items.length > 30">
          <div style="width: 10px;">&nbsp;</div>
          <img src="../../assets/img/add.svg" alt="新增" style="height: 71px;" />
          <div style="width: 10px;">&nbsp;</div>
        </div>
        <template v-for="item in list.items">
          <!-- 工作流列表项 -->
          <div class="app-item">
            <div style="width: 10px;">&nbsp;</div>
            <div class="icon" @click="handleClick(item)">
              <img :src="getIcon(item)" alt="" />
            </div>
            <div style="width: 15px;">&nbsp;</div>
            <div class="right-area" @click="handleClick(item)">
              <div style="height: 10px;">&nbsp;</div>
              <div class="label">{{ item.name }}</div>
              <div class="summary" :title="getSummary(item)">
                {{getSummary(item)}}
              </div>
            </div>
            <div style="width: 5px;">&nbsp;</div>
            <div class="action">
              <div style="height: 5px;">&nbsp;</div>
              <div style="flex-grow: 1;">
                <input v-if="canEdit(item)" type="button" value="编辑" :disabled="!canEdit(item)" @click="handleEditClick(item)" />
                <input v-else type="button" value="查看" @click="handleEditClick(item, { onlySee: true })" />
              </div>
              <div style="flex-grow: 1;">
                <input type="button" value="删除" :disabled="!canDelete(item)" @click="handleDeleteClick(item)" />
              </div>
              <div style="height: 5px;">&nbsp;</div>
            </div>
            <div style="width: 5px;">&nbsp;</div>
            <div class="action">
              <div style="height: 5px;">&nbsp;</div>
              <div style="flex-grow: 1;">
                <input type="button" value="执行" :disabled="!canExec(item)" @click="handleExecuteClick(item)" style="height:100%;" />
              </div>
              <div style="height: 5px;">&nbsp;</div>
            </div>
            <div style="width: 20px;">&nbsp;</div>
          </div>
        </template>
        <!-- 新增按钮 -->
        <div class="app-item" title="新增" @click="handleAddClick">
          <div style="width: 10px;">&nbsp;</div>
          <img src="../../assets/img/add.svg" alt="新增" style="height: 71px;" />
          <div style="width: 10px;">&nbsp;</div>
        </div>
        <div style="clear: both;"></div>
      </div>
    </div>
    <div style="height: 10px;">&nbsp;</div>
    <el-dialog v-model="formDialog.visible" :title="formDialog.title" :width="formDialog.width" top="30px"
      :before-close="handleFormDialogClose" :close-on-click-modal="false">
      <div v-loading="formDialog.loading"
        :style="`display: flex; flex-direction: row; width: 100%; height:${formDialog.height}; border: 1px solid #ddd;`">
        <workflow-editor ref="workflowEditor0" v-if="formDialog.visible" :debug="debug" 
        :iconDirUrl="iconDirUrl"
        :showExportButton="showWfExportButton" :customToolBar="customToolBar"
          :customWorkflowFields="customWorkflowFields" :customNodeFieldsMap="customNodeFieldsMap"
          @on-save="handleWorkflowSaveClick" style="width: 0;flex-grow: 1;"></workflow-editor>
      </div>
    </el-dialog>
    <el-dialog v-model="executeDialog.visible" :title="executeDialog.title" :width="executeDialog.width" top="30px"
      :before-close="handleExecuteDialogClose" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="true">
      <div v-loading="executeDialog.loading"
        :style="`display: flex; flex-direction: column; height:${executeDialog.height}; border: 0;`">
        <execute-panel :ref="el => { if (el) refs.executePanel0 = el }" style="flex-grow: 1;"></execute-panel>
<!--        <div class="ctrl-area" style="text-align: center;line-height: 60px;">-->
<!--          <el-button @click="handleExecuteDialogClose">返回</el-button>-->
<!--        </div>-->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { defineAsyncComponent, ref } from 'vue';
import '../../assets/css/app-start-view_card.less'
import { waitGetObj } from "../../../../../code/util/code-util.js";
import workflowEditor from '../../../components/cs-workflow/left-tool-flow-editor.vue'

import _api_mixin from './mixin/_api_mixin';
import _cfg_mixin from './mixin/_cfg_mixin';
import _form_mixin from './mixin/_form_mixin';
import _execute_mixin from './mixin/_execute_mixin';

export default {
  name: "cli-workflow",
  mixins: [_api_mixin, _cfg_mixin, _form_mixin, _execute_mixin],
  props: {
    iconDirUrl: {
      type: String,
      default: './assets/imgs/cli-workflow/'
    }
  },
  components: {
    'workflow-editor': workflowEditor,
    'execute-panel': defineAsyncComponent(() => import('./components/execute-panel.vue')),
    // 'workflow-editor': defineAsyncComponent(() => import('../components/cs-workflow/left-tool-flow-editor.vue'))
  },
  data() {
    return {
      debug: true,
      updateCode: 0,
      loginedUser: null,
      list: {
        // [{ id, name, label, icon, summary, app_comp_name, enabled }]
        items: []
      },
      loading: false,
      refs: ref({}),
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    async onShowed() {
      this.loginedUser = await this.getCtx().loginService.getUser();
      this.buildService();
      await this.loadList();
    },
    async onActivated(pars = {}) {
      await this.loadList();
    },
    async loadList() {
      try {
        this.loading = true;
        this.list.items = await this.getList();
        this.loading = false;
      } catch (exc) {
        this.$alert(exc.message, { type: 'error' });
      }
    },
    getIcon(item) {
      return new URL(`./assets/img/workflow.svg`, import.meta.url).href
    },
    getSummary(item) {
      let str = '';
      // 创建人
      str = item.createUser;
      // 可查看角色
      for (const canSeeRole of this.getCanSeeRoles(item)) {
        str += `、${canSeeRole}`;
      }
      // 可查看人员
      for (const canSeeUser of this.getCanSeeUsers(item)) {
        str += `、${canSeeUser.name}`;
      }
      return str;
    },
    getCanSeeRoles(item) {
      if (item.can_see_roles && item.can_see_roles.length > 0) {
        const list = item.can_see_roles.split('|');
        return list.filter((n) => {
          return n && n.length > 0;
        });
      }
      return []
    },
    getCanSeeUsers(item) {
      if (item.can_see_users && item.can_see_users.length > 0) {
        const list = JSON.parse(item.can_see_users);
        return list;
      }
      return []
    },

    // showSeeButton(item) {
    //   if (item.can_open_users && item.can_open_users.indexOf(this.loginedUser.id) !== -1) return true;
    //   return false;
    // },
    canExec(item) {
      if (item.my === true) return true;

      if (item.can_exec_users && item.can_exec_users.indexOf(this.loginedUser.id) !== -1) return true;

      // ['xx', 'bb']
      const roles = this.loginedUser.roles;
      for (const role of roles) {
        if (item.can_exec_roles && item.can_exec_roles.indexOf(role) !== -1) return true;
      }

      return false;
    },
    canEdit(item) {
      if (item.my === true) return true;

      if (item.can_edit_users && item.can_edit_users.indexOf(this.loginedUser.id) !== -1) return true;

      return false;
    },
    canDelete(item) {
      if (item.my === true) return true;

      return false;
    },

    async handleClick(item) {
    },
    // ...
    onScroll() {
      const self = this;
    },
    onResize() {
      const self = this;

      this.formDialog.width = (document.documentElement.clientWidth - 100) + 'px'
      this.formDialog.height = (document.documentElement.clientHeight - 150) + 'px'

      if (document.documentElement.clientWidth - 100 > 1200) {
        this.executeDialog.width = 1200;
      }
      else {
        this.executeDialog.width = (document.documentElement.clientWidth - 100) + 'px'
      }
      this.executeDialog.height = (document.documentElement.clientHeight - 150) + 'px'

      self.onScroll();
    },
  },
  async mounted() {
    const self = this;
    // ...
    window.addEventListener('resize', self.onResize, false);
    window.addEventListener('scroll', self.onScroll, false);


    self.$emit('on-ready');

    const cacIndexView = await waitGetObj(self.getCtx(), 'cacIndexView');
    // 注册
    await cacIndexView.registerAppStartPage({
      name: 'cli-workflow',
      openAppName: self.openAppName,
      view: self,
    });
    self.onShowed();
  },
  destroyed: function () {
    const self = this;
    // ...
    window.removeEventListener('resize', self.onResize, false);
    window.removeEventListener('scroll', self.onScroll, false);
  }
}
</script>

<style scoped lang="less"></style>