<template>
  <div class="cs-ai-client" style="height: 100%;display: flex;flex-direction: row;" v-loading="loading">
    <!-- 左栏 -->
    <div class="left-bar" style="height: 100%;display: flex;flex-direction: column;">
      <!-- 左栏的顶栏 -->
      <div class="top-bar">
        <div class="logo">
          {{ getTitle() }}
        </div>
      </div>
      <!-- 左栏的分隔栏 -->
      <div style="border-bottom: 1px solid silver;margin: 0 30px 20px 30px"></div>
      <!-- 左栏的菜单栏 -->
      <div class="left-menu-bar" v-loading="leftBarLoading" element-loading-background="rgba(0, 0, 0, 0.3)"
        :style="`flex-grow:1;width:${ui.leftMenuBarWidth}px;`">
        <index-left-menu-bar ref="indexLeftMenuBar0" @on-menu-order-change="handleMenuOrderChange($event)"
          @on-open-menu="openApp($event.name)"></index-left-menu-bar>
      </div>
      <!-- 左栏的底栏 -->
      <div class="bottom-bar">
        <div class="user-label">
          <img src="./assets/img/user.svg" />
          <span>{{ loginUserRealname }}</span>
        </div>
        <div style="width:15px;height: 100%;"></div>
        <div class="logout-btn" @click="handleLogout">
          <img :src="logoutIcon" alt="登出" title="登出" />
        </div>
      </div>
    </div>
    <!-- 左栏右侧主区 -->
    <div class="left-right-main-area" style="height: 100%;flex-grow: 1;display: flex;flex-direction: row;">
      <div class="inner-border-bar-left" style="width: 10px;">&nbsp;</div>
      <div style="height: 100%;flex-grow: 1;display: flex;flex-direction: column;">
        <!-- 顶部边栏 -->
        <div style="height: 10px;">&nbsp;</div>
        <!-- 顶栏 -->
        <div class="top-bar">
          <index-top-bar-right></index-top-bar-right>
        </div>
        <!-- Tab栏 -->
        <div class="tab-bar" style="display: flex;flex-direction: row;">
          <!-- Tab主栏 -->
          <div style="flex-grow: 1;">
            <index-tab-bar ref="indexTabBar0" :open-menus="openMenus" @on-tab-change="handleTabChange"
              @on-remove-tab="handleRemoveTab"></index-tab-bar>
          </div>
          <div style="width: 10px;">&nbsp;</div>
          <!-- Tab右栏 -->
          <div class="right-bar" style="">
            <img class="alert" src="./assets/img/tixing.svg" />
          </div>
        </div>
        <!-- 应用内容区 -->
        <div class="content-area">
          <!-- ica缓存机制 -->
          <index-content-area v-show="activeAppCacheType === 'ica'" ref="indexContentArea0"
            @on-open-menu="openApp($event.name)"></index-content-area>
          <!-- rka缓存机制 -->
          <router-view v-show="activeAppCacheType == null || activeAppCacheType === 'rka'" name="app"
            v-slot="{ Component }">
            <keep-alive>
              <component :is="Component"></component>
            </keep-alive>
          </router-view>
        </div>
        <div class="content-area-bottom">&nbsp;</div>
        <!-- 底部边栏 -->
        <div style="height: 10px;">&nbsp;</div>
      </div>
      <div class="inner-border-bar-right" style="width: 10px;">&nbsp;</div>
    </div>
    <!-- 右侧边栏 -->
    <div style="width: 15px;">&nbsp;</div>
    <login-win ref="loginWin0"></login-win>
  </div>
</template>

<script>
// element-ui-plus
// import 'element-plus/dist/index.css'

// element-ui
// import ElementUI from 'element-ui';
// import 'element-ui/lib/theme-chalk/index.css';
// Vue.use(ElementUI);

import { defineAsyncComponent, ref } from 'vue';

import LoginService from "../../code/module/platform/service/LoginService.js";
import LoginViewModel from "../../code/module/platform/view-model/LoginViewModel.js";
import indexLeftBarMixin from "./mixin/index-left-bar-mixin.js";
import indexMenuMixin from "./mixin/index-menu-mixin.js";

import IndexLeftMenuBar from "./components/index-left-menu-bar.vue";
import IndexTopBarRight from "./components/index-top-bar-right.vue";
import IndexTabBar from "./components/index-tab-bar.vue";
// import IndexContentArea from "./components/index-content-area.vue";
import LoginWin from "../platform/components/login-win.vue";

import logoutIcon from "./assets/img/logout.svg";
import "./assets/css/index.less";
import MyResidentAppService from '../../code/module/cs-ai-client/service/MyResidentAppService';
import ResidentAppService from '../../code/module/cs-ai-client/service/ResidentAppService';
import AIMultiChatService from '../../code/module/cs-ai-client/service/AIMultiChatService';
import DataSourceMapper from "../../code/module/cs-data-source/DataSourceMapper";
import indexUtilMixin from "./mixin/index-util-mixin.js";
import IndexMenuTabViewModel from "../../code/module/cs-ai-client/view-model/IndexMenuTabViewModel.js";
import ShellMapper from "../../code/module/cs-ai-client/mapper/ShellMapper.js";

export default {
  mixins: [indexLeftBarMixin, indexMenuMixin, indexUtilMixin],
  components: {
    IndexLeftMenuBar,
    IndexTopBarRight,
    IndexTabBar,
    'index-content-area': defineAsyncComponent(() => import('./components/index-content-area.vue')),
    LoginWin, TopBarRight: IndexTopBarRight, LeftMenuBar: IndexLeftMenuBar
  },
  data() {
    return {
      loading: false,
      leftBarLoading: false,

      loadDataDone: false,
      logoutIcon,
      loginUserRealname: '',

      myResidentAppVersion: null,

      appCache: {},

      ui: {
        height: 0,
        leftMenuBarWidth: 150,
      }
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
    async onShowed(pars) {
      // console.log('index.onShowed')
      const self = this;
      if (pars == null) pars = {};

      self.loading = true

      let ctx = this.getCtx();

      if (ctx.shellMapper == null) ctx.shellMapper = new ShellMapper(ctx)
      if (ctx.dataSourceMapper == null) ctx.dataSourceMapper = new DataSourceMapper(ctx)

      if (ctx.loginService == null) {
        ctx.loginService = new LoginService(ctx)
        await ctx.loginService.init()
      }

      if (ctx.residentAppService == null) ctx.residentAppService = new ResidentAppService(ctx)
      if (ctx.myResidentAppService == null) ctx.myResidentAppService = new MyResidentAppService(ctx)
      if (ctx.aiMultiChatService == null) ctx.aiMultiChatService = new AIMultiChatService(ctx)

      if (ctx.loginViewModel == null) ctx.loginViewModel = new LoginViewModel(ctx, { self })
      if (ctx.indexMenuTabViewModel == null) ctx.indexMenuTabViewModel = new IndexMenuTabViewModel(ctx)

      window.ctx = ctx;
      window.shellMapper = ctx.shellMapper;

      // 登录检查
      console.log(`登录检查...`)
      await ctx.loginViewModel.checkLogin({
        readyCb() {
          self.loading = false
        },
        cb(pars1) {
          self.loginUserRealname = pars1.loginUser.realname;
        }
      });

      if (ctx.cacIndexView == null) ctx.cacIndexView = self

      self.loading = true
      // 加载数据
      console.log(`加载数据...`)
      await self.loadData();
      self.loadDataDone = true;

      // 首次打开的菜单
      if (self.firstOpenMenus.length > 0) {
        self.firstOpenMenus.forEach((n) => {
          self.openApp(n);
        })
      }
      self.loading = false

      this.$refs.indexTabBar0.onShowed();
      this.$refs.indexLeftMenuBar0.onShowed({ items: self.leftMenus });

      this.$nextTick(() => {
        // 直接通过链接打开时
        ctx.indexMenuTabViewModel.onOpenByRoutePath(this)
      })
    },
    // loadMockData() {
    //   this.leftMenus = [
    //     { type: null, label: '首页', name: 'home', app_comp_name: null, closeable: false },
    //     { type: 'item', label: '虚拟专员', name: 'cs-mini-agent', app_comp_name: 'cs-mini-agent', icon: 'el-icon-s-order', closeable: true },
    //     { type: 'item', label: '应用中心', name: 'app-center', app_comp_name: 'app-center', icon: 'el-icon-menu', closeable: true },
    //   ];
    //   this.firstOpenMenus = ['home'];
    // },
    async loadLeftMenus(myResidentAppResult) {
      this.leftBarLoading = true
      let leftMenus = []

      // 加载我的常驻应用
      if (myResidentAppResult == null) {
        myResidentAppResult = await this.getCtx().myResidentAppService.loadData()
      }
      this.myResidentAppVersion = myResidentAppResult.version
      this.getCtx().myResidentAppResult = myResidentAppResult

      // 所有应用
      const allApps = this.getCtx().$appCenterList
      // ...
      leftMenus = myResidentAppResult.items.map((n) => {
        // 是收藏的子应用
        if (n.id == null) {
          return {
            type: 'menu-item',
            closeable: true,
            isCollect: true,
            ...n,
          }
        }
        else {
          const findApp = allApps.find((a) => {
            return a.id === n.id
          })
          if (findApp) {
            return {
              type: 'menu-item',
              label: findApp.label,
              id: findApp.id,
              name: findApp.name,
              app_comp_name: findApp.app_comp_name,
              icon: findApp.icon,
              closeable: true
            }
          }
          else {
            console.log('all apps: ', allApps)
            console.log('myResApp', n)
          }
        }
      })
      leftMenus = leftMenus.filter((n) => {
        return n != null
      })
      leftMenus.push({ type: 'menu-item', label: '应用中心', name: 'app-center', app_comp_name: 'app-center', icon: 'el-icon-menu', closeable: true })
      this.leftBarLoading = false

      this.leftMenus = leftMenus;

      this.$refs.indexLeftMenuBar0.onShowed({ items: leftMenus });
    },
    async loadData() {
      // this.loadMockData();

      this.loading = true
      const r = await this.getCtx().dataSourceMapper.getAppCenterList()
      this.getCtx().$appCenterList = r.data
      this.getCtx().$appCenterList.push({ label: '应用中心', name: 'app-center', app_comp_name: 'app-center', icon: 'el-icon-menu', hideInAppCenter: true })
      this.loading = false

      await this.loadLeftMenus()
    },
    // 注册应用起始页
    async registerAppStartPage(pars = {}) {
      await this.waitLoadDataDone();

      // let appName = this.$route.path.replace('/cac/app/', '');
      // const findLeftMenu = this.getLeftMenuByName(appName);

      let cacheKey = pars.name;
      if (pars.openAppName) {
        cacheKey = pars.openAppName;
      }
      console.log(`注册应用起始页：${cacheKey}`, pars)
      this.appCache[cacheKey] = {
        name: pars.name,
        openAppName: pars.openAppName,
        view: pars.view,
      };

      // // 首次打开触发onActivated
      // this.$nextTick(() => {
      //   if (pars.view.onActivated) {
      //     if (this.$route.path.indexOf(`/${pars.name}/`) !== -1) {
      //       const arr = this.$route.path.split('/');
      //       const childAppId = arr[arr.length - 1];
      //       console.log(`首次打开触发onActivated：${childAppId}`, pars.view)
      //       pars.view.onActivated({
      //         childAppId: childAppId,
      //       });
      //     }
      //     else if (this.$route.path.indexOf(`/${pars.name}`) !== -1) {
      //       console.log(`首次打开触发onActivated`, pars.view)
      //       pars.view.onActivated({
      //       });
      //     }
      //   }
      // })

      if (pars.cb) {
        pars.cb();
      }
    },
    // 登出
    handleLogout() {
      // 调用登出服务
      this.getCtx().loginService.logout().then(() => {
        // 登出成功后重定向到登录页
        window.location.reload();
      });
    },
    // 常驻菜单顺序发生了改变
    async handleMenuOrderChange(leftMenus) {
      // 更新
      const r = await this.getCtx().myResidentAppService.saveData({
        version: this.myResidentAppVersion,
        // 转换为我的常驻应用列表
        items: leftMenus.filter((n) => n.name !== 'app-center').map((n) => {
          if (n.isCollect) {
            return {
              name: n.name,
              app_comp_name: n.app_comp_name,
              label: n.label,
              icon: n.icon,
              params: n.params
            }
          }

          return {
            id: n.id,
            name: n.name,
            app_comp_name: n.app_comp_name,
          }
        })
      })
      this.myResidentAppVersion = r.version
    },
    // ...
    onScroll() {
      const self = this;
    },
    onResize() {
      const self = this;
      self.ui.height = document.documentElement.clientHeight;
      self.onScroll();
    },
  },
  async mounted() {
    const self = this;

    // ...
    window.addEventListener('resize', self.onResize, false);
    window.addEventListener('scroll', self.onScroll, false);

    self.$nextTick(function () {
      self.onResize();
    });

    self.$nextTick(function () {
      // 添加F5刷新事件监听
      document.addEventListener('keydown', function (event) {
        if (event.key === 'F5') {
          event.preventDefault(); // 阻止默认的F5刷新行为
          location.reload(); // 手动刷新页面
        }
      });
    });

    await self.onShowed();
  },
  // async updated() {
  //   const self = this;
  //   console.log('updated')

  //   // await self.onShowed();
  // },
  destroyed: function () {
    const self = this;
    // ...
    window.removeEventListener('resize', self.onResize, false);
    window.removeEventListener('scroll', self.onScroll, false);
  }
}
</script>

<style scoped lang="less"></style>

<style lang="less">
:root {
  --el-component-size-small: 34px;
}
.el-select--small .el-select__wrapper {
  height: 34px;
}
</style>