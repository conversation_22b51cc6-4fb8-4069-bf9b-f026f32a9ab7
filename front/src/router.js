import { createRouter, createWebHashHistory } from 'vue-router'

// 
const routes = [
  {
    path: '/',
    // name: 'Home',
    components: {
      main: () => import('./pages/cs-ai-client/index.vue')
    }
  },
  // {
  //   path: '/cma',
  //   // name: 'CSMiniAgent',
  //   // component: () => import('./pages/cs-mini-agent/index.vue')
  //   components: {
  //     main: () => import('./pages/cs-mini-agent/index.vue')
  //   }
  // },
  // {
  //   path: '/cma/test-multi-chat',
  //   name: '多模型聊天测试',
  //   // component: () => import('./pages/cs-mini-agent/index.vue')
  //   components: {
  //     main: () => import('./pages/cs-mini-agent/test-multi-chat.vue')
  //   }
  // },
  // {
  //   path: '/cma/test-gcwgsb',
  //   name: '工程违规识别测试',
  //   // component: () => import('./pages/cs-mini-agent/index.vue')
  //   components: {
  //     main: () => import('./pages/cs-mini-agent/test-gcwgsb.vue')
  //   }
  // },
  {
    path: '/cac',
    name: 'CSAIClient',
    // component: () => import('./pages/cs-ai-client/index.vue')
    components: {
      main: () => import('./pages/cs-ai-client/index.vue')
    },
    children: [
      // 空页面
      {
        path: 'app/empty',
        components: {
          app: () => import('./pages/cs-ai-client/app-comps/empty.vue')
        }
      },
      // 通过url嵌入的应用
      {
        path: 'url-app/:appName?',
        components: {
          app: () => import('./pages/cs-ai-client/app-comps/empty.vue')
        }
      },
      // 应用中心
      {
        path: 'app/app-center',
        components: {
          app: () => import('./pages/cs-ai-client/app-comps/app-center-list.vue')
        }
      },
      // 一屏多聊
      {
        path: 'app/ai-multi-chat/:childAppId?',
        components: {
          // app: () => import('./pages/cs-ai-client/app-comps/ai-multi-chat.vue')
          app: () => import('./pages/cs-ai-client/app-comps/empty.vue')
        }
      },
      // // 虚拟专员
      // {
      //   path: 'app/cs-mini-agent',
      //   components: {
      //     app: () => import('./pages/cs-mini-agent/index.vue')
      //   }
      // },
      // CLI工作流
      {
        path: 'app/cli-workflow',
        components: {
          app: () => import('./pages/cs-ai-client/app-comps/module/cli-workflow/cli-workflow.vue')
        }
      },
      // SCR工作流
      {
        path: 'app/scr-workflow',
        components: {
          app: () => import('./pages/cs-ai-client/app-comps/module/scr-workflow/scr-workflow.vue')
        }
      },
      // {
      //   path: 'app/ai-multi-chat',
      //   components: {
      //     app: () => import('./pages/cs-ai-client/app-comps/ai-multi-chat.vue')
      //   }
      // },
    ]
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router