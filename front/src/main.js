
import { ClientDataService } from "./code/module/platform/service/ClientDataService.js";
import { CACShellMapper } from "./code/mapper/CACShellMapper.js";
// import ClientDataMapper from "./code/module/cs-ai-client/mapper/ClientDataMapper";

const ctx = {
  navigatorName: null,
  imports: {
  },
};
ctx.clientDataService = new ClientDataService(ctx);
ctx.cacShellMapper = new CACShellMapper();
// ctx.clientDataMapper = new ClientDataMapper(ctx);

// *** vue3 ***
import { createApp } from 'vue'
import App from './app.vue'

const app = createApp(App)

// 注册ctx到Vue
app.config.globalProperties.$ctx = ctx;

// 注册路由
import router from './router'
app.use(router);

// 注册ElementPlus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
app.use(ElementPlus);
// 注册ElementPlus图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')



// // *** vue2 ***
// import Vue from 'vue'
// import App from './App.vue'

// // 加入pinia
// import { createPinia, PiniaVuePlugin } from 'pinia'
// Vue.use(PiniaVuePlugin)
// const pinia = createPinia()

// new Vue({
//   router,
//   // pinia,
//   render: h => h(App)
// }).$mount('#app')