export default class ReadTmpDatNodeProcessor {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async start(startParams, preNodeParams, nodeParams, opts = {}) {
        const result = {
            canNext: true,
            effect: -1,
            params: {
                ...preNodeParams,
            },
        };

        if (opts.onNodeDoing) {
            opts.onNodeDoing({
                nodeLabel: nodeParams.label,
                label: `开始执行`,
                data: null,
            });
        }

        try {
            const tgtName = opts.executor._replaceInnerParams(nodeParams.name, startParams, preNodeParams, nodeParams);
            let key = 'data';
            if (tgtName && tgtName.length > 0) {
                key = tgtName
            }
            const r = await this.ctx.dataSourceMapper.getUserData(key, {
                appKey: 'cac_user_tmp_dat',
            })

            if (r == null || r.data == null) {
                result.effect = -1;

                if (opts.onNodeDone) {
                    opts.onNodeDone({
                        nodeLabel: nodeParams.label,
                        label: `执行失败`,
                        data: `执行失败 -> 没有读取到数据`,
                    });
                }
            }
            else {
                if (opts.onNodeDoing) {
                    opts.onNodeDoing({
                        nodeLabel: nodeParams.label,
                        label: `执行中`,
                        data: (r.data ?? '').substring(0, 50) + '...',
                    });
                }

                result.params.__data = r.data;
                result.effect = 1;

                if (opts.onNodeDone) {
                    opts.onNodeDone({
                        nodeLabel: nodeParams.label,
                        label: `执行完毕（字数：${r.data.length}）`,
                        data: null,
                    });
                }
            }
        }
        catch (exc) {
            result.canNext = false;
            result.effect = -1;

            if (opts.onNodeError) {
                opts.onNodeError({
                    nodeLabel: nodeParams.label,
                    label: `执行异常`,
                    data: `执行异常 -> ${exc.message}`,
                });
            }
        }


        return result;
    }
}