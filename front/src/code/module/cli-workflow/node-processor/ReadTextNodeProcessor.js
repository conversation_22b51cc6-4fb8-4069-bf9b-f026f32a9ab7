export default class ReadTextNodeProcessor {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async start(startParams, preNodeParams, nodeParams, opts = {}) {
        const result = {
            canNext: true,
            effect: -1,
            params: {
                ...preNodeParams,
            },
        };

        try {
            if (nodeParams.data && nodeParams.data.length > 0) {
                result.params.__data = opts.executor._replaceInnerParams(nodeParams.data, startParams, preNodeParams, nodeParams);
            }
            else {
                result.params.__data = '';
            }
            result.effect = 1;

            if (opts.onNodeDone) {
                opts.onNodeDone({
                    nodeLabel: nodeParams.label,
                    label: `读取文本完毕（字数：${nodeParams.data.length}）`,
                    data: null,
                });
            }
        }
        catch (exc) {
            result.canNext = false;
            result.effect = -1;

            if (opts.onNodeError) {
                opts.onNodeError({
                    nodeLabel: nodeParams.label,
                    label: `读取文本异常`,
                    data: `读取文本异常 -> ${exc.message}`,
                });
            }
        }

        return result;
    }
}