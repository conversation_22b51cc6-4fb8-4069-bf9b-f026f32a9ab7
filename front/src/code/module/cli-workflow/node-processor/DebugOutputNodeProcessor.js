export default class DebugOutputNodeProcessor {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async start(startParams, preNodeParams, nodeParams, opts = {}) {
        const result = {
            canNext: true,
            effect: -1,
            params: {
                ...preNodeParams,
            },
        };

        try {
            let inputData = preNodeParams.__data;

            if (typeof(inputData) !== 'string') {
                inputData = JSON.stringify(inputData);
            }

            if (opts.onNodeDoing) {
                opts.onNodeDoing({
                    nodeLabel: nodeParams.label,
                    label: `执行中`,
                    data: `【上一个节点参数】\n${JSON.stringify(preNodeParams)}\n\n`,
                });
            }

            if (opts.onNodeDoing) {
                opts.onNodeDoing({
                    nodeLabel: nodeParams.label,
                    label: `执行中`,
                    data: `【上一个节点内容】\n${'```'}\n${inputData}\n${'```'}\n\n`,
                });
            }

            if (opts.onNodeDone) {
                opts.onNodeDone({
                    nodeLabel: nodeParams.label,
                    label: `调试输出完毕`,
                    data: null,
                });
            }
        } catch (exc) {
            result.canNext = false;
            result.effect = -1;

            if (opts.onNodeError) {
                opts.onNodeError({
                    nodeLabel: nodeParams.label,
                    label: `调试输出异常`,
                    data: `调试输出异常 -> ${exc.message}`,
                });
            }
        }

        return result;
    }
}