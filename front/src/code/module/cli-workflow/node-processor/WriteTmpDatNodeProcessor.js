export default class WriteTmpDatNodeProcessor {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async start(startParams, preNodeParams, nodeParams, opts = {}) {
        const result = {
            canNext: true,
            effect: -1,
            params: {
                ...preNodeParams,
            },
        };

        if (opts.onNodeDoing) {
            opts.onNodeDoing({
                index: preNodeParams.__index,
                nodeLabel: nodeParams.label,
                label: `开始执行`,
                data: null,
            });
        }

        try {
            const tgtName = opts.executor._replaceInnerParams(nodeParams.name, startParams, preNodeParams, nodeParams);
            const srcName = opts.executor._replaceInnerParams(nodeParams.src_name, startParams, preNodeParams, nodeParams);
            const save_data = opts.executor._replaceInnerParams(nodeParams.save_data, startParams, preNodeParams, nodeParams);
            const mode = nodeParams.mode;
            let toWriteData = preNodeParams.__data;

            let key = 'data';
            if (tgtName && tgtName.length > 0) {
                key = tgtName;
            }

            if (save_data && save_data.length > 0) {
                toWriteData = save_data;
            }
            else if (srcName && srcName.length > 0) {
                if (opts.onNodeDoing) {
                    opts.onNodeDoing({
                        index: preNodeParams.__index,
                        nodeLabel: nodeParams.label,
                        label: `执行中`,
                        data: `从“${srcName}”获取\n`,
                    });
                }

                toWriteData = preNodeParams[srcName];
            }

            if (toWriteData == null) toWriteData = '';

            // 添加模式
            if (mode === 'append') {
                let str = '';
                const r = await this.ctx.dataSourceMapper.getUserData(key, {
                    appKey: 'cac_user_tmp_dat',
                });
                if (r && r.data) {
                    str = r.data;
                }
                toWriteData = str + toWriteData;

                if (opts.onNodeDoing) {
                    opts.onNodeDoing({
                        index: preNodeParams.__index,
                        nodeLabel: nodeParams.label,
                        label: `执行中【添加】（字数：${(toWriteData + '').length}）`,
                        data: `执行中【添加】`,
                    });
                }

                await this.ctx.dataSourceMapper.setUserData(key, toWriteData, {
                    appKey: 'cac_user_tmp_dat',
                })
            }
            // 覆盖模式
            else {
                if (opts.onNodeDoing) {
                    opts.onNodeDoing({
                        index: preNodeParams.__index,
                        nodeLabel: nodeParams.label,
                        label: `执行中【覆盖】（字数：${(toWriteData + '').length}）`,
                        data: `执行中【覆盖】`,
                    });
                }

                await this.ctx.dataSourceMapper.setUserData(key, toWriteData, {
                    appKey: 'cac_user_tmp_dat',
                })
            }

            result.effect = 1;
            result.params.__data = '';

            if (opts.onNodeDone) {
                const maxSize = 50;
                const length = (toWriteData + '').length;

                if (length > maxSize) {
                    opts.onNodeDone({
                        index: preNodeParams.__index,
                        nodeLabel: nodeParams.label,
                        label: `执行完毕（字数：${length}）`,
                        data: `${(toWriteData + '').substring(0, maxSize)}...`,
                    });
                }
                else {
                    opts.onNodeDone({
                        index: preNodeParams.__index,
                        nodeLabel: nodeParams.label,
                        label: `执行完毕（字数：${length}）`,
                        data: toWriteData,
                    });
                }
            }
        }
        catch (exc) {
            result.canNext = false;
            result.effect = -1;

            if (opts.onNodeError) {
                opts.onNodeError({
                    index: preNodeParams.__index,
                    nodeLabel: nodeParams.label,
                    label: `执行异常`,
                    // data: `写入临时数据异常 -> ${exc.message}`,
                    data: `执行异常 -> ${exc.message} -> ${exc.stack}`,
                });
            }
        }

        return result;
    }
}