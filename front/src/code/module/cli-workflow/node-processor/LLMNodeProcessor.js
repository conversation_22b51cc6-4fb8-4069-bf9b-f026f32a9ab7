import CBAIOChatModelMapper from "../../../mapper/CBAIOChatModelMapper";
import {clearThinkContent} from "../../../util/ai-reply-util.js";

export default class LLMNodeProcessor {
    constructor(ctx) {
        this.ctx = ctx;
        this.cbaioChatModelMapper = new CBAIOChatModelMapper(ctx);
    }

    async start(startParams, preNodeParams, nodeParams, opts = {}) {
        const self = this;
        const result = {
            canNext: true,
            effect: -1,
            params: {
                ...preNodeParams,
            },
        };

        // console.log(`【LLMNodeProcessor】${JSON.stringify(preNodeParams)}`)

        if (opts.onNodeDoing) {
            opts.onNodeDoing({
                index: preNodeParams.__index,
                nodeLabel: nodeParams.label,
                label: `理解需求中`,
                data: null,
            });
        }

        if (nodeParams.user_prompt == null || nodeParams.user_prompt.length === 0) {
            nodeParams.user_prompt = '{input-data}';
        }

        // 通过选择获得的模型
        let model = nodeParams.model;

        let system_prompt = opts.executor._replaceInnerParams(nodeParams.system_prompt, startParams, preNodeParams, nodeParams);
        const user_prompt = opts.executor._replaceInnerParams(nodeParams.user_prompt, startParams, preNodeParams, nodeParams);
        const output_type = nodeParams.output_type;
        // 直接设置模型值
        if (nodeParams.model_1 && nodeParams.model_1.length > 0) {
            model = opts.executor._replaceInnerParams(nodeParams.model_1, startParams, preNodeParams, nodeParams);
        }

        system_prompt += `\n\n\n回复不要有任何解释，直接输出结果。\n`;

        const messages = [];
        messages.push({ role: 'system', content: system_prompt });
        messages.push({ role: 'user', content: user_prompt });

        if (opts.onNodeDoing) {
            opts.onNodeDoing({
                index: preNodeParams.__index,
                nodeLabel: nodeParams.label,
                label: `开始生成内容...`,
                data: `开始使用模型“${model}”生成内容...\n`,
            });
        }

        await new Promise((resolve) => {
            let totalChatReply = '';
            self.cbaioChatModelMapper.chatStream(model, messages, (reply) => {
                totalChatReply += reply;

                if (opts.onNodeDoing) {
                    opts.onNodeDoing({
                        index: preNodeParams.__index,
                        nodeLabel: nodeParams.label,
                        label: `生成内容中（字数：${totalChatReply.length}）`,
                        data: reply,
                    });
                }
            }, () => {
                try {
                    totalChatReply = clearThinkContent(totalChatReply);

                    result.effect = 1;
                    if (output_type === 'list') {
                        let list = JSON.parse(totalChatReply);
                        result.params.__data = list;
                        result.params.__data_type = 'list';
                    }
                    else {
                        result.params.__data = totalChatReply;
                    }

                    if (opts.onNodeDone) {
                        opts.onNodeDone({
                            index: preNodeParams.__index,
                            nodeLabel: nodeParams.label,
                            label: `生成完成（字数：${totalChatReply.length}）`,
                            data: null,
                        });
                    }
                } catch (exc) {
                    if (opts.onNodeError) {
                        opts.onNodeError({
                            index: preNodeParams.__index,
                            nodeLabel: nodeParams.label,
                            label: `生成内容失败`,
                            data: `生成内容失败 -> ${exc.message}`,
                            error: exc,
                        });
                    }
                }

                resolve();
            }, (err) => {
                result.canNext = false;
                result.effect = -1;

                if (opts.onNodeError) {
                    opts.onNodeError({
                        index: preNodeParams.__index,
                        nodeLabel: nodeParams.label,
                        label: `生成内容失败`,
                        data: `生成内容失败 -> ${err.message}`,
                        error: err,
                    });
                }

                resolve();
            })
        });

        return result;
    }
}