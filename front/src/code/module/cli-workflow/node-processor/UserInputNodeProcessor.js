export default class UserInputNodeProcessor {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async start(startParams, preNodeParams, nodeParams, opts = {}) {
        const result = {
            canNext: true,
            effect: -1,
            params: {
                ...preNodeParams,
            },
        };

        if (opts.onNodeDoing) {
            opts.onNodeDoing({
                nodeLabel: nodeParams.label,
                label: `等待用户输入`,
                data: null,
            });
        }

        try {
            let designStr = nodeParams.design;
            let design = {
                items: []
            };

            designStr = designStr
                .replace(/[}][\s\n]+\]/g, '}]')
                .replace(/\[[\s\n]+[{]/g, '[{')
                .replace(/[,][\s\n]+[{]/g, ',{')
                .replace(/[}][,][\s\n]+/g, '},')

            designStr.split('\n').forEach((line) => {
                const line1 = `(${line})`;
                const obj = eval(line1);
                if (obj.type === 'design') {
                    for (const prop in obj) {
                        if (prop === 'type') {
                        }
                        else {
                            design[prop] = obj[prop];
                        }
                    }
                }
                else {
                    design.items.push(obj);
                }
            })

            const form = await opts.waitUserInput({
                design,
            });
            if (form === false) {
                result.canNext = false;
                result.effect = -1;

                console.log(`用户输入取消`);

                if (opts.onNodeDone) {
                    opts.onNodeDone({
                        nodeLabel: nodeParams.label,
                        label: `用户输入取消`,
                        data: null,
                    });
                }
            }
            else if (form) {
                result.params.__data = form;
                for (const prop in form) {
                    result.params[prop] = form[prop];
                }

                result.effect = 1;

                if (opts.onNodeDone) {
                    opts.onNodeDone({
                        nodeLabel: nodeParams.label,
                        label: `用户输入完毕`,
                        data: null,
                    });
                }
            }
        } catch (exc) {
            result.canNext = false;
            result.effect = -1;

            if (opts.onNodeError) {
                opts.onNodeError({
                    nodeLabel: nodeParams.label,
                    label: `执行异常`,
                    data: `执行异常 -> ${exc.message}`,
                });
            }
        }

        return result;
    }
}