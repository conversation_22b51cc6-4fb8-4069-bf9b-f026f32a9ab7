import {getLimitStr} from "../../../util/str-util.js";

/**
 * 读取本地文件内容
 */
export default class ReadLocalFileNodeProcessor {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async start(startParams, preNodeParams, nodeParams, opts = {}) {
        const result = {
            canNext: true,
            effect: -1,
            params: {
                ...preNodeParams,
            },
        };

        if (opts.onNodeDoing) {
            opts.onNodeDoing({
                nodeLabel: nodeParams.label,
                label: `开始执行`,
                data: null,
            });
        }

        try {
            let filePath = opts.executor._replaceInnerParams(nodeParams.path, startParams, preNodeParams, nodeParams);
            const inputData = preNodeParams.__data;

            if (opts.onNodeDoing) {
                opts.onNodeDoing({
                    nodeLabel: nodeParams.label,
                    label: `读取中`,
                    data: `路径：${filePath}\n\n`,
                });
            }

            const str = await this.ctx.shellMapper.readStrFromFile(filePath);
            result.params.__data = str;

            if (opts.onNodeDone) {
                opts.onNodeDone({
                    nodeLabel: nodeParams.label,
                    label: `执行完毕`,
                    data: `路径：${filePath}\n内容：${getLimitStr(str, 50)}\n\n`,
                });
            }
        } catch (exc) {
            result.canNext = false;
            result.effect = -1;

            if (opts.onNodeError) {
                opts.onNodeError({
                    nodeLabel: nodeParams.label,
                    label: `执行异常`,
                    data: `执行异常 -> ${exc.message}`,
                });
            }
        }

        return result;
    }
}