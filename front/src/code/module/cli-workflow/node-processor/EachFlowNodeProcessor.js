export default class EachFlowNodeProcessor {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async start(startParams, preNodeParams, nodeParams, opts = {}) {
        const result = {
            canNext: true,
            effect: -1,
            params: {
                ...preNodeParams,
            },
        };

        if (opts.onNodeDoing) {
            opts.onNodeDoing({
                nodeLabel: nodeParams.label,
                label: `遍历流转中`,
                data: null,
            });
        }

        try {
            const inputData = preNodeParams.__data;
            result.effect = 1;
            result.toNextResults = [];
            let index = 0;
            let listSize = 0;

            for (const item of inputData) {
                listSize++;
            }
            for (const item of inputData) {
                result.toNextResults.push({
                    canNext: true,
                    effect: 1,
                    params: {
                        ...preNodeParams,
                        __data: item,
                        __index: index + 1,
                        __list_size: listSize,
                    }
                })

                // if (opts.onNodeDoing) {
                //     opts.onNodeDoing({
                //         nodeLabel: nodeParams.label,
                //         label: `遍历流转中`,
                //         data: `【遍历号-${index + 1}】\n${item}\n`,
                //     });
                // }

                index++;
            }

            if (opts.onNodeDone) {
                opts.onNodeDone({
                    nodeLabel: nodeParams.label,
                    label: `遍历流转完毕（长度：${result.toNextResults.length}）`,
                    data: `长度：${result.toNextResults.length}`,
                });
            }
        } catch (exc) {
            result.canNext = false;
            result.effect = -1;

            if (opts.onNodeError) {
                opts.onNodeError({
                    nodeLabel: nodeParams.label,
                    label: `遍历流转异常`,
                    data: `遍历流转异常 -> ${exc.message}`,
                });
            }
        }

        return result;
    }
}