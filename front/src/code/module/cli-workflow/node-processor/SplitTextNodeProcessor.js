export default class SplitTextNodeProcessor {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async start(startParams, preNodeParams, nodeParams, opts = {}) {
        const result = {
            canNext: true,
            effect: -1,
            params: {
                ...preNodeParams,
            },
        };

        if (opts.onNodeDoing) {
            opts.onNodeDoing({
                nodeLabel: nodeParams.label,
                label: `拆分文本中`,
                data: null,
            });
        }

        // console.log(`【SplitTextNodeProcessor】${JSON.stringify(preNodeParams)}`)

        try {
            const inputData = preNodeParams.__data;
            let list = [];

            // 把大纲字符串解析成一行行的数组
            let sections = inputData.split('\n');

            let index = 0;
            let findSection = false;
            let t_add_section = '';
            for (const sectionLine of sections) {
                if (sectionLine.startsWith('#### ')) {
                    findSection = true;

                    if (t_add_section.length > 0) {
                        list.push(t_add_section);

                        if (opts.onNodeDoing) {
                            opts.onNodeDoing({
                                nodeLabel: nodeParams.label,
                                label: `拆分文本中`,
                                data: `【拆分号-${index}】\n${(t_add_section ?? "").substring(0, 50)}...\n`,
                            });
                        }
                    }
                    t_add_section = '';
                }

                if (findSection) {
                    t_add_section += sectionLine + '\n';
                }
            }

            if (t_add_section.length > 0) {
                list.push(t_add_section);

                if (opts.onNodeDoing) {
                    opts.onNodeDoing({
                        nodeLabel: nodeParams.label,
                        label: `拆分文本中`,
                        data: `【拆分号-${index}】\n${(t_add_section ?? "").substring(0, 50)}...\n`,
                    });
                }
            }

            // 拆分后数组
            result.params.__data = list;
            // 拆分长度
            result.params.__length = list.length;
            result.effect = 1;

            if (opts.onNodeDone) {
                opts.onNodeDone({
                    nodeLabel: nodeParams.label,
                    label: `拆分文本完毕（长度：${list.length}）`,
                    data: null,
                });
            }
        } catch (exc) {
            result.canNext = false;
            result.effect = -1;

            if (opts.onNodeError) {
                opts.onNodeError({
                    nodeLabel: nodeParams.label,
                    label: `拆分文本异常`,
                    data: `拆分文本异常 -> ${exc.message}`,
                });
            }
        }

        return result;
    }
}