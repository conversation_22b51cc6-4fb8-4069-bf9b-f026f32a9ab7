export default class DemoNodeProcessor {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async start(startParams, preNodeParams, nodeParams, opts = {}) {
        const result = {
            canNext: true,
            effect: -1,
            params: {
                ...preNodeParams,
            },
        };

        if (opts.onNodeDoing) {
            opts.onNodeDoing({
                nodeLabel: nodeParams.label,
                label: `开始执行`,
                data: null,
            });
        }

        try {
            const inputData = preNodeParams.__data;

            if (opts.onNodeDoing) {
                opts.onNodeDoing({
                    nodeLabel: nodeParams.label,
                    label: `执行中`,
                    data: null,
                });
            }

            if (opts.onNodeDone) {
                opts.onNodeDone({
                    nodeLabel: nodeParams.label,
                    label: `执行完毕`,
                    data: null,
                });
            }
        } catch (exc) {
            result.canNext = false;
            result.effect = -1;

            if (opts.onNodeError) {
                opts.onNodeError({
                    nodeLabel: nodeParams.label,
                    label: `执行异常`,
                    data: `执行异常 -> ${exc.message}`,
                });
            }
        }

        return result;
    }
}