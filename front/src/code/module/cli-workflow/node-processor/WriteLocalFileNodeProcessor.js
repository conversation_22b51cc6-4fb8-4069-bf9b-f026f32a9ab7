import {getLimitStr} from "../../../util/str-util.js";
import {convertMarkdownToWord} from "../../../util/markdown-to-word-util.js";

/**
 * 写入内容到本地文件
 */
export default class WriteLocalFileNodeProcessor {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async start(startParams, preNodeParams, nodeParams, opts = {}) {
        const result = {
            canNext: true,
            effect: -1,
            params: {
                ...preNodeParams,
            },
        };

        if (opts.onNodeDoing) {
            opts.onNodeDoing({
                nodeLabel: nodeParams.label,
                label: `开始执行`,
                data: null,
            });
        }

        try {
            // 写入文件路径
            let filePath = opts.executor._replaceInnerParams(nodeParams.path, startParams, preNodeParams, nodeParams);
            // 来源字段名
            const srcName = opts.executor._replaceInnerParams(nodeParams.src_name, startParams, preNodeParams, nodeParams);
            // 转换类型
            const transform = opts.executor._replaceInnerParams(nodeParams.transform, startParams, preNodeParams, nodeParams);

            let inputData;
            if (srcName && srcName.length > 0) {
                inputData = preNodeParams[srcName];
            }
            else {
                inputData = preNodeParams.__data;
            }

            if (transform === 'md_to_doc') {
                inputData = convertMarkdownToWord(inputData);
                if (opts.onNodeDoing) {
                    opts.onNodeDoing({
                        nodeLabel: nodeParams.label,
                        label: `写入中`,
                        data: `路径：${filePath}\n内容（${transform}）：${getLimitStr(inputData, 50)}\n\n`,
                    });
                }
            }
            else {
                if (opts.onNodeDoing) {
                    opts.onNodeDoing({
                        nodeLabel: nodeParams.label,
                        label: `写入中`,
                        data: `路径：${filePath}\n内容：${getLimitStr(inputData, 50)}\n\n`,
                    });
                }
            }

            await this.ctx.shellMapper.writeStrToFile(filePath, inputData);

            if (opts.onNodeDone) {
                opts.onNodeDone({
                    nodeLabel: nodeParams.label,
                    label: `执行完毕`,
                    data: null,
                });
            }
        } catch (exc) {
            result.canNext = false;
            result.effect = -1;

            if (opts.onNodeError) {
                opts.onNodeError({
                    nodeLabel: nodeParams.label,
                    label: `执行异常`,
                    data: `执行异常 -> ${exc.message}`,
                });
            }
        }

        return result;
    }
}