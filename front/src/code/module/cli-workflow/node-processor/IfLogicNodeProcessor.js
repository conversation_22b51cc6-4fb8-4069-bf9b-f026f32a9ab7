/**
 * if判断节点
 */
export default class IfLogicNodeProcessor {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async start(startParams, preNodeParams, nodeParams, opts = {}) {
        const result = {
            canNext: true,
            effect: -1,
            params: {
                ...preNodeParams,
            },
        };

        if (opts.onNodeDoing) {
            opts.onNodeDoing({
                nodeLabel: nodeParams.label,
                label: `开始执行`,
                data: null,
            });
        }

        try {
            const inputData = preNodeParams.__data;
            // 表达式
            let expression = opts.executor._replaceInnerParams(nodeParams.expression, startParams, preNodeParams, nodeParams);

            if (opts.onNodeDoing) {
                opts.onNodeDoing({
                    nodeLabel: nodeParams.label,
                    label: `判断中`,
                    data: `表达式：${expression}\n\n`,
                });
            }

            if (eval(expression) === true) {
                result.effect = 1;

                if (opts.onNodeDoing) {
                    opts.onNodeDoing({
                        nodeLabel: nodeParams.label,
                        label: `判断中`,
                        data: `判断结果为真\n\n`,
                    });
                }
            }
            else {
                if (opts.onNodeDoing) {
                    opts.onNodeDoing({
                        nodeLabel: nodeParams.label,
                        label: `判断中`,
                        data: `判断结果为假\n\n`,
                    });
                }
            }

            if (opts.onNodeDone) {
                opts.onNodeDone({
                    nodeLabel: nodeParams.label,
                    label: `执行完毕`,
                    data: null,
                });
            }
        } catch (exc) {
            result.canNext = false;
            result.effect = -1;

            if (opts.onNodeError) {
                opts.onNodeError({
                    nodeLabel: nodeParams.label,
                    label: `执行异常`,
                    data: `执行异常 -> ${exc.message}`,
                });
            }
        }

        return result;
    }
}