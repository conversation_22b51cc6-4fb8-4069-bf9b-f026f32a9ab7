import CSWorkflowExecutor from "../cs-workflow/CSWorkflowExecutor";
import LLMNodeProcessor from "./node-processor/LLMNodeProcessor";
import ReadTextNodeProcessor from "./node-processor/ReadTextNodeProcessor";
import ReadTmpDatNodeProcessor from "./node-processor/ReadTmpDatNodeProcessor";
import WriteTmpDatNodeProcessor from "./node-processor/WriteTmpDatNodeProcessor";
import SplitTextNodeProcessor from "./node-processor/SplitTextNodeProcessor.js";
import EachFlowNodeProcessor from "./node-processor/EachFlowNodeProcessor.js";
import DebugOutputNodeProcessor from "./node-processor/DebugOutputNodeProcessor.js";
import UserInputNodeProcessor from "./node-processor/UserInputNodeProcessor.js";
import ReadLocalFileNodeProcessor from "./node-processor/ReadLocalFileNodeProcessor.js";
import WriteLocalFileNodeProcessor from "./node-processor/WriteLocalFileNodeProcessor.js";
import IfLogicNodeProcessor from "./node-processor/IfLogicNodeProcessor.js";

export default class CLIWorkflowExecutor extends CSWorkflowExecutor {
    constructor(ctx, runtimeData) {
        super(ctx, runtimeData);
        // 可以在此初始化 CreepWFExecutor 特有的属性
        this.nodeProcessorsMap = {};
    }
    
    async start(startParams, opts = {}) {
        return await super.start(startParams, opts);
    }

    /**
     * 处理节点（可根据需要重写）
     * @param {*} startParams 
     * @param {*} preNodeParams 
     * @param {*} node 
     * @param {*} opts 
     * @returns 
     */
    async procNode(startParams, preNodeParams, node, opts = {}) {
        let nodeProcessor = this.getNodeProcessor(node);

        if (nodeProcessor == null) {
            throw new Error(`未能根据“${node.type}”找到NodeProcessor`);
        }
        
        opts.node = node;
        return await nodeProcessor.start(startParams, preNodeParams, node.pars, opts);
    }

    getNodeProcessor(node) {
        if (this.nodeProcessorsMap[node.type] == null) {
            if (node.type === 'user-input') {
                this.nodeProcessorsMap[node.type] = new UserInputNodeProcessor(this.ctx);
            }
            else if (node.type === 'llm') {
                this.nodeProcessorsMap[node.type] = new LLMNodeProcessor(this.ctx);
            }
            else if (node.type === 'split-text') {
                this.nodeProcessorsMap[node.type] = new SplitTextNodeProcessor(this.ctx);
            }
            else if (node.type === 'if-logic') {
                this.nodeProcessorsMap[node.type] = new IfLogicNodeProcessor(this.ctx);
            }
            else if (node.type === 'each-flow') {
                this.nodeProcessorsMap[node.type] = new EachFlowNodeProcessor(this.ctx);
            }
            // *** 读写 ***
            else if (node.type === 'read-text') {
                this.nodeProcessorsMap[node.type] = new ReadTextNodeProcessor(this.ctx);
            }
            else if (node.type === 'read-local-file') {
                this.nodeProcessorsMap[node.type] = new ReadLocalFileNodeProcessor(this.ctx);
            }
            else if (node.type === 'write-local-file') {
                this.nodeProcessorsMap[node.type] = new WriteLocalFileNodeProcessor(this.ctx);
            }
            else if (node.type === 'read-tmp-dat') {
                this.nodeProcessorsMap[node.type] = new ReadTmpDatNodeProcessor(this.ctx);
            }
            else if (node.type === 'write-tmp-dat') {
                this.nodeProcessorsMap[node.type] = new WriteTmpDatNodeProcessor(this.ctx);
            }
            // *** 其它 ***
            else if (node.type === 'debug-output') {
                this.nodeProcessorsMap[node.type] = new DebugOutputNodeProcessor(this.ctx);
            }
        }

        return this.nodeProcessorsMap[node.type];
    }
}