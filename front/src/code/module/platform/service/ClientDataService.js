import {WebClientDataMapper} from "../mapper/WebClientDataMapper.js";

/**
 * 客户端数据服务
 */
export class ClientDataService {

    constructor(ctx) {
        this.ctx = ctx;

        this.clientDataMapper = new WebClientDataMapper(ctx);
    }

    async get(key) {
        return await this.clientDataMapper.get(key);
    }

    async set(key, value) {
        await this.clientDataMapper.set(key, value);
    }

    async remove(key) {
        await this.clientDataMapper.remove(key);
    }
}