export default class ScrWorkflowService {
    constructor(ctx, opts = {}) {
        this.ctx = ctx;
        this._appKey = 'scr_gzl';

        if (opts.appKey) {
            this._appKey = opts.appKey;
        }
    }

    /**
     *
     * @returns {Promise<*[]>}
     */
    async getList() {
        const self = this;
        const list = [];

        const loginedUser = await this.ctx.loginService.getUser();
        // ['xx', 'bb']
        const roles = loginedUser.roles;

        // 加载自己建的
        {
            const result = await this.ctx.dataSourceMapper.getExtAppDataList({
                appKey: self._appKey,
                values: {
                    create_user_id: loginedUser.id,
                },
                designs: {
                    create_user_id: {
                        matchMode: 0,
                        isBuildInField: true,
                    }
                }
            })
            if (result.success === false) throw new Error(result.msg)

            for (const item of result.data) {
                const sObj = JSON.parse(item.search);
                list.push({
                    id: item.id,
                    name: sObj.name,
                    main_body: sObj.main_body,
                    has_user_input: sObj.has_user_input,
                    changeUser: item.changeUser,
                    changeTime: item.changeTime,
                    createUser: item.createUser,
                    createTime: item.createTime,
                    can_see_roles: sObj.can_see_roles,
                    can_see_users: sObj.can_see_users,
                    can_exec_roles: sObj.can_exec_roles,
                    can_exec_users: sObj.can_exec_users,
                    can_edit_users: sObj.can_edit_users,
                    my: true,
                })
            }
        }
        // 加载挂钩用户的
        {
            const result = await this.ctx.dataSourceMapper.getExtAppDataList({
                appKey: self._appKey,
                values: {
                    can_see_users: loginedUser.id,
                },
                designs: {
                    can_see_users: {
                        matchMode: 1,
                    }
                }
            })
            if (result.success === false) throw new Error(result.msg)

            for (const item of result.data) {
                const findItem = list.find((n) => {
                    return n.id === item.id;
                });
                if (findItem == null) {
                    const sObj = JSON.parse(item.search);
                    list.push({
                        id: item.id,
                        name: sObj.name,
                        main_body: sObj.main_body,
                        has_user_input: sObj.has_user_input,
                        changeUser: item.changeUser,
                        changeTime: item.changeTime,
                        createUser: item.createUser,
                        createTime: item.createTime,
                        can_see_roles: sObj.can_see_roles,
                        can_see_users: sObj.can_see_users,
                        can_exec_roles: sObj.can_exec_roles,
                        can_exec_users: sObj.can_exec_users,
                        can_edit_users: sObj.can_edit_users,
                        my: false,
                    })
                }
            }
        }
        // 加载挂钩角色的
        if (roles && roles.length > 0) {
            let myRolesStr = '';
            for (let i = 0; i < roles.length; i++) {
                const role = roles[i];
                if (i > 0) {
                    myRolesStr += '|';
                }
                myRolesStr += role;
            }

            const result = await this.ctx.dataSourceMapper.getExtAppDataList({
                appKey: self._appKey,
                values: {
                    can_see_roles: myRolesStr,
                },
                designs: {
                    can_see_roles: {
                        matchMode: 5,
                    }
                }
            })
            if (result.success === false) throw new Error(result.msg)

            for (const item of result.data) {
                const findItem = list.find((n) => {
                    return n.id === item.id;
                });
                if (findItem == null) {
                    const sObj = JSON.parse(item.search);
                    list.push({
                        id: item.id,
                        name: sObj.name,
                        main_body: sObj.main_body,
                        has_user_input: sObj.has_user_input,
                        changeUser: item.changeUser,
                        changeTime: item.changeTime,
                        createUser: item.createUser,
                        createTime: item.createTime,
                        can_see_roles: sObj.can_see_roles,
                        can_see_users: sObj.can_see_users,
                        can_exec_roles: sObj.can_exec_roles,
                        can_exec_users: sObj.can_exec_users,
                        can_edit_users: sObj.can_edit_users,
                        my: false,
                    })
                }
            }
        }

        return list;
    }

    /**
     *
     * @param id
     * @returns {Promise<*>}
     */
    async getForm(id) {
        const self = this;
        const result = await this.ctx.dataSourceMapper.getExtAppDataForm({
            appKey: self._appKey,
            id: id,
        })
        if (result.success === false) throw new Error(result.msg)

        return result.data;
    }

    /**
     * 
     * @param {*} updType 
     * @param {*} form { id, search, content }
     */
    async saveForm(updType, form) {
        const self = this;
        const result = await this.ctx.dataSourceMapper.saveExtAppDataForm({
            updType,
            form: {
                appKey: self._appKey,
                ...form
            }
        })
        if (result.success === false) throw new Error(result.msg);
        return result.data;
    }

    /**
     *
     * @param id
     * @returns {Promise<void>}
     */
    async deleteForm(id) {
        const self = this;
        const result = await this.ctx.dataSourceMapper.deleteExtAppDataForm({
            appKey: self._appKey,
            id: id,
        })
        if (result.success === false) throw new Error(result.msg);
    }
}