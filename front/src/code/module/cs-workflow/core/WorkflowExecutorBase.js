export default class WorkflowExecutorBase {
    constructor(ctx, runtimeData) {
        this.ctx = ctx;
        this.runtimeData = runtimeData;
    }

    _threadProcNodeBefore(startParams, preNodeParams, node, preNodeList, cache, opts = {}) {
        if (node == null) {
            node = this._findNodeByType('start');
        }

        if (node.pathIndex == null) node.pathIndex = 0;
        opts.node = node;

        return {
            node,
            result: {
                canNext: true,
                params: {},
                effect: 1,
            }
        };
    }

    /**
     * 获取起始节点之后的节点
     * @returns {*[]}
     * @private
     */
    _findStartNextNodes() {
        const startNode = this._findNodeByType('start');
        if (startNode == null) {
            throw new Error('未找到起始节点');
        }
        return this._findNextNodes(startNode);
    }

    _findNodeByType(type) {
        return this.runtimeData.nodes.find(n => n.type === type);
    }

    _findNextNodes(node, effect) {
        const list = [];
        for (const link of this.runtimeData.links) {

            let matchLink = false;
            // 筛选消极路线
            if (effect === -1) {
                if (link.pars && link.pars.effect === -1) {
                    matchLink = true;
                }
            }
            // 筛选积极路线
            else {
                if (link.pars == null) {
                    matchLink = true;
                }
                else if (link.pars && (link.pars.effect == null || link.pars.effect === 1)) {
                    matchLink = true;
                }
            }

            if (matchLink && link.fromId === node.id) {
                for (const node of this.runtimeData.nodes) {
                    if (node.id === link.toId) {
                        list.push(node);
                        break;
                    }
                }
            }
        }
        return list;
    }

    _replaceInnerParams(inputText, startParams, preNodeParams, nodeParams) {
        if (inputText && inputText.length > 0) {
            if (inputText.indexOf('{wf-label}') !== -1) {
                inputText = inputText.replaceAll('{wf-label}', startParams.__workflow_label ?? '');
            }
            if (inputText.indexOf('{input-data}') !== -1) {
                inputText = inputText.replaceAll('{input-data}', preNodeParams.__data ?? '');
            }
            if (inputText.indexOf('{input-length}') !== -1) {
                inputText = inputText.replaceAll('{input-length}', preNodeParams.__length ?? '');
            }
            if (inputText.indexOf('{input-index}') !== -1) {
                inputText = inputText.replaceAll('{input-index}', preNodeParams.__index ?? '');
            }
            if (inputText.indexOf('{input-list-size}') !== -1) {
                inputText = inputText.replaceAll('{input-list-size}', preNodeParams.__list_size ?? '');
            }

            for (let i = 0; i < 30; i++) {
                if (inputText.indexOf('{input:') !== -1) {
                    const reg = /\{input:(.*?)\}/g;
                    const matches = inputText.matchAll(reg);
                    for (const match of matches) {
                        const key = match[1];
                        let inputValue = preNodeParams[key];
                        inputText = inputText.replaceAll(match[0], inputValue ?? '');
                    }
                }
                else {
                    break;
                }
            }
            if (inputText.indexOf('{input:') !== -1) {
                console.log(`未能替换干净：${inputText}`)
            }
        }
        return inputText;
    }
}