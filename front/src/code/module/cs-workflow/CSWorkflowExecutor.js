import WorkflowExecutorBase from './core/WorkflowExecutorBase.js';
export default class CSWorkflowExecutor extends WorkflowExecutorBase {
    constructor(ctx, runtimeData) {
        super(ctx, runtimeData);
    }

    /* --- 复写方法 --- */
    
    /**
     * 
     * @param {*} node 
     * @param {*} inputParams 
     * @returns 
     */
    async procNode(startParams, preNodeParams, node, opts = {}) {
        let canNext = true;
        const outputParams = {};
        let effect = 1;



        return {
            canNext,
            params: outputParams,
            effect,
        }
    }
    
    /* --- 系统方法 --- */

    async start(startParams, opts = {}) {
        opts.executor = this;
        
        const cache = {
            paths: []
        };
        
        await this._threadProcNode(startParams, {
            ...startParams
        }, null, [], cache, opts);
        return {
            cache,
        };
    }

    async stop() {
        this._req_stop = true;
    }

    async _threadProcNode(startParams, preNodeParams, node, preNodeList, cache, opts = {}) {
        let before = this._threadProcNodeBefore(startParams, preNodeParams, node, preNodeList, cache, opts);
        let result = before.result;
        node = before.node;

        if (node.type === 'start') {
        }
        else {
            try {
                result = await this.procNode(startParams, preNodeParams, node, {
                    ...opts,
                    preNodeList,
                });
            }
            catch(exc) {
                console.log(`procNode异常 => ${exc.message} -> ${node.type}|${JSON.stringify(node.pars)} -> ${exc.stack}`);
            }
        }

        // 当节点结束
        function onEnd() {
            cache.paths.push([...preNodeList, node])
        }

        if (this._req_stop === true) {
            onEnd();
        }
        else if (result.toNextResults) {
            for (const toNextResult of result.toNextResults) {
                const nextNodes = this._findNextNodes(node, toNextResult.effect);
                if (nextNodes && nextNodes.length > 0) {
                    let i = 0;
                    for (const nextNode of nextNodes) {
                        nextNode.pathIndex = node.pathIndex + i;
                        await this._threadProcNode(startParams, toNextResult.params, nextNode, [...preNodeList, node], cache, opts);
                        i++;
                    }
                }
                else {
                    onEnd();
                }
            }
        }
        else if (result.canNext) {
            const toNextResult = result;
            const nextNodes = this._findNextNodes(node, toNextResult.effect);
            if (nextNodes && nextNodes.length > 0) {
                let i = 0;
                for (const nextNode of nextNodes) {
                    nextNode.pathIndex = node.pathIndex + i;
                    await this._threadProcNode(startParams, toNextResult.params, nextNode, [...preNodeList, node], cache, opts);
                    i++;
                }
            }
            else {
                onEnd();
            }
        }
        else {
            onEnd();
        }
    }
}