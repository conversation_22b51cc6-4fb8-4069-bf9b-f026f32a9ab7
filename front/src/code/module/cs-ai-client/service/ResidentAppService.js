
/**
 * 常驻应用服务
 */
export default class ResidentAppService {
    constructor(ctx) {
        this.ctx = ctx;

        // 应用中心点击显示后是进入个人配置还是全局配置中
        // 0 个人配置，1 授权域配置
        this._type = 0;

        if (this._type === 0) {
            this._dataFuncName = 'UserData';
            this._dataKey = 'my_resident_apps';
        }
        else {
            this._dataFuncName = 'Setting';
            this._dataKey = 'resident_apps';
        }
    }

    /**
     * 加载数据
     * @returns 
     */
    async loadData() {
        const r = await this.ctx.dataSourceMapper[`get${this._dataFuncName}`](this._dataKey);
        const val = r.data

        if (val && val.length > 0) {
            return JSON.parse(val)
        }

        return {
            items: []
        }
    }

    /**
     * 保存数据
     * @param {*} data 
     */
    async saveData(data) {
        const latest = await this.loadData()

        if (latest.version > 0) {
            if (data.version !== latest.version) {
                throw new Error('版本不匹配，请重新加载后再操作');
            }
        }


        if (latest.version > 0) {
            data.version = latest.version + 1
        }
        else {
            data.version = 1
        }
        await this.ctx.dataSourceMapper[`set${this._dataFuncName}`](this._dataKey, JSON.stringify(data));
    }

    /**
     * 在左栏显示应用
     * @param {*} appItem 
     * @returns 
     */
    async enableApp(appItem) {
        const data = await this.loadData()
        
        if (data.items.find((n) => n.id === appItem.id)) {
            return;
        }

        data.items.splice(0, null, {
            id: appItem.id,
            name: appItem.name,
            app_comp_name: appItem.app_comp_name,
        });

        await this.saveData(data)
    }

    /**
     * 在左栏隐藏应用
     * @param {*} appItem 
     * @returns 
     */
    async disableApp(appItem) {
        const data = await this.loadData()
        
        for (const i in data.items) {
            const item = data.items[i]
            if (item.id === appItem.id) {
                data.items.splice(i, 1);
                break;
            }
        }

        await this.saveData(data)
    }
}