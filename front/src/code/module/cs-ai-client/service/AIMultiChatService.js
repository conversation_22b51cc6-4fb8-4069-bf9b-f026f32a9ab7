// import { cds_getUserData, cds_setUserData } from "../../cs-data-source/data-source-mapper";

/**
 * 一屏多聊服务
 */
export default class AIMultiChatService {
    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     * 加载数据
     * @returns 
     */
    async loadData() {
        let mra_result = {
            items: []
        }
        const r = await this.ctx.dataSourceMapper.getUserData('ai_multi_chat');
        const val = r.data

        if (val && val.length > 0) {
            mra_result = JSON.parse(val)
        }

        return mra_result
    }

    /**
     * 保存数据
     * @param {*} data 
     */
    async saveData(data) {
        // 加载数据源版本
        let mra_result = {
            items: []
        }
        const r = await this.ctx.dataSourceMapper.getUserData('ai_multi_chat');
        const val = r.data

        if (val && val.length > 0) {
            mra_result = JSON.parse(val)
        }
        const latest = mra_result

        // 对比数据源版本
        if (latest.version > 0) {
            if (data.version !== latest.version) {
                throw new Error('版本不匹配，请重新加载后再操作');
            }
        }

        // 更新版本
        if (latest.version > 0) {
            data.version = latest.version + 1
        }
        else {
            data.version = 1
        }
        await this.ctx.dataSourceMapper.setUserData('ai_multi_chat', JSON.stringify(data));

        return data;
    }
}