
/**
 * 我的常驻应用服务
 */
export default class MyResidentAppService {
    constructor(ctx) {
        this.ctx = ctx;

        // 应用中心点击显示后是进入个人配置还是全局配置中
        // 0 个人配置，1 授权域配置
        this._type = 0;

        if (this._type === 0) {
            this._dataFuncName = 'UserData';
        }
        else {
            this._dataFuncName = 'Setting';
        }
    }

    /**
     * 加载数据
     * @returns {MyResidentAppResult}
     */
    async loadData() {
        let mra_result = {
            items: []
        }

        const r = await this.ctx.dataSourceMapper[`get${this._dataFuncName}`]('my_resident_apps')
        const val = r.data

        if (val && val.length > 0) {
            mra_result = JSON.parse(val)
        }


        // 检查常驻应用是否都包含了
        const allAppsResult = await this.ctx.dataSourceMapper.getAppCenterList()
        const allApps = allAppsResult.data
        // for (const ra_item of allApps) {
        //     let find = false
        //     for (const mra_item of mra_result.items) {
        //         if (mra_item.app_comp_name === ra_item.app_comp_name) {
        //             find = true
        //             break
        //         }
        //     }
        //     if (!find) {
        //         mra_result.items.push({
        //             id: ra_item.id,
        //             name: ra_item.name,
        //             app_comp_name: ra_item.app_comp_name,
        //         })
        //     }
        // }
        // 检查是否多了不该有的（对比应用库）
        let toDelList = []
        for (const mra_item of mra_result.items) {
            let find = false
            for (const ra_item of allApps) {
                if (ra_item.app_comp_name === mra_item.app_comp_name) {
                    find = true
                    break
                }
            }
            if (!find) {
                toDelList.push(mra_item.id)
            }
        }
        // 检查是否多了不该有的子应用（对比常驻应用）
        for (const mra_item of mra_result.items) {
            if (mra_item.id == null) {
                let find = false
                for (const ra_item of mra_result.items) {
                    if (ra_item.id != null) {
                        if (ra_item.app_comp_name === mra_item.app_comp_name && ra_item.id != null) {
                            find = true
                            break
                        }
                    }
                }
                if (!find) {
                    toDelList.push(mra_item.id)
                }
            }
        }
        // 删除多出来的
        mra_result.items = mra_result.items.filter((n) => {
            for (const id of toDelList) {
                if (id === n.id) {
                    return false
                }
            }
            return true
        })


        return mra_result
    }

    /**
     * 保存数据
     * @param {*} data 
     * @returns {MyResidentAppResult}
     */
    async saveData(data) {
        // 加载数据源版本
        let mra_result = {
            items: []
        }
        const r = await this.ctx.dataSourceMapper[`get${this._dataFuncName}`]('my_resident_apps');
        const val = r.data

        if (val && val.length > 0) {
            mra_result = JSON.parse(val)
        }
        const latest = mra_result

        // 对比数据源版本
        if (latest.version > 0) {
            if (data.version !== latest.version) {
                throw new Error('版本不匹配，请重新加载后再操作');
            }
        }

        // 更新版本
        if (latest.version > 0) {
            data.version = latest.version + 1
        }
        else {
            data.version = 1
        }
        await this.ctx.dataSourceMapper[`set${this._dataFuncName}`]('my_resident_apps', JSON.stringify(data));
        return data
    }

    /**
     * 收藏
     * @param {*} appItem name, app_comp_name, label, icon, params
     * @returns {MyResidentAppResult}
     */
    async collect(appItem) {
        let data = {
            items: []
        }
        const r = await this.ctx.dataSourceMapper[`get${this._dataFuncName}`]('my_resident_apps');
        const val = r.data

        if (val && val.length > 0) {
            data = JSON.parse(val)
        }

        const findItem = data.items.find((n) => n.name === appItem.name)
        if (findItem) {
            findItem.app_comp_name = appItem.app_comp_name;
            findItem.label = appItem.label;
            findItem.icon = appItem.icon;
            findItem.params = appItem.params;
        }
        else {
            data.items.splice(0, null, {
                ...appItem
                // name: appItem.name,
                // app_comp_name: appItem.app_comp_name,
                // label: appItem.label,
            });
        }

        // 更新版本
        if (data.version > 0) {
            data.version = data.version + 1
        }
        else {
            data.version = 1
        }
        await this.ctx.dataSourceMapper[`set${this._dataFuncName}`]('my_resident_apps', JSON.stringify(data));
        return data
    }

    /**
     * 
     * @param {*} appItem name
     */
    isCollect(appItem) {
        return this.ctx.myResidentAppResult.items.find((n) => n.name === appItem.name) != null
    }

    /**
     * 取消收藏
     * @param {*} appItem name
     * @returns {MyResidentAppResult}
     */
    async discard(appItem) {
        let data = {
            items: []
        }
        const r = await this.ctx.dataSourceMapper[`get${this._dataFuncName}`]('my_resident_apps');
        const val = r.data

        if (val && val.length > 0) {
            data = JSON.parse(val)
        }

        for (const i in data.items) {
            const item = data.items[i]
            if (item.name === appItem.name) {
                data.items.splice(i, 1)
                break;
            }
        }

        // 更新版本
        if (data.version > 0) {
            data.version = data.version + 1
        }
        else {
            data.version = 1
        }
        await this.ctx.dataSourceMapper[`set${this._dataFuncName}`]('my_resident_apps', JSON.stringify(data));
        return data
    }
}