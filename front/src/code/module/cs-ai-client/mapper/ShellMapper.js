/**
 * 壳映射
 */
export default class ShellMapper {

    constructor(ctx) {
        this.ctx = ctx
    }

    /**
     * 判定当前是否在壳中运行
     * @returns {boolean}
     */
    isInShell() {
        if (this.getType() === 'cs-desktop-client') {
            return true
        }
        return false
    }

    /**
     * 壳类型
     * @returns {*}
     */
    getType() {
        return window.shellType
    }

    /**
     * 用OS默认浏览器打开地址
     * @param url
     */
    async openExternalUrl(url) {
        const { ipcRenderer } = require('electron');
        await ipcRenderer.invoke('open-external-url', url)
    }

    /**
     * 判定文件是否存在
     * @param filePath
     * @returns {Promise<boolean>}
     */
    async isFileExist(filePath) {
        const { ipcRenderer } = require('electron');
        return await ipcRenderer.invoke('is-file-exist', filePath)
    }

    /**
     * 判定目录是否存在
     * @param dirPath
     * @returns {Promise<boolean>}
     */
    async isDirExist(dirPath) {
        const { ipc<PERSON><PERSON><PERSON> } = require('electron');
        return await ipcRenderer.invoke('is-dir-exist', dirPath)
    }

    /**
     * 写入文本内容到文件中
     * @param filePath
     * @param content
     */
    async writeStrToFile(filePath, content) {
        const { ipcRenderer } = require('electron');
        await ipcRenderer.invoke('write-str-to-file', filePath, content)
    }

    /**
     * 读取文本文件内容
     * @param filePath
     * @returns {Promise<string>}
     */
    async readStrFromFile(filePath) {
        const {ipcRenderer} = require('electron');
        return await ipcRenderer.invoke('read-str-from-file', filePath)
    }
}