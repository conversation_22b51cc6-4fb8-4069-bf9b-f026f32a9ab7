/**
 * 客户端数据Mapper
 */
export default class ClientDataMapper {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async set(key, value) {
        if (window.localStorage) {
            window.localStorage.setItem(key, value)
        }
    }

    async get(key) {
        if (window.localStorage) {
            return window.localStorage.getItem(key)
        }
    }

    async remove(key) {
        if (window.localStorage) {
            window.localStorage.removeItem(key);
        }
    }
}