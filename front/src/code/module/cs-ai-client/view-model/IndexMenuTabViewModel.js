/**
 * 主页菜单选项卡视图模型
 */
export default class IndexMenuTabViewModel {
    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     * 当通过路由地址打开
     * @param view
     * @returns {boolean}
     */
    onOpenByRoutePath(view) {
        if (view.$route.path.startsWith('/cac/app/')) {
            let str = view.$route.path.replace('/cac/app/', '');
            view.openApp(str, {
            });
            return true;
        }
        else if (view.$route.path.startsWith('/cac/url-app/')) {
            let str = view.$route.path.replace('/cac/url-app/', '');
            view.openApp(str, {
            });
            return true;
        }
        return false;
    }

    onTabChange(view, name) {
        view.openApp(name)
    }

    onRemoveTab(view, name) {
        const debug = false;
        // 清空历史
        {
            const list = [];
            view.openMenuHistory.forEach((n) => {
                if (n !== name) {
                    list.push(n);
                }
            });
            view.openMenuHistory = list;
        }

        // ...
        let needOpenPreMenu = false;
        if (view.$refs.indexTabBar0.getActiveTab() === name) {
            needOpenPreMenu = true;
        }
        if (debug) console.log('RemoveTab: ', name, view.$refs.indexTabBar0.getActiveTab(), needOpenPreMenu);

        // 删除已打开的菜单
        let index = 0;
        for (const openMenu of view.openMenus) {
            if (openMenu.name === name) {
                view.openMenus.splice(index, 1);
                break;
            }
            index++;
        }

        // 自动打开前一个打开的菜单
        if (needOpenPreMenu) {
            view.$refs.indexTabBar0.setActiveTab(null)
            view.$refs.indexLeftMenuBar0.setSelectedByName(null)

            view.$nextTick(() => {
                // 打开上一次打开的
                if (view.openMenus.length > 0 && view.openMenuHistory.length > 0) {
                    const preName = view.openMenuHistory.pop()
                    view.openApp(preName);
                }
                // 打开第一个
                else if (view.openMenus.length > 0) {
                    const preName = view.openMenus[0].name
                    view.openApp(preName)
                }
                // 打开应用中心
                else {
                    view.openApp('empty')
                }
            });
        }
    }
}