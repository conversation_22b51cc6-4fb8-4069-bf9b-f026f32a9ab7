import CBAIOMapper from "../../mapper/CBAIOMapper.js"

export default class DataSourceMapper {
    constructor(ctx) {
        this.ctx = ctx
        this.cbaiomapper = new CBAIOMapper(ctx);
    }

    async getExtAppDataList(params) {
        return await this.cbaiomapper.reqByPost1('/api/cds/get-ext-app-data-list', params);
    }

    async getExtAppDataForm(params) {
        return await this.cbaiomapper.reqByPost1('/api/cds/get-ext-app-data-form', params);
    }

    async saveExtAppDataForm(params) {
        return await this.cbaiomapper.reqByPost1('/api/cds/save-ext-app-data-form', params);
    }

    async deleteExtAppDataForm(params) {
        return await this.cbaiomapper.reqByPost1('/api/cds/delete-ext-app-data-form', params);
    }

    async getAppCenterList() {
        return await this.cbaiomapper.reqByPost1('/api/cds/get-app-center-list');
    }

    async getAIToolList() {
        return await this.cbaiomapper.reqByPost1('/api/cds/get-ai-tool-list');
    }

    async getSetting(key) {
        return await this.cbaiomapper.reqByPost1('/api/cds/get-setting', { key });
    }

    async setSetting(key, value) {
        return await this.cbaiomapper.reqByPost1('/api/cds/set-setting', { key, value });
    }

    async getUserData(key, opts = {}) {
        return await this.cbaiomapper.reqByPost1('/api/cds/get-user-data', { key, opts });
    }

    async setUserData(key, value, opts = {}) {
        return await this.cbaiomapper.reqByPost1('/api/cds/set-user-data', { key, value, opts });
    }
}