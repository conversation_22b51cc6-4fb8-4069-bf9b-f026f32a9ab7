import { sleep } from './thread-util.js'

export async function waitGetObj(target, name) {
    return new Promise(async (resolve, reject) => {

        for (let i = 0; ; i++) {
            // console.log(target)
            if (target[name]) {
                resolve(target[name]);
                break;
            }

            await sleep(300);
        }

        resolve(null);
    });
}

export async function waitFuncDone(func) {
    for (let i = 0; ; i++) {
        if (func() === true) break;

        await new Promise(async (resolve, reject) => {
            setTimeout(() => {
                resolve(null);
            }, 300);
        });
    }
}