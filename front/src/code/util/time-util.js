const weekDays0 = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
const weekDays1 = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];

/**
 * 转为日期字符串
 * @param {*} date 
 * @returns yyyy-MM-dd
 */
export function toDateStr(date) {
    date = convertToDate(date);
    if (date === '') return '';

    let id = '';
    id += date.getFullYear();
    id += '-';
    if ((date.getMonth() + 1) < 10) {
        id += '0' + (date.getMonth() + 1);
    }
    else {
        id += (date.getMonth() + 1);
    }
    id += '-';
    if (date.getDate() < 10) {
        id += '0' + date.getDate();
    }
    else {
        id += date.getDate();
    }

    return id;
}

/**
 * 转为时间字符串
 * @param {*} date 
 * @param {*} noSecond 
 * @returns HH:mm:ss
 */
export function toTimeStr(date, noSecond) {
    date = convertToDate(date);
    if (date === '') return '';

    let h = date.getHours();
    let m = date.getMinutes();
    let s = date.getSeconds();

    if (h < 10) {
        h = '0' + h;
    }
    if (m < 10) {
        m = '0' + m;
    }
    if (s < 10) {
        s = '0' + s;
    }

    return h + ':' + m + (noSecond != true ? (':' + s) : '');
}

/**
 * 转为日期时间字符串
 * @param {*} date 
 * @param {*} noSecond 
 * @returns yyyy-MM-dd HH:mm:ss
 */
export function toDateTimeStr(date, noSecond) {
    /*
    if (date instanceof0 Date) {

    }
    else if (date == null || date == '' || date == 'null') {
    return '';
    }
    else {
    if (isIE() && date.indexOf('-') != -1) {
    date = date.replace(/[-]/g, '/');
    }
    if (isIE() && date.indexOf('.') != -1) {
    date = date.replace(/[.]\d+/g, '');
    }
    date = new Date(date.replace('T', ' '));
    }*/
    date = convertToDate(date);
    if (date === '') return '';

    let id = '';
    id += date.getFullYear();
    id += '-';
    if ((date.getMonth() + 1) < 10) {
        id += '0' + (date.getMonth() + 1);
    }
    else {
        id += (date.getMonth() + 1);
    }
    id += '-';
    if (date.getDate() < 10) {
        id += '0' + date.getDate();
    }
    else {
        id += date.getDate();
    }

    let h = date.getHours();
    let m = date.getMinutes();
    let s = date.getSeconds();

    if (h < 10) {
        h = '0' + h;
    }
    if (m < 10) {
        m = '0' + m;
    }
    if (s < 10) {
        s = '0' + s;
    }

    return id + ' ' + h + ':' + m + (noSecond != true ? (':' + s) : '');
}

// 字符串转为Date类型
function convertToDate(date) {
    if (date instanceof Date) {

    }
    else if (date == null || date === '' || date === 'null') {
        return '';
    }
    else {
        let dateStr = date.replace('T', ' ').replace(/[.][^.]+/g, '');
        /*
        if (isIE() && dateStr.indexOf('-') != -1) {
        date = dateStr.replace(/[-]/g, '/');
        }
        if (isIE() && dateStr.indexOf('.') != -1) {
        date = dateStr.replace(/[.]\d+/g, '');
        }*/
        date = new Date(dateStr);
        if (date.toJSON() == null) {
            if (dateStr.indexOf('-') !== -1) {
                date = new Date(dateStr.replace(/[-]/g, '/'));
            }
            else if (dateStr.indexOf('/') !== -1) {
                date = new Date(dateStr.replace(/[\/]/g, '-'));
            }
        }
    }
    return date;
}

// 时间转为yyyy-MM
function toYearMonthStr(date) {
    date = convertToDate(date);
    if (date === '') return '';

    let id = '';
    id += date.getFullYear();
    id += '-';
    if ((date.getMonth() + 1) < 10) {
        id += '0' + (date.getMonth() + 1);
    }
    else {
        id += (date.getMonth() + 1);
    }

    return id;
}

function addMonths(date, num) {
    num = parseInt(num);
    let sDate = dateToDate(date);

    let sYear = sDate.getFullYear();
    let sMonth = sDate.getMonth() + 1;
    let sDay = sDate.getDate();

    let eYear = sYear;
    let eMonth = sMonth + num;
    let eDay = sDay;
    while (eMonth > 12) {
        eYear++;
        eMonth -= 12;
    }

    let eDate = new Date(eYear, eMonth - 1, eDay);

    while (eDate.getMonth() != eMonth - 1) {
        eDay--;
        eDate = new Date(eYear, eMonth - 1, eDay);
    }

    return eDate;
}

function dateToDate(date) {
    let sDate = new Date();
    if (typeof date == 'object'
        && typeof new Date().getMonth == "function"
    ) {
        sDate = date;
    }
    else if (typeof date == "string") {
        let arr = date.split('-')
        if (arr.length == 3) {
            sDate = new Date(arr[0] + '-' + arr[1] + '-' + arr[2]);
        }
    }

    return sDate;
}

function timestampToDateTime(timestamp) {
    if (typeof timestamp == 'string') {
        timestamp = parseInt(timestamp);
    }
    /*时间戳为10位需*1000，时间戳为13位的话不需乘1000*/
    let date = null;
    if (timestamp.toString().length == 10) {
        date = new Date(timestamp * 1000);
    }
    else {
        date = new Date(timestamp);
    }
    let Y = date.getFullYear() + '-';
    let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    let D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
    let h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
    let m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
    let s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
    return Y + M + D + h + m + s;
}

function getWeekDays0() {
    return weekDays0;
}

function getWeekDays1() {
    return weekDays1;
}

// module.exports = {
//     toDateStr,
//     toTimeStr,
//     toDateTimeStr
// }