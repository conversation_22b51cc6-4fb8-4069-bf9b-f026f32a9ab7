/**
 * 获取受限长度文本
 * @param source 原始文本
 * @param maxLength 最大长度
 * @param tag 受限后展示的后缀（默认省略号）
 */
export function getLimitStr(source, maxLength, tag) {
    if (source) {
        if (source.length > maxLength) {
            if (tag == null) {
                tag = '...'
            }
            const limitStr = source.substring(0, maxLength) + tag
            return limitStr
        }
    }
    return source
}