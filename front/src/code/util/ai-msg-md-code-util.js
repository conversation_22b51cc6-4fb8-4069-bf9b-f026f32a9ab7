/**
 * 增强消息中Markdown内容里的代码块
 * @param {HTMLElement} messageElement 
 */
export function enhanceMsgMdCodeBlock(messageElement) {
    if (!messageElement) return;

    // SVG代码块
    {
        const svgCodeBlocks = messageElement.querySelectorAll('pre code.language-xml');
        svgCodeBlocks.forEach(codeBlock => {
            const codeContent = codeBlock.textContent;
            if (codeContent.includes('<svg')) {
                // 增强
                enhanceSvgBlock(codeBlock);
            }
        });
    }
    {
        const svgCodeBlocks = messageElement.querySelectorAll('pre code.language-svg');
        svgCodeBlocks.forEach(codeBlock => {
            // 增强
            enhanceSvgBlock(codeBlock);
        });
    }
}

/**
 * 增强SVG代码块
 * @param {*} codeContent 
 * @returns 
 */
function enhanceSvgBlock(codeBlock) {
    const codeContent = codeBlock.textContent;
    // 获取原始pre元素
    const preElement = codeBlock.parentElement;
    if (!preElement || !preElement.parentNode) return;

    // 创建容器
    const container = document.createElement('div');
    container.className = 'svg-code-block';

    // 创建头部和按钮
    const header = document.createElement('div');
    header.className = 'svg-code-header';

    const previewBtn = document.createElement('button');
    previewBtn.className = 'svg-preview-btn';
    previewBtn.textContent = '预览';

    const sourceBtn = document.createElement('button');
    sourceBtn.className = 'svg-source-btn';
    sourceBtn.textContent = '源代码';
    sourceBtn.style.display = 'none';
    
    const openInNewBtn = document.createElement('button');
    openInNewBtn.className = 'svg-open-new-btn';
    openInNewBtn.textContent = '新窗口打开';
    openInNewBtn.style.display = 'none';

    header.appendChild(previewBtn);
    header.appendChild(sourceBtn);
    header.appendChild(openInNewBtn);

    // 创建代码区域
    const codeArea = document.createElement('div');
    codeArea.className = 'svg-code-area';

    // 克隆原始代码块而不是移动它
    const clonedPre = preElement.cloneNode(true);
    codeArea.appendChild(clonedPre);

    // 创建预览区域
    const previewArea = document.createElement('div');
    previewArea.className = 'svg-preview-area';
    previewArea.style.display = 'none';
    previewArea.innerHTML = codeContent;

    // 组装容器
    container.appendChild(header);
    container.appendChild(codeArea);
    container.appendChild(previewArea);

    // 替换原始代码块
    preElement.parentNode.replaceChild(container, preElement);

    // 添加按钮点击事件
    previewBtn.addEventListener('click', () => {
        codeArea.style.display = 'none';
        previewArea.style.display = 'block';
        previewBtn.style.display = 'none';
        sourceBtn.style.display = 'inline-block';
        openInNewBtn.style.display = 'inline-block';
    });

    sourceBtn.addEventListener('click', () => {
        previewArea.style.display = 'none';
        codeArea.style.display = 'block';
        sourceBtn.style.display = 'none';
        openInNewBtn.style.display = 'none';
        previewBtn.style.display = 'inline-block';
    });
    
    // 添加在新窗口打开SVG的功能
    openInNewBtn.addEventListener('click', () => {
        // 创建一个Blob对象
        const svgBlob = new Blob([codeContent], {type: 'image/svg+xml'});
        // 创建URL
        const svgUrl = URL.createObjectURL(svgBlob);
        
        // 打开新窗口
        const newWindow = window.open(svgUrl, '_blank');
        
        // 当新窗口加载完成后，添加一些样式使SVG居中显示
        newWindow.addEventListener('load', () => {
            const style = newWindow.document.createElement('style');
            style.textContent = `
                body {
                    margin: 0;
                    padding: 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100vh;
                    background-color: #f5f5f5;
                    overflow: auto; /* 添加滚动功能 */
                }
                svg {
                    max-width: 90%;
                    /* 移除max-height限制，允许SVG完整显示 */
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    background-color: white;
                }
            `;
            newWindow.document.head.appendChild(style);
            
            // 释放URL对象
            newWindow.addEventListener('unload', () => {
                URL.revokeObjectURL(svgUrl);
            });
        });
    });
}