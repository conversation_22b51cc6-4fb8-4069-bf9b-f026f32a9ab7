/**
 * 清理回复中的思考内容
 * @param reply
 */
export function clearThinkContent(reply) {
    if (reply) {
        if (reply.indexOf('<think>\n') !== -1 && reply.indexOf('</think>\n') !== -1) {
            if (reply.indexOf('</think>\n\n') !== -1) {
                const arr = reply.split('</think>\n\n');
                reply = arr[1];
            }
            else if (reply.indexOf('</think>\n') !== -1) {
                const arr = reply.split('</think>\n');
                reply = arr[1];
            }
        }
    }
    return reply;
}