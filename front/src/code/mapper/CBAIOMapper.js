import { reqPostJson, fetchStream } from "../util/http-util.js";
import sysCfg from "../../config/app-config.js";

export default class CBAIOMapper {
    constructor(ctx) {
        this.ctx = ctx
    }

    async reqByPost(cmd, params) {
        const token = await this.ctx.loginService.getToken();

        if (cmd.startsWith("/")) {
            cmd = cmd.substring(1);
        }

        return await reqPostJson(`${sysCfg.backRootUrl}${cmd}`, {
            token: token,
            ...params,
        });
    }

    async reqByPost1(cmd, params) {
        const token = await this.ctx.loginService.getToken();

        if (cmd.startsWith("/")) {
            cmd = cmd.substring(1);
        }

        return await reqPostJson(`${sysCfg.backRootUrl}${cmd}`, {
            token: token,
            params: params,
        });
    }

    async reqByPostStream(cmd, params, cb, doneCb, errCb) {
        const token = await this.ctx.loginService.getToken();

        if (cmd.startsWith("/")) {
            cmd = cmd.substring(1);
        }

        fetchStream(`${sysCfg.backRootUrl}${cmd}`, 'POST', {
            token,
            ...params,
        }, (r) => {
            if (cb) cb(r);
        }, () => {
            if (doneCb) doneCb();
        }, (err) => {
            if (errCb) errCb(err);
        });
    }
}