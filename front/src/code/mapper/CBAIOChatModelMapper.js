import CBAIOMapper from "./CBAIOMapper.js";

export default class CBAIOChatModelMapper {
    constructor(ctx) {
        this.ctx = ctx
        this.cbaioMapper = new CBAIOMapper(ctx)
    }

    /**
     *
     * @param model
     * @param messages
     * @param opts
     * @returns {Promise<Object>}
     */
    async chat(model, messages, opts = {}) {
        return await this.cbaioMapper.reqByPost('/api/chat-model/chat', {
            model,
            messages,
            ...opts,
        })
    }

    /**
     * 
     * @param {*} model 
     * @param {*} messages 
     * @param {*} opts 
     * @param {*} opts.attachments 附件
     * @param {*} opts.tools 工具
     * @param {*} opts.max_tokens 最大令牌数
     */
    async chatStream(model, messages, cb, doneCb, errCb, opts = {}) {
        this.cbaioMapper.reqByPostStream('/api/chat-model/chat-stream', {
            model,
            messages,
            ...opts,
        }, cb, doneCb, errCb);
    }
}