export class CACShellMapper {
    constructor() {
        this._debug = false;
    }

    // 判断是否在客户端中打开
    isInClient() {
        if (top && top.getClientType) {
            const t = top.getClientType();
            if (t && t === 'cs-desktop-client') {
                if (this._debug) console.log('判断是否在客户端中打开...', true);
                return true;
            }
        }
        if (this._debug) console.log('判断是否在客户端中打开...', false);
        return false;
    }

    newBaiduMediaRecorder() {
        return top.newBaiduMediaRecorder();
    }
}