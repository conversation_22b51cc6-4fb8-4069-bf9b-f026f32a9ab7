let debug = false;
let backRootUrl = 'http://localhost:3000/';
// let backRootUrl = 'http://************:3000/';

if (process.env.NODE_ENV === 'production') {
  console.log('线上环境');
  debug = false;
  backRootUrl = '../';
}
else if (process.env.NODE_ENV === 'test') {
  console.log('测试环境');
  debug = true;
}
else {
  console.log('开发环境');
  debug = true;
}

export default {
  // 是否开启调试
  debug,
  // 后端服务地址
  backRootUrl,
  baiduSpeechRecogBaseUrl: 'http://test.ovinfo.com:2243',
  baiduSpeechRecogAccessToken: '838bc71ca3a0',
}