const express = require('express');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');

const lancedb = require("@lancedb/lancedb");
const apacheArrow = require("apache-arrow");

// 用于主项目执行
const {start} = require('./src/main-start');
// 用于派生项目执行
// const {start} = require('./lib/main-start');

const imports = {
    express, bodyParser,
    fs, path,
    lancedb, apacheArrow,
};
const ctx = start(imports, {
    startServer: false,
});

// const r = await ctx.shortTimeCacheService.getChatModel().embed('ollama_nomic-embed-text:v1.5', '你好，是否螺丝扣搭街坊老师的JFK塑料袋放进');
const r = await ctx.shortTimeCacheService.getChatModel().embed('zhipu_Embedding-3', '你好，是否螺丝扣搭街坊老师的JFK塑料袋放进');

console.log(`长度：${r.embedding.length}`)
console.log(JSON.stringify(r.embedding).substring(0, 100), '...')

const r1 = {
    ...r
}
delete r1.embedding
console.log(JSON.stringify(r1))