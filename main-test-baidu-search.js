const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');

const lancedb = require("@lancedb/lancedb");
const apacheArrow = require("apache-arrow");

// 用于主项目执行
const {start} = require('./src/main-start');
// 用于派生项目执行
// const {start} = require('./lib/main-start');

const imports = {
    express, bodyParser, path,
    lancedb, apacheArrow,
};
const ctx = start(imports, {
    startServer: false,
});

ctx.shortTimeCacheService.getBaiduQianFanAppBuilderMapper().chatAgent("c3a74d13-f2f8-4fe5-a14f-ea19b2e6a2f4", JSON.stringify({
    kw: "工作流前端技术有哪些", top_num: 8, type: 'ai'
}), (r) => {
    // request_id, date, answer: String, conversation_id, message_id, is_completion, content: []
    // console.log(r);

    const json = r.answer;
    const answer = JSON.parse(json);
    console.log(answer.result);
    console.log(answer.result_ai);
    console.log(answer.result_ai_refs);
}, (err) => {
    // code, message, response.data.code, response.data.message
    console.log(err.code, err.message, err.response.data.message);
});