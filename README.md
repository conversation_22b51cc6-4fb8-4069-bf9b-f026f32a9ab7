
# 项目结构
- ./front 主前端项目，包含cac
- ./front-apps 应用前端项目，包含各个零散的应用


# 包含内容
- AI问答，结合了所有AI服务
- 知识库，有自己开发的知识库框架也有结合外部知识库平台的接口


# 安装
```bash

cnpm i @lancedb/lancedb
# （安装后会报错，去创建下报错的那个文件再安装就好了）
# （经验：我在生产环境安装也报错，但是不影响运行（cnpm install --production））
cnpm i apache-arrow

```


# 本地启动
- dev-run-des5.bat

# 部分打包（部分依赖库支持打包）
1. 执行 dev-build-main-expose-es5.bat
2. 复制 main.js 到服务器，把不支持打包的依赖放在main中导入
3. 在服务器上需要安装依赖

# 部分打包（部分依赖库支持打包）
1. 执行 dev-build-main-expose-es5.bat
2. 复制 main.js 到服务器，把不支持打包的依赖放在main中导入
3. 在服务器上需要安装依赖
    - npm i --production
    - npm i --production --legacy-peer-deps（忽略版本限制）
    - npm i --production --force（忽略所有限制）


# 测试案例
- 几点上班
- 公司做什么业务的（百度千帆回答不出）
- 公司是做什么业务的