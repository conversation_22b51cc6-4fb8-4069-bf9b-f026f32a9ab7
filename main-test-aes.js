import crypto from 'crypto';

// 解密函数
function decryptAESKey(text, encodingAESKey) {
    // 添加=号补位
    const aesKey = Buffer.from(encodingAESKey + '=', 'base64');
    const iv = aesKey.slice(0, 16);
    const decipher = crypto.createDecipheriv('aes-256-cbc', aesKey, iv);
    decipher.setAutoPadding(false);
    
    let decrypted;
    try {
        decrypted = Buffer.concat([
            decipher.update(Buffer.from(text, 'base64')),
            decipher.final()
        ]);
    } catch (e) {
        console.error('解密失败:', e);
        return '';
    }
    
    // 去除补位字符
    let pad = decrypted[decrypted.length - 1];
    if (pad < 1 || pad > 32) {
        pad = 0;
    }
    decrypted = decrypted.slice(0, decrypted.length - pad);
    
    // 去除16位随机字符串、4字节网络字节序和corpid
    const content = decrypted.slice(16);
    const msgLen = content.readUInt32BE(0);
    const msg = content.slice(4, 4 + msgLen).toString('utf8');
    
    return msg;
}

// 加密函数
function encryptAESKey(text, encodingAESKey) {
    // 添加=号补位
    const aesKey = Buffer.from(encodingAESKey + '=', 'base64');
    const iv = aesKey.slice(0, 16);
    
    // 生成16位随机字符串
    const randomString = crypto.randomBytes(16);
    
    // 将文本转换为Buffer
    const textBuffer = Buffer.from(text, 'utf8');
    
    // 计算文本长度，并转换为网络字节序（4字节）
    const msgLenBuffer = Buffer.alloc(4);
    msgLenBuffer.writeUInt32BE(textBuffer.length, 0);
    
    // 拼接消息内容
    let content = Buffer.concat([
        randomString,
        msgLenBuffer,
        textBuffer
    ]);
    
    // PKCS#7 填充
    const padLength = 32 - (content.length % 32);
    const padBuffer = Buffer.alloc(padLength, padLength);
    content = Buffer.concat([content, padBuffer]);
    
    // 加密
    const cipher = crypto.createCipheriv('aes-256-cbc', aesKey, iv);
    cipher.setAutoPadding(false); // 已手动填充，无需自动填充
    
    const encrypted = Buffer.concat([
        cipher.update(content),
        cipher.final()
    ]);
    
    // 转换为Base64编码
    return encrypted.toString('base64');
}

const plainText = "哈哈哈hahaha";
const encodingAESKey = "6sZP8ZfzXKaUh5nGTxlR1so7jyITg5OSoZz9o4ES6qk";
const encryptedText = encryptAESKey(plainText, encodingAESKey);

console.log('加密后的文本:', '"' + encryptedText + '"');

const decryptedText = decryptAESKey(encryptedText, encodingAESKey);
console.log('解密后的文本:', '"' + decryptedText + '"');
