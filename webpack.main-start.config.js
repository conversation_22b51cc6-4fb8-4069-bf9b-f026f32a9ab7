// import path from 'path';
// export default {
//     entry: "./main.js",
//     mode: "production",
//     output: {
//         path: path.resolve() + '/dist',
//         filename: "main.js",
//         libraryTarget: 'commonjs'
//     },
//     // externals: {
//     //     // "fs": true,
//     //     // "vm": true,
//     // },
//     // resolve: {
//     //     fallback: {
//     //         // "fs": false,
//     //         // "vm": false,
//     //     },
//     // },
//     target: 'node' // 'webworker' or 'node' or 'node-webkit'
// };
const path = require('path')

module.exports = {
    entry: "./src/main-start.js",
    // mode: "production",
    mode: "development",
    output: {
        // path: __dirname + '/dist',
        path: path.resolve(__dirname, 'lib'),
        filename: "main-start.js",
        globalObject: "this",
        library: 'cs-njs-vue-ssr-start',
        libraryTarget: "umd",
    },
    target: 'node' // 'webworker' or 'node' or 'node-webkit'
};