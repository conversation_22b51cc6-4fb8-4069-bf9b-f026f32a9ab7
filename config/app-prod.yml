port: 8080

ollama:
  host: 'http://test.ovinfo.com:2234'
  chatModel: 'qwen2.5:7b'
  #  chatModel: 'gemma2:27b'
  #  chatModel: 'llama3.2:3b'
  #  chatModel: 'llama3.1:70b'
  embedModel: 'mxbai-embed-large:335m'

openai_brain_wild:
  url: "http://************:8088/api/wild/chatGpt"

bd_qf_ab:
  url: "https://qianfan.baidubce.com"
  apikey: "bce-v3/ALTAK-lFYCoGMzg3rnPsS6eV75z/d139e88fec3c00a2e23334349260847f653ca697"

cs_mini_kb:
  kwModel: 'qwen2.5:7b'
  chatModelPlatform: 'openai' # 聊天模型使用的平台
  chatModel: 'gpt-4o-mini'

cs_bot_kb0:
  url: 'http://************:8089'
  innerKey: 'abc131'

cs_bot_kb0_v2:
  url: 'http://test.ovinfo.com:2238'

anythingllm:
  url: 'http://test.ovinfo.com:2235'
  apikey: 'FFSFDFX-8GKMSV3-HWGDW3H-Y5Y3DHW'
  workDirPath: '/data/sites/AnythingLLM'