port: 3000
# Ollama服务
ollama:
  host: 'http://test.ovinfo.com:2234'
  chatModel: 'qwen2.5:7b' # 默认使用的聊天模型 qwen2.5:7b qwen2.5:14b 无法使用：qwen:14b gemma2:27b
  embedModel: 'mxbai-embed-large:335m' # 默认使用的嵌入模型
# 基于 OpenAI 在 Brain-Wild
openai_brain_wild:
#  url: "http://localhost:8088/api/wild/chatGpt"
  url: "http://************:8088/api/wild/chatGpt"
  url1: "http://************:8088/api/wild/openai"
# 基于 BD-QF-AppBuilder
bd_qf_ab:
  url: "https://qianfan.baidubce.com"
  apikey: "bce-v3/ALTAK-lFYCoGMzg3rnPsS6eV75z/d139e88fec3c00a2e23334349260847f653ca697"
# 自己研发的知识库系统
cs_mini_kb:
  # 数据源
  dataSourceUrl: 'http://cs.juzhengdevelop.com:8072'
  kwModel: 'ollama_qwen3:14b|no_think|'
  # kwModel: 'ollama_qwen2.5:7b' # 用于分析关键词的模型
  # kwModel: 'br_gpt-4o-mini' # 用于分析关键词的模型
  chatModel: 'br_gpt-4o-mini'
#  chatModel: 'ollama_qwen2.5:7b'
  embedModel: 'br_text-embedding-3-small'
#  embedModel: 'zhipu_embedding-3' # 已适配了OpenAI维度
#  embedModel: 'ollama_nomic-embed-text:v1.5' # 会报错，估计要重新建库
#  embedModel: 'ollama_mxbai-embed-large:335m' # 会报错，估计要重新建库

# 基于 llama-index + openai
cs_bot_kb0:
  url: 'http://************:8089'
  innerKey: 'abc131'
# 基于 llama-index + ollama
cs_bot_kb0_v2:
  url: 'http://test.ovinfo.com:2238'
# 基于 AnythingLLM
anythingllm:
  url: 'http://test.ovinfo.com:2235'
  apikey: 'FFSFDFX-8GKMSV3-HWGDW3H-Y5Y3DHW'
  workDirPath: '/data/sites/AnythingLLM'

# 智能体
cs_mini_agent:
  # 聊天服务端口
  chatWsPort: 3001
  # 企业微信
  qywx:
    corps:
      - corpId: 'wx2403e0282a67d98d'
        sac: 'bot.test'
        loginSKey: '7feb60a5'
        apps:
          - agentId: '1000031'
            secret: 'kr8a2K-VG8gismENnoS3CK1UUJ1w8Ax3mOrTb8vL-_c'
            # 接收消息
            receiveMessages:
              reqId: 'agent-rwzs' # 用于区分地址
              token: 'Rbiudq7FeJYaHeguhiu79Lu2'
              encodingAESKey: '6sZP8ZfzXKaUh5nGTxlR1so7jyITg5OSoZz9o4ES6qk'


# 用户库
user_db:
  'bot.test':
    # 数据源
    dataSourceUrl: 'http://cs.juzhengdevelop.com:8072'
#    dataSourceUrl: 'http://localhost:8083'
    dataSourceType: 'csaui6jt'
    dataSourceSac: 'bot.test'
    skey: '7feb60a5'
    users:
      - username: 'root'
        password: 'abc123'
        realname: '程晟'
        id: '7ca27f12-b9df-46fd-9945-f3a52f40895a'
        weixin: 'hotwizardry'
        roles: ['admin']
      - username: 'cs'
        password: 'hot850907'
        realname: '程晟'
        id: '7ca27f12-b9df-46fd-9945-f3a52f40895a'
        weixin: 'icewizardry'
        roles: ['admin']
      - username: 'erosewin'
        password: '6nfo1127o'
        realname: '顾正磊'
        id: '33985ae0-69b9-4e8c-9a28-5bb349d87d7e'
        weixin: 'aioloswin'
        roles: ['admin']

  'yxt.cn':
    dataSourceUrl: 'http://cs.juzhengdevelop.com:8072'
    #    dataSourceUrl: 'http://localhost:8083'
    dataSourceType: 'csaui6jt'
    dataSourceSac: 'bot.test'
    skey: '7feb60a5'
#    skey: '2a07a796'
    users:
      - username: 'root'
        password: '52df47a03230'
        realname: '根'
      # - username: 'lhgc_1'
      #   password: '65479288'
      #   realname: '绿化工程1号'
      # - username: 'yxt_1'
      #   password: '34487fb7'
      #   realname: '杨小通1号'

  'cz.cn':
    skey: '2a07a796'
    users:
      - username: 'trail_0'
        password: '17bcef43'
        realname: '演示0'

  'lhgc.cn':
    skey: '2a07a796'
    users:
      - username: 'root'
        password: 'abc123'
        realname: '根'

  'test.cn':
    skey: '196fb976'
    users:
      - username: 'report'
        password: 'd5d27d69'
        realname: '报告编写者'
