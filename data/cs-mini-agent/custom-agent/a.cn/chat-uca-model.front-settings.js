(function() {
    return {
        sendMessageType: 'stream',
        // 获取初始提示词（欢迎词）
        async getInitialPrompt() {
            return {
                content: `### 你来啦，快来一起对话吧~
            `
            };
        },
        // 输入框内部工具
        inputInnerTools: [
            // 模型选择器
            {
                name: 'model',
                type: 'select',
                placeholder: '请选择模型',
                value: 'ollama_qwen3:32b|no_think|',
                options: [
                    {
                        label: 'DeepSeek-V3.1-满血（ALiBL）',
                        value: 'albl_deepseek-v3.1',
                        title: '上下文130k，输出65k',
                    },
                    {
                        label: 'DeepSeek-V3.1-满血（官网）',
                        value: 'ds_deepseek-chat',
                        title: '上下文64k，输出8k',
                    },
                    {
                        label: 'Qwen3-Max（ALiBL）',
                        value: 'albl_qwen3-max-preview',
                        title: '上下文262k，输出32k',
                        canToolCall: true,
                    },
                    {
                        label: 'Qwen3-235b【不思考】（ALiBL）',
                        value: 'albl_qwen3-235b-a22b|no_think|',
                        title: '上下文131k，输出8k',
                        canToolCall: true,
                    },
                    {
                        label: 'MiniMax-Text-01（MiniMax）',
                        value: 'minimax_MiniMax-Text-01',
                        title: '上下文1000k',
                        canVision: true,
                        canToolCall: true,
                    },
                    {
                        label: 'Kimi-k2-0711-preview',
                        value: 'msa_kimi-k2-0711-preview',
                        title: '上下文13w',
                        canToolCall: true,
                    },
                    /*** OpenAI（Brain） ***/
                    {
                        label: 'GPT-4o-mini（Brain）',
                        value: 'br_gpt-4o-mini',
                        title: '上下文128k，输出16k',
                    },
                    {
                        label: 'GPT-4o（Brain）',
                        value: 'br_gpt-4o',
                        title: '上下文128k，输出16k',
                    },
                    /*** Ollama ***/
                    {
                        label: 'Qwen3-32b【不思考】（公司）',
                        value: 'ollama_qwen3:32b|no_think|',
                        canToolCall: true,
                    },
                    {
                        label: 'Qwen3-32b（公司）',
                        value: 'ollama_qwen3:32b',
                        canToolCall: true,
                    },
                    {
                        label: 'DeepSeek-R1-14b（公司）',
                        value: 'ollama_deepseek-r1:14b',
                    },
                ]
            }
        ],
    };
})