(function (ctx) {
    return {
        parentId: 'chat-kb',
        name: '绿化工程',
        summary: '知识库智能体',
        settings: {
            maxChatHistoryLength: 0,
            chatHistoryBotReplyMaxSize: 10, // 对话时塞入的历史AI回复内容大小限制
            config: {
                kbId: '20241023172616342-6e7218',
                yshdMinScore: 0.50, // 预设回答相似度阈值
                kbListMaxSize: 5, // 查找最大条数
            },
            chatOpts: {
                temperature: 0.3, // 温度 [0~1] 越大随机性越高
                // chatModel: 'ds_deepseek-chat',
                // chatModel: 'minimax_MiniMax-Text-01',
                chatModel: 'zhipu_glm-4.5-air|no_think|',
                // chatModel: 'zhipu_glm-4.5|no_think|',
                // chatModel: 'zhipu_glm-4.5-x|no_think|',
                // chatModel: 'zhipu_glm-4-plus',
                // chatModel: 'msa_kimi-k2-0711-preview',
                // chatModel: 'br_gpt-4.1',
                // chatModel: 'br_gpt-4o',
                // chatModel: 'albl_qwen3-235b-a22b|no_think|',
                // chatModel: 'albl_qwen-turbo',
                // chatModel: 'albl_qwen-plus',
                // chatModel: 'ollama_qwen3:14b|no_think|',
                toolModel: 'ollama_qwen3:14b|no_think|',
                // toolModel: 'minimax_MiniMax-Text-01',
                // toolModel: 'zhipu_glm-4-plus',
                // toolModel: 'albl_qwen-max',
                // toolModel: 'albl_qwen-plus',
                // toolModel: 'br_gpt-4o-mini',
                // toolModel: 'ollama_qwen2.5:14b',
            },
        },
    };
})