(function (ctx) {
    return [
        /*
        // 工具样例
        {
            name: 'toolDemo',
            descr: '',
            params: {
                required: [],
                props: {
                }
            },
            call: async function(params) {

            }
        },
        */
        // 列出超时任务
        {
            name: 'listTimeoutTask',
            descr: '列出超时任务',
            params: {
                required: [],
                props: {
                }
            },
            // 调用工具
            call: async function(params) {
                let result = '';
                const list = await ctx.execPlugin({
                    name: 'loadTimeoutTaskList',
                    params: {
                    }
                });

                if (list.success === false) {
                    return {
                        reply: `请求失败 -> ${list.msg}`
                    };
                }

                const cols = [
                    { name: 'name', label: '任务名', descr: '' },
                    { name: 'optUser', label: '操作人', descr: '' }
                ];

                result = `在“${ctx.timeUtil.toDateTimeStr(new Date())}”查到${list.length}条超时任务：\n`;
                result += ctx.strUtil.buildMarkdownTable(cols, list);
                return {
                    reply: result
                };
            }
        },
        // 列出我的未读任务更新历史
        {
            name: 'listMyNotReadTaskUpdateHistory',
            descr: '列出我的未读任务更新历史',
            params: {
                required: [],
                props: {
                }
            },
            // 调用工具
            call: async function(params, opts) {
                let result = '';

                opts.outputState('查询用户信息中');
                const findEmpInfo = await ctx.execPlugin({
                    name: '_findEmpByRn',
                    params: {
                        un: ctx.loginUser.username
                    }
                })

                if (findEmpInfo == null) {
                    return {
                        reply: `未能找到用户“${ctx.loginUser.username}”信息`
                    };
                }

                if (findEmpInfo.success === false) {
                    return {
                        reply: `请求失败 -> ${list.msg}`
                    };
                }

                opts.outputState('查询任务信息中');
                const list = await ctx.execPlugin({
                    name: 'listMyNotReadTaskUpdateHistory',
                    params: {
                        userId: findEmpInfo.UserId, //ctx.loginUser.id
                    }
                });

                if (list.success === false) {
                    return {
                        reply: `请求失败 -> ${list.msg}`
                    };
                }

                // list根据CrtTime降序排序
                list.sort((a, b) => new Date(b.CrtTime).getTime() - new Date(a.CrtTime).getTime());

                result = `在“${ctx.timeUtil.toDateTimeStr(new Date())}”查到${list.length}条我的未读任务：\n`;
                result += `\n`;

                let index = 1;
                for (const item of list) {
                    result += `${index++}. ${item.TaskName}\n`;
                    result += `   - ${item.UpdateRN}：${ctx.htmlUtil.clearHtml(item.Note)} [${ctx.timeUtil.toDateTimeStr(new Date(item.CrtTime))}]\n`;
                    result += `   - 事项：${item.PTaskName}\n`;
                    result += `   - 对象：${item.ObjectName}\n`;
                    if (item.DailyWorkHours && item.DailyWorkHours.length > 0) {
                        result += `   - 工时：${item.DailyWorkHours}\n`;
                    }
                    result += `   - ---\n`;
                    result += `\n`;
                }

                return {
                    reply: result
                };
            }
        },
        // 列出某用户未完成的任务
        {
            name: 'listUserWWCTask',
            descr: '列出某用户未完成的任务',
            params: {
                required: ['usrRn'],
                props: {
                    usrRn: {
                        type: 'string',
                        descr: '用户姓名',
                    }
                }
            },
            call: async function(params) {
                let list = [];
                let result = '';
                const usrRn = params.usrRn;

                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'listUserWWCTask',
                        pars: {
                            usrRn: usrRn
                        }
                    }
                });
                if (r.success === false) {
                    return `请求失败 -> ${r.msg}`;
                }
                list = r.data;

                const cols = [
                    { name: '{index}', label: '序号' },
                    { name: 'Name', label: '任务名', descr: '' },
                    { name: 'PName', label: '事项', descr: '' },
                    { name: 'ObjName', label: '对象', descr: '' },
                ];

                result = `在“${ctx.timeUtil.toDateTimeStr(new Date())}”查到${list.length}条任务：\n`;
                result += ctx.strUtil.buildMarkdownTable(cols, list);

                return {
                    reply: result
                };
            }
        },
        // 查看任务成本
        {
            name: 'seeTaskCost',
            descr: '查看任务成本，根据任务里人员上报的工时来计算出成本',
            needRoles: ['admin', '成本管理员'],
            params: {
                required: ['taskName'],
                props: {
                    taskName: {
                        type: 'string',
                        descr: '任务名或任务名关键词',
                    }
                }
            },
            call: async function(params, opts) {
                const taskName = params.taskName;
                let find_task_id;

                opts.outputState('查询任务中');
                const list = await ctx.execPlugin({
                    name: '_findTaskByNameKw',
                    params: {
                        kw: taskName
                    }
                });
                if (list.success === false) {
                    return {
                        reply: `请求失败 -> ${list.msg}`
                    };
                }

                let result = '';
                if (list.length > 1) {
                    // 生成回复
                    result = `查到${list.length}条任务，请选择一个来查看成本：\n`;
                    for (const i in list) {
                        const item = list[i];
                        result += `${parseInt(i) + 1}. ${item.Name}\n`;
                    }

                    // 添加临时记忆（任务列表）
                    ctx.chatTempMemoryUtil.set('multi_task_list', list);

                    // 添加临时系统记忆
                    {
                        let memoryStr = '';
                        const cols = [
                            { name: '{index}', label: '序号' },
                            { name: 'Name', label: '任务名', descr: '', maxLength: 30 }
                        ];
                        memoryStr = `查到${list.length}条任务，请选择一个来查看成本：\n`;
                        memoryStr += ctx.strUtil.buildMarkdownTable(cols, list);

                        ctx.chatTempMemoryUtil.clearSys();
                        ctx.chatTempMemoryUtil.setSys('multi_list', memoryStr);
                    }

                    return {
                        reply: result
                    }
                }
                else if (list.length === 1) {
                    find_task_id = list[0].Id;
                }
                else {
                    return {
                        reply: '未能找到匹配的任务'
                    }
                }

                if (find_task_id == null) {
                    // 加载临时记忆
                    const ctm_list_str = ctx.chatTempMemoryUtil.get('multi_task_list');
                    // 删除临时记忆
                    ctx.chatTempMemoryUtil.remove('multi_task_list');
                    const ctm_list = JSON.parse(ctm_list_str);
                    for (const i in ctm_list) {
                        const item = ctm_list[i];
                        if (item.Name === taskName) {
                            find_task_id = item.Id;
                            break;
                        }
                    }
                }

                // 清理临时系统记忆
                ctx.chatTempMemoryUtil.clearSys();

                opts.outputState('查询任务成本中');
                const report = await ctx.execPlugin({
                    name: 'getTaskCostReport',
                    params: {
                        id: find_task_id
                    }
                });
                result = `下面是任务“${taskName}”的成本报告：\n`;
                result += `成本总计${report.TotalCost}元，共上报工时${report.TotalWorkHours}小时\n\n`;
                result += `人员成本明细：\n`;

                report.UserCosts.sort((b, a) => {
                    if (a.Cost > b.Cost) {
                        return 1;
                    }
                    else if (a.Cost < b.Cost) {
                        return -1;
                    }
                    return 0;
                })

                for (const i in report.UserCosts) {
                    const userCost = report.UserCosts[i];
                    result += `- ${userCost.Realname} 成本：${userCost.Cost}元，上报工时：${userCost.WorkHours}小时\n`;
                }
                return {
                    reply: result
                };
            }
        },
        // 查看事项成本
        {
            name: 'seePTaskCost',
            descr: '查看事项成本，根据所属此事项的任务里人员上报的工时来计算出成本',
            needRoles: ['admin', '成本管理员'],
            params: {
                required: ['taskName'],
                props: {
                    taskName: {
                        type: 'string',
                        descr: '事项名或事项名关键词',
                    }
                }
            },
            call: async function(params, opts) {
                const taskName = params.taskName;
                let find_task_id;

                opts.outputState('查询事项中');
                const list = await ctx.execPlugin({
                    name: '_findPTaskByNameKw',
                    params: {
                        kw: taskName
                    }
                });
                if (list.success === false) {
                    return {
                        reply: `请求失败 -> ${list.msg}`
                    };
                }

                let result = '';
                if (list.length > 1) {
                    // 生成回复
                    result = `查到${list.length}条任务，请选择一个来查看成本：\n`;
                    for (const i in list) {
                        const item = list[i];
                        result += `${parseInt(i) + 1}. ${item.Name}\n`;
                    }

                    // 添加临时记忆（任务列表）
                    ctx.chatTempMemoryUtil.set('multi_task_list', list);

                    // 添加临时系统记忆
                    {
                        let memoryStr = '';
                        const cols = [
                            { name: '{index}', label: '序号' },
                            { name: 'Name', label: '任务名', descr: '', maxLength: 30 }
                        ];
                        memoryStr = `查到${list.length}条任务，请选择一个来查看成本：\n`;
                        memoryStr += ctx.strUtil.buildMarkdownTable(cols, list);

                        ctx.chatTempMemoryUtil.clearSys();
                        ctx.chatTempMemoryUtil.setSys('multi_list', memoryStr);
                    }

                    return {
                        reply: result
                    }
                }
                else if (list.length === 1) {
                    find_task_id = list[0].Id;
                }
                else {
                    return {
                        reply: '未能找到匹配的任务'
                    }
                }

                if (find_task_id == null) {
                    // 加载临时记忆
                    const ctm_list_str = ctx.chatTempMemoryUtil.get('multi_task_list');
                    // 删除临时记忆
                    ctx.chatTempMemoryUtil.remove('multi_task_list');
                    const ctm_list = JSON.parse(ctm_list_str);
                    for (const i in ctm_list) {
                        const item = ctm_list[i];
                        if (item.Name === taskName) {
                            find_task_id = item.Id;
                            break;
                        }
                    }
                }

                // 清理临时系统记忆
                ctx.chatTempMemoryUtil.clearSys();

                opts.outputState('查询事项成本中');
                const report = await ctx.execPlugin({
                    name: 'getPTaskCostReport',
                    params: {
                        id: find_task_id
                    }
                });
                result = `下面是任务“${taskName}”的成本报告：\n`;
                result += `成本总计${report.TotalCost}元，共上报工时${report.TotalWorkHours}小时，共${report.ChildTaskCount}条任务\n\n`;
                result += `人员成本明细：\n`;

                report.UserCosts.sort((b, a) => {
                    if (a.Cost > b.Cost) {
                        return 1;
                    }
                    else if (a.Cost < b.Cost) {
                        return -1;
                    }
                    return 0;
                })

                for (const i in report.UserCosts) {
                    const userCost = report.UserCosts[i];
                    result += `- ${userCost.Realname} 成本：${userCost.Cost}元，上报工时：${userCost.WorkHours}小时\n`;
                }
                return {
                    reply: result
                };
            }
        },
        // 查询某用户或某机构的手机号和电话号码
        {
            name: 'searchUserOrOrgPhone',
            descr: '查询某用户或某机构的手机号和电话号码',
            params: {
                required: ['kw'],
                props: {
                    kw: {
                        type: 'string',
                        descr: '某用户姓名关键词或某机构名称关键词，至少两个中文字。举例1：查询大疆号码 -> 大疆；举例2：吉普号码 -> 吉普;',
                    }
                }
            },
            // 调用工具
            call: async function(params) {
                let result = '';
                const kw = params.kw;

                const list = await ctx.execPlugin({
                    name: 'searchUserOrOrgPhone',
                    params: {
                        kw: kw
                    }
                });

                // log('【调用工具】', '插件返回共', list.length, '条');
                // log('【调用工具】', '插件返回结果：', JSON.stringify(list));
                if (list.success === false) {
                    return {
                        reply: `请求失败 -> ${list.msg}`
                    };
                }

                const cols = [
                    { name: 'name', label: '名称', descr: '用户名或机构名' },
                    { name: 'phoneInfo', label: '号码信息', descr: '手机号或电话号码' }
                ];

                result = `根据关键词“${kw}”查到的号码：\n`;
                result += ctx.strUtil.buildMarkdownTable(cols, list);
                return {
                    reply: result
                };
            }
        },
        // 获取某人任务队列列表
        {
            name: 'getUserCtskQueueListByCtskRn',
            descr: '获取某人的任务队列',
            params: {
                required: ['ctskRn'],
                props: {
                    ctskRn: {
                        type: 'string',
                        descr: '人名',
                    }
                }
            },
            // 调用工具
            call: async function(params) {
                let list = [];
                let result = '';
                const ctskRn = params.ctskRn;
                let realName = params.realName;

                // 检查用户是否只有一个
                if (realName == null || realName.length === 0)
                {
                    const r = await ctx.execPlugin({
                        name: '_checkUserRealNameKwUnique',
                        params: {
                            kw: ctskRn,
                            label: '请选择一个来查看此用户的任务队列'
                        }
                    });
                    if (r.success === false) {
                        return `请求失败 -> ${r.msg}`;
                    }
                    else if (r.success === true) {
                        // 找到一个用户
                        if (r.data) {
                            realName = r.data;
                        }
                        // 找到多个用户
                        else if (r.reply) {
                            // 添加临时记忆（人员列表）
                            ctx.chatTempMemoryUtil.clearSys();
                            ctx.chatTempMemoryUtil.setSys('multi_list', r.reply);
                            return {
                                reply: r.reply
                            }
                        }
                    }
                }

                list = await ctx.execPlugin({
                    name: 'getUserCtskQueueListByCtskRn',
                    params: {
                        ctskRn: realName
                    }
                });

                if (list.success === false) {
                    return {
                        reply: `请求失败 -> ${list.msg}`
                    };
                }

                if (list.length === 0) {
                    return `此用户没有任务队列`;
                }

                // log(JSON.stringify(list))

                // 添加临时记忆（子任务列表）
                {
                    let memoryStr = '';
                    const cols = [
                        { name: 'index', label: '序号', descr: '在队列中的序号' },
                        { name: 'ctsk_name', label: '任务名', descr: '', maxLength: 30 }
                    ];
                    memoryStr = `查到用户“${realName}”的任务队列：\n`;
                    memoryStr += ctx.strUtil.buildMarkdownTable(cols, list);

                    ctx.chatTempMemoryUtil.clearSys();
                    ctx.chatTempMemoryUtil.setSys('multi_list', memoryStr);
                }

                {
                    const cols = [
                        { name: 'index', label: '序号', descr: '在队列中的序号' },
                        { name: 'details', label: '详细', descr: '' },
                        // { name: 'ctsk_name', label: '任务名', descr: '' }
                    ];

                    result = `查到用户“${realName}”的任务队列：\n`;
                    result += ctx.strUtil.buildMarkdownTable(cols, list.map((n) => {
                        return {
                            index: n.index,
                            details: `任务名：${n.ctsk_name}<br>
<span style="color: gray;">对&emsp;象：${n.obj_name}</span>`
                        }
                    }));
                }

                return {
                    reply: result
                };
            }
        },
        // 移动队列中某任务到最前位置
        {
            name: 'moveCtskQueueToStartByCtskRn',
            descr: '移动队列中某任务到最前位置',
            params: {
                required: ['ctskRn', 'fromIndex'],
                props: {
                    ctskRn: {
                        type: 'string',
                        descr: '人名',
                    },
                    fromIndex: {
                        type: 'number',
                        descr: '任务序号',
                    }
                }
            },
            // 调用工具
            call: async function(params) {
                let result = '';
                const ctskRn = params.ctskRn;
                const fromIndex = params.fromIndex;
                let realName = params.realName;

                // 检查用户是否只有一个
                if (realName == null || realName.length === 0) {
                    const r = await ctx.execPlugin({
                        name: '_checkUserRealNameKwUnique',
                        params: {
                            kw: ctskRn
                        }
                    });
                    if (r.success === false) {
                        return `请求失败 -> ${r.msg}`;
                    }
                    else if (r.success === true) {
                        realName = r.data;
                    }
                    else {
                        return r;
                    }
                }

                // 调用插件
                {
                    const r = await ctx.execPlugin({
                        name: 'moveCtskQueueToStartByCtskRn',
                        params: {
                            ctskRn: realName,
                            fromIndex: fromIndex
                        }
                    });
                    if (r.success === false) {
                        return `请求失败 -> ${r.msg}`;
                    }
                }

                result = `已调整”${realName}“的任务队列，将序号${fromIndex}移动到最前位置`;
                return {
                    reply: result,
                }
            }
        },
        // 移动队列中某任务到最后位置
        {
            name: 'moveCtskQueueToEndByCtskRn',
            descr: '移动队列中某任务到最后位置',
            params: {
                required: ['ctskRn', 'fromIndex'],
                props: {
                    ctskRn: {
                        type: 'string',
                        descr: '人名',
                    },
                    fromIndex: {
                        type: 'number',
                        descr: '任务序号',
                    }
                }
            },
            // 调用工具
            call: async function(params) {
                let result = '';
                const ctskRn = params.ctskRn;
                const fromIndex = params.fromIndex;
                let realName = params.realName;

                // 检查用户是否只有一个
                if (realName == null || realName.length === 0)
                {
                    const r = await ctx.execPlugin({
                        name: '_checkUserRealNameKwUnique',
                        params: {
                            kw: ctskRn
                        }
                    });
                    if (r.success === false) {
                        return `请求失败 -> ${r.msg}`;
                    }
                    else if (r.success === true) {
                        realName = r.data;
                    }
                    else {
                        return r;
                    }
                }

                // 调用插件
                {
                    const r = await ctx.execPlugin({
                        name: 'moveCtskQueueToEndByCtskRn',
                        params: {
                            ctskRn: realName,
                            fromIndex: fromIndex
                        }
                    });
                    if (r.success === false) {
                        return `请求失败 -> ${r.msg}`;
                    }
                }

                result = `已调整”${realName}“的任务队列，将序号${fromIndex}移动到最后位置`;
                return {
                    reply: result,
                }

                // // 调用工具
                // result = await ctx.callTool({
                //     name: 'getUserCtskQueueListByCtskRn',
                //     params: {
                //         realName: realName,
                //         ctskRn: null
                //     }
                // });

                // // log('【调用工具】', result);
                // return {
                //     reply: result.reply
                // };
            }
        },
        // 移动队列中某任务到指定位置
        {
            name: 'moveCtskQueueToIndexByCtskRn',
            descr: '移动队列中某任务到指定位置',
            params: {
                required: ['ctskRn', 'fromIndex', 'toIndex'],
                props: {
                    ctskRn: {
                        type: 'string',
                        descr: '人名',
                    },
                    fromIndex: {
                        type: 'number',
                        descr: '任务原序号',
                    },
                    toIndex: {
                        type: 'number',
                        descr: '任务新序号',
                    }
                }
            },
            // 调用工具
            call: async function(params) {
                let result = '';
                const ctskRn = params.ctskRn;
                const fromIndex = params.fromIndex;
                const toIndex = params.toIndex;
                let realName = params.realName;

                // 检查用户是否只有一个
                if (realName == null || realName.length === 0)
                {
                    const r = await ctx.execPlugin({
                        name: '_checkUserRealNameKwUnique',
                        params: {
                            kw: ctskRn
                        }
                    });
                    if (r.success === false) {
                        return `请求失败 -> ${r.msg}`;
                    }
                    else if (r.success === true) {
                        realName = r.data;
                    }
                    else {
                        return r;
                    }
                }

                let toIndex1 = toIndex;
                if (fromIndex < toIndex) {
                    toIndex1 = String(Number(toIndex) + 1);
                }

                // 调用插件
                {
                    const r = await ctx.execPlugin({
                        name: 'moveCtskQueueToIndexByCtskRn',
                        params: {
                            ctskRn: realName,
                            fromIndex: fromIndex,
                            toIndex: toIndex1
                        }
                    });
                    if (r.success === false) {
                        return `请求失败 -> ${r.msg}`;
                    }
                }

                log(`【调试】移动任务队列从 ${fromIndex} 到 ${toIndex}（${toIndex1}）`)

                result = `已调整”${realName}“的任务队列，将序号${fromIndex}移动到序号${toIndex}的位置`;
                return {
                    reply: result,
                }

                // // 调用工具
                // result = await ctx.callTool({
                //     name: 'getUserCtskQueueListByCtskRn',
                //     params: {
                //         realName: realName,
                //         ctskRn: null
                //     }
                // });

                // // log('【调用工具】', result);
                // return {
                //     reply: result.reply
                // };
            }
        },
        // 给某人创建事项
        {
            name: 'createPTaskByRn',
            descr: '给某人创建事项',
            params: {
                required: ['name', 'usrRn', 'crtUsrRn'],
                props: {
                    name: {
                        type: 'string',
                        descr: '事项名',
                    },
                    usrRn: {
                        type: 'string',
                        descr: '事项操作人姓名（事项所属人），如没有指定那就是当前用户',
                    },
                    crtUsrRn: {
                        type: 'string',
                        descr: '任务创建人姓名（当前用户）',
                    },
                    objName: {
                        type: 'string',
                        descr: '事项挂钩的对象名',
                    },
                }
            },
            // 调用工具
            call: async function(params, opts) {
                let result = '';
                const name = params.name;
                const usrRn = params.usrRn;
                const crtUsrRn = params.crtUsrRn;
                const objName = params.objName;

                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'createPTaskByRn',
                        pars: {
                            name: name,
                            usrRn: usrRn,
                            crtUsrRn: crtUsrRn,
                            objName: objName,
                        }
                    }
                });
                log(`【工具调试】`, `请求返回结果：`, JSON.stringify(r))
                if (r.success === false) {
                    log(`【错误】创建事项异常 -> ${r.msg}`)
                    return {
                        reply: `创建事项异常`
                    }
                }

                if (r.msg && r.msg.length > 0) {
                    ctx.chatTempMemoryUtil.clearSys();
                    ctx.chatTempMemoryUtil.setSys('multi_list', `创建事项失败 -> ${r.msg}`);
                    return {
                        reply: `创建事项失败 -> ${r.msg}`
                    }
                }

                ctx.chatTempMemoryUtil.clearSys();
                result = `已为“${usrRn}”创建事项“${name}”（创建人：${crtUsrRn}）`;

                return {
                    reply: result
                };
            }
        },
        // 给某人创建任务
        {
            name: 'createTaskByRn',
            descr: '给某人创建任务',
            params: {
                required: ['name', 'ptskOrObjName', 'usrRn', 'crtUsrRn'],
                props: {
                    name: {
                        type: 'string',
                        descr: '任务名',
                    },
                    ptskOrObjName: {
                        type:'string',
                        descr: '事项或对象名',
                    },
                    usrRn: {
                        type: 'string',
                        descr: '任务操作人姓名（任务所属人）',
                    },
                    crtUsrRn: {
                        type: 'string',
                        descr: '任务创建人姓名（当前用户）',
                    },
                }
            },
            // 调用工具
            call: async function(params, opts) {
                let result = '';
                const name = params.name;
                const usrRn = params.usrRn;
                const crtUsrRn = params.crtUsrRn;
                const ptskOrObjName = params.ptskOrObjName;

                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'createTaskByRn',
                        pars: {
                            name: name,
                            usrRn: usrRn,
                            ptskOrObjName: ptskOrObjName,
                            crtUsrRn: crtUsrRn
                        }
                    }
                });
                log(`【工具调试】`, `请求返回结果：`, JSON.stringify(r))
                if (r.success === false) {
                    log(`【错误】创建任务异常 -> ${r.msg}`)
                    return {
                        reply: `创建任务异常`
                    }
                }

                if (r.msg && r.msg.length > 0) {
                    ctx.chatTempMemoryUtil.clearSys();
                    ctx.chatTempMemoryUtil.setSys('multi_list', `创建任务失败 -> ${r.msg}`);
                    return {
                        reply: `创建任务失败 -> ${r.msg}`
                    }
                }

                ctx.chatTempMemoryUtil.clearSys();
                result = `已为“${usrRn}”创建任务“${name}”（所属事项名：${ptskOrObjName}，创建人：${crtUsrRn}）`;

                return {
                    reply: result
                };
            }
        },
        // 给某人创建子任务
        {
            name: 'createChildTaskByRn',
            descr: '给某人创建子任务，子任务是某个任务的子项，所以叫做子任务',
            params: {
                required: ['name', 'tskName', 'usrRn', 'crtUsrRn'],
                props: {
                    name: {
                        type: 'string',
                        descr: '子任务名',
                    },
                    tskName: {
                        type:'string',
                        descr: '任务名',
                    },
                    usrRn: {
                        type: 'string',
                        descr: '子任务操作人姓名（子任务所属人）',
                    },
                    crtUsrRn: {
                        type: 'string',
                        descr: '任务创建人姓名（当前用户）',
                    }
                }
            },
            // 调用工具
            call: async function(params) {
                let result = '';
                const name = params.name;
                const tskName = params.tskName;
                const usrRn = params.usrRn;
                const crtUsrRn = params.crtUsrRn;
                let realName = params.realName;

                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'createChildTaskByRn',
                        pars: {
                            name: name,
                            usrRn: usrRn,
                            tskName: tskName,
                            crtUsrRn: crtUsrRn
                        }
                    }
                });
                log(`【工具调试】`, `请求返回结果：`, JSON.stringify(r))
                if (r.success === false) {
                    log(`【错误】创建子任务异常 -> ${r.msg}`)
                    return {
                        reply: `创建子任务异常`
                    }
                }

                if (r.msg && r.msg.length > 0) {
                    ctx.chatTempMemoryUtil.clearSys();
                    ctx.chatTempMemoryUtil.setSys('multi_list', `创建任务失败 -> ${r.msg}`);
                    return {
                        reply: `创建子任务失败 -> ${r.msg}`
                    }
                }

                ctx.chatTempMemoryUtil.clearSys();
                result = `已为“${usrRn}”创建子任务“${name}”（所属任务名：${tskName}，创建人：${crtUsrRn}）`;

                return {
                    reply: result
                };
            }
        },
        // 列出待审子任务工时
        {
            name: 'listReviewWorkHours',
            descr: '罗列出需要带审核的子任务工时列表',
            params: {
                required: [],
                props: {
                }
            },
            // 调用工具
            call: async function(params) {
                let result = '';

                const now = new Date();
                let endDate = ctx.timeUtil.toDateStr(now);
                let end = endDate + ' 23:59:59';
                let startDate = ctx.timeUtil.toDateStr(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000));
                let start = startDate + ' 00:00:00';

                const r = await ctx.execPlugin({
                    name: 'reqCZ2021TaskByCmd',
                    params: {
                        cmd: '/workhours/getreviewworkhoursreport',
                        pars: {
                            flow: 0,
                            loginInfo: {
                                uid: ctx.loginUser.id,
                                un: ctx.loginUser.username,
                                rn: ctx.loginUser.realname,
                                sac: '',
                            },
                            start: start,
                            end: end
                        },
                    }
                });
                if (r.success === false) {
                    log(`【错误】获取待审工时异常 -> ${r.msg}`)
                    return {
                        reply: `获取待审工时异常 -> ${r.msg}`
                    };
                }

                const list = r.data.items;

                // 添加临时记忆（子任务列表）
                {
                    // let memoryStr = '';
                    // const cols = [
                    //     { name: 'index', label: '序号', descr: '' },
                    //     { name: 'targetParentName', label: '事项', descr: '' },
                    //     { name: 'targetName', label: '任务', descr: '子任务所属的任务名' },
                    //     { name: 'createUserName', label: '人员', descr: '子任务操作人姓名' },
                    //     { name: 'hours', label: '工时', descr: '子任务工时' },
                    //     { name: 'content', label: '说明', descr: '' },
                    //     { name: 'id', label: '子任务ID', descr: '' },
                    // ];
                    // memoryStr = `有${r.data.items.length}条待审工时（${startDate} ~ ${endDate}）：\n`;
                    // memoryStr += ctx.strUtil.buildMarkdownTable(cols, list);

                    const tempMemoryList = [];
                    list.forEach((item, index) => {
                        tempMemoryList.push({
                            index: index + 1,
                            id: item.id,
                        });
                    });
                    ctx.chatTempMemoryUtil.clearSys();
                    ctx.chatTempMemoryUtil.setSys('待审子任务工时列表', tempMemoryList);
                }

                // 生成回复内容
                {
                    const toReplyList = [];
                    let cols = [
                        { name: 'index', label: '序号', descr: '' },
                        { name: 'details', label: '详细', descr: '' },
                        // { name: 'targetParentName', label: '事项', descr: '' },
                        // { name: 'targetName', label: '任务', descr: '子任务所属的任务名' },
                        // { name: 'createUserName', label: '人员', descr: '子任务操作人姓名' },
                        // { name: 'hours', label: '工时', descr: '子任务工时' },
                        // { name: 'content', label: '说明', descr: '' },
                    ];

                    list.forEach((item, index) => {
                        item.index = index + 1;
                        toReplyList.push({
                            index: item.index,
                            details: `任务：${item.targetName}；<br/>
人员：${item.createUserName}；工时：${item.hours}<br/>
事项：${item.targetParentName}；<br/>
说明：${item.content}；
`
                        })
                    });

                    result = `有${r.data.items.length}条待审工时（${startDate} ~ ${endDate}）：\n`;
                    result += ctx.strUtil.buildMarkdownTable(cols, toReplyList);
                }

                return {
                    reply: result
                };
            }
        },
        // 按特定工时批准子任务工时
        {
            name: 'approveChildTaskWorkHours1',
            descr: `按特定工时批准子任务工时，只有当用户要求按特定工时批准时才使用
            例：{第n条按{hours}小时通过，因为{reason}}`,
            params: {
                required: ['index', 'user', 'hours', 'reason'],
                props: {
                    index: {
                        type: 'string',
                        descr: '子任务序号',
                    },
                    user: {
                        type: 'string',
                        descr: '子任务操作人姓名',
                    },
                    hours: {
                        type: 'number',
                        descr: '用户指定的子任务工时',
                    },
                    reason: {
                        type: 'string',
                        descr: '调整工时原因，例：{第3条按2小时通过，理由是已调整过工作内容} -> 已调整过工作内容',
                    }
                }
            },
            // 调用工具
            call: async function(params) {
                let result = '';

                const index = params.index;
                const user = params.user;
                const hours = params.hours;
                const reason = params.reason;

                // 加载临时记忆
                const dszrwgsList = ctx.chatTempMemoryUtil.get('待审子任务工时列表');
                // 根据序号找到id
                const findId = dszrwgsList.find(a=> String(a.index) === String(index)).id;

                log(`批准子任务工时：${user}|${index}|${findId} ...`);
                const r = await ctx.execPlugin({
                    name: 'reqCZ2021TaskByCmd',
                    params: {
                        cmd: '/workhours/audit1',
                        pars: {
                            loginInfo: {
                                uid: ctx.loginUser.id,
                                un: ctx.loginUser.username,
                                rn: ctx.loginUser.realname,
                                sac: '',
                            },
                            form: {
                                id: findId,
                                flow: 1,
                                hoursEnd: hours,
                                contentEnd: reason
                            }
                        },
                    }
                });
                if (r.success === false) {
                    log(`【错误】按特定工时批准子任务工时异常 -> ${r.msg}`)
                    return {
                        reply: `按特定工时批准子任务工时异常 -> ${r.msg}`
                    };
                }

                result = `已批准“${user}”的子任务${hours}小时，原因是${reason}（${id}）`;

                return {
                    reply: result
                };
            }
        },
        // 批准多条待审子任务工时
        {
            name: 'approveMultiChildTaskWorkHours',
            descr: `批准单条或多条待审子任务工时，只有用户说全部批准才是多条，其它情况都是单条
            例1：{批准第n条}
            例2：{批准n} = 批准第n条`,
            params: {
                required: ['index', 'user'],
                props: {
                    index: {
                        type: 'string',
                        descr: '子任务序号，多个需要用英文逗号分隔，例：1,2,3',
                    },
                    user: {
                        type: 'string',
                        descr: '子任务操作人姓名',
                    }
                }
            },
            // 调用工具
            call: async function(params) {
                let result = '';

                let count = 0;
                const indexStr = params.index;
                const user = params.user;

                const indexList = indexStr.split(',');

                // 加载临时记忆
                const dszrwgsList = ctx.chatTempMemoryUtil.getSys('待审子任务工时列表');
                // log(`加载临时记忆：${JSON.stringify(dszrwgsList)}`)

                for (const index of indexList) {
                    // 根据序号找到id
                    const findId = dszrwgsList.find(a=> String(a.index) === String(index)).id;

                    log(`批量批准子任务工时：${user}|${index}|${findId} ...`);
                    const r = await ctx.execPlugin({
                        name: 'approveChildTaskWorkHour',
                        params: {
                            id: findId
                        }
                    });
                    if (r.success === false) {
                        const errMsg = `【错误】批准子任务工时异常（${user}|${index}|${findId}） -> ${r.msg}`;
                        log(errMsg)
                        return {
                            reply: errMsg
                        };
                    }
                    count++;
                }

                result = `已批准${count}条待“${ctx.loginUser.realname}”审核的子任务工时`;

                return {
                    reply: result
                };
            }
        }
    ]
})