(function (ctx, opts) {
    return [
        // 打开网页给用户看
        {
            name: 'openUrlForUser',
            descr: '打开网页给用户看',
            params: {
                required: [],
                props: {
                    url: {
                        type: 'string',
                        descr: '打开的网页地址',
                    },
                }
            },
            call: async function(params, opts) {
                const url = params.url;

                // 直接返回字符串，会追加一轮对话让AI进行回复
                // 如果返回对象设置reply属性，会直接回复
                return {
                    reply: `<cs-ai-reply-cmd style="display: none;">${JSON.stringify({ type: 'open-url', url })}</cs-ai-reply-cmd>\n`,
                    replyToAgent: '已成功打开网址',
                    // 继续对话
                    continue: true,
                };
            }
        }
    ]
})