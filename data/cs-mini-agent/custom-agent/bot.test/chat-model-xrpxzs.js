(function (ctx) {
    // 新人培训助手
    return {
        parentId: 'chat-model',
        name: '公司运行手册',
        summary: '帮助你了解公司及制度',
        settings: {
            maxChatHistoryLength: 6, // 对话时塞入的历史对话条数限制
            chatHistoryBotReplyMaxSize: 1, // 对话时塞入的历史AI回复内容大小限制
            config: {
                kbId: '20240622125505178-f7ebc1',
                kbListMaxSize: 5,
            },
            chatOpts: {
                chatModel: 'albl_qwen3-235b-a22b|no_think|', // 推荐
                // chatModel: 'ollama_deepseek-r1:14b', // 推荐
                // chatModel: 'ollama_qwq:32b',
                // chatModel: 'ollama_qwen2.5:14b',
                // chatModel: 'ollama_qwen3:32b|no_think|',
                // chatModel: 'ollama_qwen3:14b|no_think|',
                // chatModel: 'albl_qwen-turbo',
                // toolModel: 'albl_qwen-turbo',
                toolModel: 'ollama_qwen3:14b|no_think|',
                // toolModel: 'zhipu_glm-4-plus',
                // toolModel: 'albl_qwen-plus',
            },
            getSystemMessage(pars, opts, ctx) {
                let str = ``;

                str += `
                ### 上下文
                #### 当前日期
                ${ctx.timeUtil.toDateStr(new Date())}
                #### 当前时间
                ${ctx.timeUtil.toTimeStr(new Date())}
                `;

                str += `
                ### 任务说明
                #### 角色要求
                - 你是知识库助手，通过调用工具来查询知识后回答`

                return str;
            },
            buildSystemMessageBeforeChat(pars, opts, ctx) {

                if (pars.type === 'chat') {
                    // 加载未找到回复
                    let notFindReply = '对不起，暂时没有找到相关知识';
                    return pars.systemMessage + `
                - 在调用工具后如果没能在知识库中找到答案，请回复“${notFindReply}”
                `;
                }

                return pars.systemMessage;
            },
            // getChatHistory(pars) {
            //     const list = [];
            //     for (const i in pars.chatHistory) {
            //         const item = pars.chatHistory[i];
            //         if (item.content.indexOf('（下面内容不在对话中显示）' !== -1)) {
            //             console.log('过滤：', JSON.stringify(item))
            //         }
            //         else {
            //             list.push(item);
            //         }
            //     }
            //     return list;
            // },
            // // 获取到消息后
            // async onGetReceiveMessage(receiveMessage) {
            //     return receiveMessage;
            // },
        },
    };
})