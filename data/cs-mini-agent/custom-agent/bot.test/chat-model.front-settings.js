(function() {
    return {
        sendMessageType: 'stream',
        // 获取初始提示词（欢迎词）
        async getInitialPrompt() {
            return {
                content: `### 你来啦，快来一起对话吧~
            `
            };
        },
        // 输入框内部工具
        inputInnerTools: [
            // 模型选择器
            {
                name: 'model',
                type: 'select',
                placeholder: '请选择模型',
                value: 'ollama_deepseek-r1:14b',
                options: [
                    /*** Ollama ***/
                    {
                        label: 'DeepSeek-R1-14b（OLL）',
                        value: 'ollama_deepseek-r1:14b',
                    },
                    {
                        label: 'Qwen3-32b（OLL）',
                        value: 'ollama_qwen3:32b',
                        canToolCall: true,
                    },
                    {
                        label: 'Qwen3-32b【不思考】（OLL）',
                        value: 'ollama_qwen3:32b|no_think|',
                        canToolCall: true,
                    },
                ]
            }
        ],
    };
})