(function () {
    return {
        sendMessageType: 'stream',
        // 获取初始提示词（欢迎词）
        async getInitialPrompt(pars) {
            let hasAdminRole = false;
            if (pars.loginUser && pars.loginUser.roles) {
                for (const role of pars.loginUser.roles) {
                    if (role === 'admin' || role === '成本管理员') {
                        hasAdminRole = true;
                        break;
                    }
                }
            }

            return {
                content: `### 你好，很高兴与你交流，下面是我能做的事：
- 【查看超时任务】
- 【查询某人或某单位号码】
    - 例：查看xx的号码
- 【查看某人任务队列/调整排序】
    - 【第一步】例：查看xx的任务队列
    - 【第二步a】移动1到3
    - 【第二步b】移动3到最前
    - 【第二步c】移动3到最后
- 【查询某人任务】
    - 例：查看xx任务
- 【创建事项】
    - 例1：给xx创建事项叫 xxx
- 【创建任务】
    - 例1：给xx创建任务叫 xxx，事项名为 yyy（如果出现多条事项，输入选择第几个）
    - 例2：给xx创建任务叫 xxx，对象名为 yyy（如果出现多条事项，输入选择第几个）
- 【创建子任务】例：给xx创建子任务叫 xxx，任务名为 yyy（如果出现多条任务，输入选择第几个）
- 【查看待审工时】
    - 【第一步】例：查看待审工时
    - 【第二步a】批准第n条
    - 【第二步b】第n条按h小时批准，因为xxx
    - 【第二步c】全部批准
- 【查看自己未读任务更新历史】
    - 例：列出我的未读任务更新历史` + (hasAdminRole ? `
- 【查看任务成本】
    - 例：查看任务成本，名称是：xxx
- 【查看事项成本】
    - 例：查看事项成本，名称是：xxx` : '')
            };
        },
        // 获取到消息后
        async onGetReceiveMessage(receiveMessage) {
            return receiveMessage;
        },
        // 交互面板提交
        async onInteractionPanelSubmit(e) {
        },
    };
})