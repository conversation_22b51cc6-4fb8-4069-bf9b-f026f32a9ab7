(function (ctx) {
    // 插件
    return {
        // 更新交易
        updateDeal: {
            exec: async function(params) {
                const { httpUtil } = ctx;
                const r = await httpUtil
                .reqPostFormSync(`http://erp.ovinfo.com:8800/admin/handler.ashx?cmd=cs_finance_updDeal`, params);
                return r;
            }
        },
        _getTestData0: {
            exec: async function(params) {
                const r = {
                    "data": {
                        "reply": "共6条记录，请在交互面板继续操作",
                        "usage": {
                            "total": 1739,
                            "input": 1446,
                            "output": 293
                        },
                        "id": "1742616561072",
                        "time": 1742616561072,
                        "interactionPanelSettings": {
                            "type": "table",
                            "canMultiSelect": true,
                            "columns": [
                                {
                                    "label": "收支类型",
                                    "name": "type",
                                    "width": 90
                                },
                                {
                                    "label": "日期",
                                    "name": "date",
                                    "width": 100
                                },
                                {
                                    "label": "时间",
                                    "name": "time",
                                    "width": 90
                                },
                                {
                                    "label": "金额",
                                    "name": "money"
                                },
                                {
                                    "label": "备注",
                                    "name": "note"
                                }
                            ],
                            "rows": [
                                {
                                    "type": "利息",
                                    "date": "2025-03-21",
                                    "time": "01:11:35",
                                    "money": 4.26,
                                    "note": "批量业务"
                                },
                                {
                                    "type": "退款",
                                    "date": "2025-03-20",
                                    "time": "16:47:18",
                                    "money": 0.06,
                                    "note": "支付宝-上海壹佰米网络科技..."
                                },
                                {
                                    "type": "消费",
                                    "date": "2025-03-20",
                                    "time": "11:58:57",
                                    "money": -64.98,
                                    "note": "支付宝-上海壹佰米网络科技..."
                                },
                                {
                                    "type": "消费",
                                    "date": "2025-03-19",
                                    "time": "18:47:06",
                                    "money": -40.99,
                                    "note": "支付宝-上海拉扎斯信息科技..."
                                },
                                {
                                    "type": "退款",
                                    "date": "2025-03-19",
                                    "time": "10:37:54",
                                    "money": 0.48,
                                    "note": "支付宝-上海壹佰米网络科技..."
                                },
                                {
                                    "type": "消费",
                                    "date": "2025-03-19",
                                    "time": "10:00:34",
                                    "money": -54.49,
                                    "note": "支付宝-上海壹佰米网络科技..."
                                }
                            ],
                            "ctrlLabel": "请选择要导入的记录",
                            "buttons": [
                                {
                                    "label": "确定",
                                    "name": "submit"
                                },
                                {
                                    "label": "返回",
                                    "name": "back"
                                }
                            ]
                        }
                    }
                };
                return r.data;
            }
        }
    };
})