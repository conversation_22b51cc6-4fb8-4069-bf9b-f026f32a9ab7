(function () {
    // 记账助手
    const repairDeal = function(inputDeal, form) {
        // console.log(inputDeal);

        let timeArr = inputDeal.time.split(':');
        let dealTime_TimePart = timeArr[0] + ':' + timeArr[1];
        form.DealTime = inputDeal.date.concat(' ').concat(dealTime_TimePart);
        form.DealTime_TimePart = dealTime_TimePart;
        form.curUid = '7ca27f12-b9df-46fd-9945-f3a52f40895a';
        form.walletId = 'd9368dd6-26ec-4cf9-b953-a2ad16708ce0';
        form.IsFinish = 1;
        if (typeof(inputDeal.money) == 'string') {
            form.Money = parseFloat(inputDeal.money.replace(',', ''));
        }
        else {
            form.Money = parseFloat(inputDeal.money);
        }

        // 叮咚买菜退款
        if (inputDeal.type == '退款' && inputDeal.note.indexOf('佰米网络科技') != -1) {
            form.XmId = '76fc4aee-7b01-45dc-863c-e475356fe9f6';
            form.Note = '[退款]';
            form.Book = '[jhn-home]';
        }
        // 叮咚买菜
        else if (inputDeal.type == '消费' && inputDeal.note.indexOf('佰米网络科技') != -1) {
            form.XmId = '76fc4aee-7b01-45dc-863c-e475356fe9f6';
            form.Note = '[超市]';
            form.Book = '[jhn-home]';
        }
        // 山姆大叔
        else if (inputDeal.type == '消费' && (inputDeal.note.indexOf('山姆会员') != -1 || inputDeal.note.indexOf('支付宝-沃尔玛') != -1)) {
            form.XmId = '76fc4aee-7b01-45dc-863c-e475356fe9f6';
            form.Note = '[超市][山姆大叔]';
            form.Book = '[jhn-home]';
        }
        // 饿了么外卖
        else if (inputDeal.type == '消费' && inputDeal.note.indexOf('拉扎斯信息') != -1) {
            form.XmId = '585fa4a1-aa2a-40d7-9c21-691c1f7b7332';
            form.Note = '[饿了么外卖]';
            form.Book = '[for-cs]';
        }
        // 美团外卖
        else if (inputDeal.type == '消费' && inputDeal.note.indexOf('北京三快在线科技') != -1) {
            form.XmId = '585fa4a1-aa2a-40d7-9c21-691c1f7b7332';
            form.Note = '[美团外卖]';
            form.Book = '[for-cs]';
        }
        // 停车费
        else if (inputDeal.type == '消费' && inputDeal.note.indexOf('停车') != -1) {
            form.XmId = 'ebf2fe06-dd3a-4c45-9b7e-f23d3052dbe3';
            form.Note = '[停车费]';
            form.Book = '[jhn-home]';
        }
        // 停车费
        else if (inputDeal.type == '消费' && inputDeal.note.indexOf('上海中冶祥腾城市广场') != -1) {
            form.XmId = 'ebf2fe06-dd3a-4c45-9b7e-f23d3052dbe3';
            form.Note = '[停车费]';
            form.Book = '[jhn-home]';
        }
        // 停车费
        else if (inputDeal.type == '消费' && inputDeal.note.indexOf('支付宝-上海汇锦公寓管理') != -1) {
            form.XmId = 'ebf2fe06-dd3a-4c45-9b7e-f23d3052dbe3';
            form.Note = '[停车费]';
            form.Book = '[jhn-home]';
        }
        // 共享单车
        else if (inputDeal.type == '消费' && inputDeal.note.indexOf('支付宝-北京三快在线科技') != -1) {
            form.XmId = 'ebf2fe06-dd3a-4c45-9b7e-f23d3052dbe3';
            form.Note = '[共享单车]';
            form.Book = '[jhn-home]';
        }
        // 微医咨询
        else if (inputDeal.type == '消费' && inputDeal.note.indexOf('支付宝-挂号网') != -1) {
            form.XmId = '464ff002-b782-4a1d-bc9e-f164cfbb4a92';
            form.Note = '[微医咨询]';
            form.Book = '[for-cs]';
        }
        // steam游戏
        else if (inputDeal.type == '消费' && inputDeal.note.indexOf('支付宝-Smart2Pay B.V.') != -1) {
            form.XmId = '1f0653b1-83c1-4105-8664-0290115da7f3';
            form.Note = '[微医咨询]';
            form.Book = '[for-cs]';
        }
        // 其它
        else {
            form.XmId = 'c8441cf5-f42a-4f9f-953d-cf76bb363661';
            form.Note = `[${inputDeal.type}]`;
            form.Book = '[默认]';
        }
    };

    return {
        // 交互面板
        interactionPanelSettings: {
            enabled: true
        },
        // 输入框内部工具
        inputInnerTools: [
            // 模型选择器
            {
                name: 'model',
                type: 'select',
                placeholder: '请选择模型',
                value: 'albl_qwen-vl-max',
                options: [
                    /*** ALiBL ***/
                    // 生成一次约 1852Tokens（1453 + 399） 0.0079元（0.0043元 + 0.0036元）
                    {
                        label: 'Qwen-vl-max（ALiBL）',
                        value: 'albl_qwen-vl-max',
                        title: '上下文32k，输出8k',
                    },
                    // 识别差
                    // {
                    //     label: 'Qwen-vl-plus（ALiBL）',
                    //     value: 'albl_qwen-vl-plus',
                    //     title: '上下文8k，输出2k',
                    // },
                    // 识别差
                    // {
                    //     label: 'Qwen-vl-ocr（ALiBL）',
                    //     value: 'albl_qwen-vl-ocr',
                    //     title: '上下文34k，输出4k',
                    // },
                    /*** 智谱AI（官网） ***/
                    // 生成一次约 1887Tokens（1542 + 345） 0.0075元
                    {
                        label: 'GLM-4v-plus-0111（ZhiPu）',
                        value: 'zhipu_glm-4v-plus-0111',
                        title: '上下文16k',
                    },
                    {
                        label: 'GLM-4v-plus（ZhiPu）',
                        value: 'zhipu_glm-4v-plus',
                        title: '上下文16k',
                    },
                    {
                        label: 'GLM-4v',
                        value: 'zhipu_glm-4v',
                        title: '上下文4k',
                    },
                    /*** MiniMax（官网） ***/
                    // 生成一次约 6956Tokens（6616 + 340） 0.0094元（0.0067元 + 0.0027元）
                    {
                        label: 'MiniMax-Text-01（MiniMax）',
                        value: 'minimax_MiniMax-Text-01',
                        title: '上下文1000k',
                    },
                    {
                        label: 'abab6.5s-chat（MiniMax）',
                        value: 'minimax_abab6.5s-chat',
                        title: '上下文245k',
                    },
                    /*** Moonshot ***/
                    // 生成一次约 1626Tokens（1240 + 386） 0.0195元
                    {
                        label: 'Moonshot-v1-8k',
                        value: 'msa_moonshot-v1-8k-vision-preview',
                        title: '上下文8k',
                    },
                    {
                        label: 'Moonshot-v1-32k',
                        value: 'msa_moonshot-v1-32k-vision-preview',
                        title: '上下文32k',
                    },
                    {
                        label: 'Moonshot-v1-128k',
                        value: 'msa_moonshot-v1-128k-vision-preview',
                        title: '上下文128k',
                    },
                    /*** OpenAI（Brain） ***/
                    {
                        label: 'GPT-4.1（Brain）',
                        value: 'br_gpt-4.1',
                        title: '上下文1047k，输出32k',
                        canVision: true,
                        canToolCall: true,
                    },
                    {
                        label: 'GPT-4.1-mini（Brain）',
                        value: 'br_gpt-4.1-mini',
                        title: '上下文1047k，输出32k',
                        canVision: true,
                        canToolCall: true,
                    },
                    {
                        label: 'GPT-4.1-nano（Brain）',
                        value: 'br_gpt-4.1-nano',
                        title: '上下文1047k，输出32k',
                        canVision: true,
                        canToolCall: true,
                    },
                    // 生成一次约 1661Tokens（1309 + 352） 0.0119$（0.0065$ + 0.0052$）
                    {
                        label: 'GPT-4o（Brain）',
                        value: 'br_gpt-4o',
                        title: '上下文128k，输出16k',
                    },
                    {
                        label: 'o1（Brain）',
                        value: 'br_o1',
                        title: '上下文200k，输出100k',
                        canVision: true,
                        canToolCall: true,
                    },
                    /*** OpenAI（Brain-Wild） ***/
                    {
                        label: 'GPT-4o（Brain-Wild）',
                        value: 'brwild_gpt-4o',
                        title: '上下文128k，输出16k',
                    },
                    /*** Ollama ***/
                    {
                        label: 'Gemma3-27b（OLL）',
                        value: 'ollama_gemma3:27b',
                    },
                    {
                        label: 'Gemma3-12b（OLL）',
                        value: 'ollama_gemma3:12b',
                    },
                    // 识别差
                    // {
                    //     label: 'Llama3.2-Vision-11B（OLL）',
                    //     value: 'ollama_llama3.2-vision:11b',
                    //     // title: '上下文13k，输入13k，输出32k',
                    // },
                ]
            }
        ],
        // 获取到消息后
        async onGetReceiveMessage(receiveMessage) {
            return receiveMessage;
        },
        // 交互面板提交
        async onInteractionPanelSubmit(e) {
            if (e.multipleSelection && e.multipleSelection.length > 0) {
                return await new Promise(function (resolve, reject) {

                    e.invokeComp.$confirm('确定要导入吗？', '确认提示', { type: 'warning' }).then(async () => {
                        // 点击确认
                        const loading = e.invokeComp.$loading({ lock: true, text: '加载中' });
                        try {
                            for (const item of e.multipleSelection) {
                                const form = {
                                    UpdType: 'new',
                                };

                                try {
                                    repairDeal(item, form);
                                } catch(exc) {
                                    e.invokeComp.$alert(`修复交易异常 -> ${exc.message}`, '错误提示', { type: 'error' });
                                    loading.close();
                                    resolve(false);
                                    return;
                                }

                                const r = await e.execAgentPlugin('updateDeal', form);
                                if (r && r.success === false) {
                                    e.invokeComp.$alert(r.msg, '错误提示', { type: 'error' });
                                    loading.close();
                                    resolve(false);
                                    return;
                                }
                            }
                            e.invokeComp.$message({ message: '导入完毕', type: 'success' });
                            loading.close();
                            resolve(true);
                        } catch (exc) {
                            console.error(exc)
                            e.invokeComp.$alert(`异常 -> ${exc.message}`, '错误提示', { type: 'error' });
                            loading.close();
                            resolve(false);
                        }
                    }).catch(() => {
                        // 点击取消
                        resolve(false);
                    });

                });
            }
            else {
                e.invokeComp.$alert('请选择要导入的记录', { type: 'error' });
                return false;
            }
        },
    };
})