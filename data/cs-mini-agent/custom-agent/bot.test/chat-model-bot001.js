(function(ctx) {
    return {
        parentId: 'chat-model',
        name: '智能体001',
        summary: null,
        settings: {
            chatOpts: {
                needConfirmTaskIfIsDone: true,
            },
            getSystemMessage(pars) {
                let str = ``;

                str += `
                ### 上下文
                #### 当前日期
                ${ctx.timeUtil.toDateStr(new Date())}
                #### 当前时间
                ${ctx.timeUtil.toTimeStr(new Date())}
                `;

                str += `
                `;

                return str;
            },
        },
    };
})