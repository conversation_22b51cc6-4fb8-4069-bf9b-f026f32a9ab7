(function (ctx) {
    return {
        parentId: 'chat-model',
        name: '任务助手',
        summary: '查询任务、创建任务',
        settings: {
            maxChatHistoryLength: 0, // 对话时塞入的历史对话条数限制
            chatHistoryBotReplyMaxSize: 50, // 对话时塞入的历史AI回复内容大小限制
            config: {
                // *** 2016 ***
                cz_url: 'http://erp.ovinfo.com:8800/apps/RobotService/Handler.aspx',
                // cz_url: 'http://localhost:27419/apps/RobotService/Handler.aspx',
                // cz_url: 'http://*************:27419/apps/RobotService/Handler.aspx',
                cz_appid: 'robot0',
                cz_skey: 'hrt35kz',
                // *** 2021 ***
                cz2021_url: 'http://erp.ovinfo.com:1107',
            },
            chatOpts: {
                // *** 聊天模型 ***
                // toolModel: 'msa_kimi-k2-0711-preview',
                // toolModel: 'br_gpt-4.1',
                // chatModel: 'albl_qwen3-235b-a22b|no_think|',
                chatModel: 'ollama_qwen3:14b|no_think|',
                // chatModel: 'albl_qwen-plus',
                // chatModel: 'albl_qwen-turbo',
                // chatModel: 'ollama_qwq:32b',
                // *** 工具模型 ***
                // toolModel: 'msa_kimi-k2-0711-preview',
                // toolModel: 'br_gpt-4.1',
                // toolModel: 'albl_qwen3-235b-a22b|no_think|',
                // toolModel: 'ollama_qwen3:14b|no_think|',
                // toolModel: 'ollama_qwen3:8b|no_think|',
                // toolModel: 'minimax_MiniMax-Text-01',
                // toolModel: 'gjld_deepseek-ai/DeepSeek-V3',
                toolModel: 'ds_deepseek-chat',
                // toolModel: 'zhipu_glm-4-plus', // 查看我的任务队列在有2组对话历史就会有问题，就不调用工具了
                // toolModel: 'albl_qwen-plus',
                // toolModel: 'albl_qwen-max',
                // toolModel: 'br_gpt-4o-mini',
            },
            getSystemMessage(pars, opts) {
                let str = ``;

                str += `
                ### 上下文
                #### 当前日期
                ${ctx.timeUtil.toDateStr(new Date())}
                #### 当前时间
                ${ctx.timeUtil.toTimeStr(new Date())}

                #### 用户信息
                - ID：${opts.loginUser.id}
                - 账号/用户名：${opts.loginUser.username}
                - 姓名：${opts.loginUser.realname}
                `;

                str += `
                ### 任务说明
                #### 角色要求
                - 你是任务助手，可以通过调用工具来完成任务
                `;

                return str;
            },
            // getChatHistory(pars) {
            //     const list = [];
            //     log('返回历史消息')
            //     // for (const i in pars.chatHistory) {
            //     //     const item = pars.chatHistory[i];
            //     //     if (item.content.indexOf('（下面内容不在对话中显示）' !== -1)) {
            //     //         console.log('过滤：', JSON.stringify(item))
            //     //     }
            //     //     else {
            //     //         list.push(item);
            //     //     }
            //     // }
            //     return list;
            // },
            // // 获取到消息后
            // async onGetReceiveMessage(receiveMessage) {
            //     return receiveMessage;
            // },
        },
    };
})