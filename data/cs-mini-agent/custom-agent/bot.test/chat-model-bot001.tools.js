(function (ctx, opts) {
    return [
        // 打开网页给用户看
        {
            name: 'openUrlForUser',
            descr: '打开网页给用户看',
            params: {
                required: [],
                props: {
                    url: {
                        type: 'string',
                        descr: '打开的网页地址',
                    },
                }
            },
            call: async function(params) {
                const url = params.url;
                return {
                    reply: `<cs-ai-reply-cmd style="display: none;">${JSON.stringify({ type: 'open-url', url })}</cs-ai-reply-cmd>`,
                    replyToAgent: '已成功打开网址',
                    // 继续对话
                    continue: true,
                };
            }
        }
    ]
})