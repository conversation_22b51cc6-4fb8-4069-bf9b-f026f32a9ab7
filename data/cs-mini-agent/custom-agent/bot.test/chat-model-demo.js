(function (ctx) {
    return {
        parentId: 'chat-model',
        name: '智能体样例',
        summary: '',
        settings: {
            // 限制对话历史长度，一般是偶数，表示一轮对话（不含系统消息和本次发送消息）
            maxChatHistoryLength: 6,
            // 自定义配置参数
            config: {
                // cz_url: 'http://erp.ovinfo.com/apps/RobotService/Handler.aspx',
                cz_url: 'http://localhost:27419/apps/RobotService/Handler.aspx',
                cz_appid: 'robot0',
                cz_skey: 'hrt35kz',
            },
            // 自定义聊天可选项参数
            chatOpts: {
                toolModel: 'ollama_qwen2.5:14b',
            },
            // 自定义系统消息
            getSystemMessage(pars, opts) {
                let str = ``;

                str += `
                ### 上下文
                #### 当前日期
                ${ctx.timeUtil.toDateStr(new Date())}
                #### 当前时间
                ${ctx.timeUtil.toTimeStr(new Date())}

                #### 用户信息
                - ID：${opts.loginUser.id}
                - 账号/用户名：${opts.loginUser.username}
                - 姓名：${opts.loginUser.realname}
                `;

                // str += `
                // ### 任务说明
                // #### 角色要求
                // - 你是公司系统助手，你有权限调用部分接口来完成任务
                // `;

                return str;
            },
            // // 自定义返回的历史消息
            // getChatHistory(pars) {
            //     const list = [];
            //     log('返回历史消息')
            //     // for (const i in pars.chatHistory) {
            //     //     const item = pars.chatHistory[i];
            //     //     if (item.content.indexOf('（下面内容不在对话中显示）' !== -1)) {
            //     //         console.log('过滤：', JSON.stringify(item))
            //     //     }
            //     //     else {
            //     //         list.push(item);
            //     //     }
            //     // }
            //     return list;
            // },
            // // 获取到消息后
            // async onGetReceiveMessage(receiveMessage) {
            //     return receiveMessage;
            // },
        },
    };
})