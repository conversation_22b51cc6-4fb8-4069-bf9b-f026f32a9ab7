(function () {
    return {
        // 发送消息类型
        sendMessageType: 'stream',
        // 获取初始提示词（欢迎词）
        async getInitialPrompt() {
            return {
                content: `你好，很高兴与你交流~`
            };
        },
        // 获取到消息后
        async onGetReceiveMessage(receiveMessage) {
            return receiveMessage;
        },
        // 交互面板提交
        async onInteractionPanelSubmit(e) {
        },
    };
})