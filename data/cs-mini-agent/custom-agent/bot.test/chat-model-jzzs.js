(function (ctx) {
    return {
        parentId: 'chat-model',
        name: '快速记账',
        summary: '上传银行收支截图直接导入',
        settings: {
            // mockReceiveMessage: true,
            // 限制对话历史长度，一般是偶数，表示一轮对话（不含系统消息和本次发送消息）
            maxChatHistoryLength: 0,
            setSystemMessageToChatStart: true,
            getSystemMessage(pars) {
                let str = ``;

                str += `
                ### 任务说明
                #### 角色要求
                - 你是手机银行收支列表截图分析专家，请识别截图并转为表格，需要的字段：收支类型、日期、时间、金额、备注

                #### 回答要求
                - 不要有任何解释，返回一个数组给我
                - 数组中的每个元素是一个对象，对象的属性如下：
                    - type: 收支类型，例：消费、退款、转账等
                    - date: 日期，例：2024-01-01
                    - time: 时间，例：12:00:00
                    - money: 金额
                    - note: 备注
                - 用json格式返回，例：[{"type":"消费","date":"2019-09-01","time":"12:00:12","money":30,"note":"..."}]
                `;

                // str += `把图中内容用json格式返回给我，需要字段：日期，类型，金额，备注`;

                // str += `请识把我传的手机银行收支列表截图转为表格，需要的字段：收支类型、日期、时间、金额、备注\n`
                //     + `其中收支类型是指：消费、退款、...等\n\n`
                //     + `把表格转成json格式，一个数组，返回给我，不需要有任何解释\n\n\n`
                //     + `收支类型 -> type\n日期 -> date\n时间 -> time\n金额 -> money\n备注 -> note\n\n\n`
                //     + "返回形式样例：```json\n[{type:'消费',date:'2019-09-01',time:'12:00:12,money:30,note:'xxxxxxx'}]\n```";
                    
                return str;
            },
            // 获取到消息后
            async onGetReceiveMessage(receiveMessage) {
                // // 用mock数据测试
                // const res = await ctx.execPlugin({
                //     name: '_getTestData0',
                //     params: {
                        
                //     }
                // });
                // return res;

                // ...
                let str = receiveMessage.reply;
                let find = false;

                // 特征1
                if (str.startsWith('```json\n')) {
                    str = str.substring(8);
                    find = true;
                }
                else if (str.startsWith('```json')) {
                    str = str.substring(7);
                    find = true;
                }
                if (str.endsWith('\n```')) {
                    str = str.substring(0, str.length - 4);
                }
                else if (str.endsWith('```')) {
                    str = str.substring(0, str.length - 3);
                }

                // 特征2
                if (!find) {
                    if (str.match(/^\[\n*\s*{\n*/) != null) {
                        find = true;
                    }
                }

                // 特征3
                if (!find) {
                    const tpl = 'Sure, here is the data in the required format:';
                    if (str.startsWith(`${tpl}\n\n`)) {
                        str = str.substring(`${tpl}\n\n`.length);
                        find = true;
                    }
                    else if (str.startsWith(`${tpl}\n`)) {
                        str = str.substring(`${tpl}\n`.length);
                        find = true;
                    }
                    else if (str.startsWith(tpl)) {
                        str = str.substring(tpl.length);
                        find = true;
                    }
                }

                if (find) {
                    // log(str);
                    let list;
                    try {
                        // list = JSON.parse(str);
                        list = eval(str);
                    }
                    catch (exc) {
                        log(`解析json失败：${str}`);
                        receiveMessage.reply = `解析数据失败`;
                        return receiveMessage;
                    }
                    receiveMessage.reply = `共${list.length}条记录，请在交互面板继续操作`;
                    // receiveMessage.data = list;
                    // 配置交互面板
                    receiveMessage.interactionPanelSettings = {
                        type: 'table',
                        canMultiSelect: true,
                        columns: [
                            {
                                label: '收支类型',
                                name: 'type',
                                width: 90,
                            },
                            {
                                label: '日期',
                                name: 'date',
                                width: 100,
                            },
                            {
                                label: '时间',
                                name: 'time',
                                width: 90,
                            },
                            {
                                label: '金额',
                                name: 'money',
                            },
                            {
                                label: '备注',
                                name: 'note',
                            },
                        ],
                        rows: list,
                        ctrlLabel: '请选择要导入的记录',
                        buttons: [
                            {
                                label: '确定',
                                name: 'submit',
                            },
                            {
                                label: '返回',
                                name: 'back',
                            },
                        ],
                    };
                }
                return receiveMessage;
            },
        },
    };
})