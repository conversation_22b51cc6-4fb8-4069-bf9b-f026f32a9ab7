(function (ctx) {
    // 插件
    return {
        /*** 系统功能 ***/
        reqCZ2016ByCmd: {
            exec: async function(params) {
                const cmd = params.cmd;
                const form = params.pars;

                const { httpUtil } = ctx;
                let cfg = ctx.agent.getSettings().config;
                let reqUrl = `${cfg.cz_url}?cmd=${cmd}&appid=${cfg.cz_appid}&skey=${cfg.cz_skey}`;
                const r = await httpUtil.reqPostFormSync(reqUrl, form);
                if (r.success !== true && typeof(r) === 'string') {
                    log(`【执行插件】请求异常 -> ${r}`);
                    return {
                        success: false,
                        msg: `返回内容无法解析${(r != null ? `（${r.substring(0, 30)}...）` : '')}`
                    }
                }
                return r;
            }
        },
        reqCZ2021TaskByCmd: {
            exec: async function(params) {
                const cmd = params.cmd;
                const form = params.pars;

                const { httpUtil } = ctx;
                let cfg = ctx.agent.getSettings().config;
                let reqUrl = `${cfg.cz2021_url}/api/task${cmd}`;
                const r = await httpUtil
                .reqPostJsonSync(reqUrl, form);
                return r;
            }
        },
        /*** 业务辅助功能 ***/
        _findEmpByRn: {
            exec: async function(params) {
                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'getEmpByUn',
                        pars: {
                            ...params
                        },
                    }
                });

                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                    return r;
                }

                return r.data;
            }
        },
        // 根据名称关键词模糊查找任务
        _findTaskByNameKw: {
            exec: async function(params) {

                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'findTaskByNameKw',
                        pars: {
                            ...params
                        },
                    }
                });

                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                    return r;
                }

                return r.data;
            }
        },
        // 根据名称关键词模糊查找事项
        _findPTaskByNameKw: {
            exec: async function(params) {

                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'findPTaskByNameKw',
                        pars: {
                            ...params
                        },
                    }
                });

                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                    return r;
                }

                return r.data;
            }
        },
        // 根据名称精确查找任务
        _findTaskByName: {
            exec: async function(params) {

                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'findTaskByName',
                        pars: {
                            ...params
                        },
                    }
                });

                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                    return r;
                }

                return r.data;
            }
        },
        // 根据名称精确查找事项
        _findPTaskByName: {
            exec: async function(params) {

                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'findPTaskByName',
                        pars: {
                            ...params
                        },
                    }
                });

                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                    return r;
                }

                return r.data;
            }
        },
        // 检查用户姓名关键词唯一性
        // @returns {string|{success,data}}
        _checkUserRealNameKwUnique: {
            exec: async function(params) {
                const kw = params.kw;
                const label = params.label;

                let list = [];
                let str = '';

                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'searchUserEmp',
                        pars: {
                            kw: kw
                        }
                    }
                });
                // log(`返回结果：${JSON.stringify(r)}`);
                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                    return r;
                }

                list = r.data;

                // 找到多个用户
                if (list.length > 1) {
                    if (label) {
                        str = `查询发现多个用户，${label}：\n`;
                    }
                    else {
                        str = '查询发现多个用户：\n';
                    }
                    let index = 1;
                    for (const item of list) {
                        str += `${index++}. ${item.RealName}\n`;
                    }
                    return {
                        success: true,
                        reply: str,
                        list: list
                    };
                }
                // 没有找到用户
                else if (list.length === 0) {
                    str = `没能根据关键词“${params.kw}”找到用户`;
                    return {
                        success: false,
                        msg: str
                    };
                }
                // 找到一个用户
                else {
                    return {
                        success: true,
                        data: list[0].RealName
                    };
                }
            }
        },
        /*** 业务主要功能 ***/
        // 批准子任务工时
        approveChildTaskWorkHour: {
            exec: async function(params) {
                const id = params.id;

                const r = await ctx.execPlugin({
                    name: 'reqCZ2021TaskByCmd',
                    params: {
                        cmd: '/workhours/audit',
                        pars: {
                            loginInfo: {
                                uid: ctx.loginUser.id,
                                un: ctx.loginUser.username,
                                rn: ctx.loginUser.realname,
                                sac: '',
                            },
                            form: {
                                id: id,
                                flow: 1,
                            }
                        },
                    }
                });
                return r;
            }
        },
        // 加载超时任务列表
        loadTimeoutTaskList: {
            exec: async function(params) {
                const list = [];

                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'listTimeoutTask',
                        pars: {},
                    }
                });

                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                    return r;
                }

                for (const i in r.data) {
                    const item = r.data[i];
                    list.push({
                        name: item.Name,
                        optUser: item.TskMgrRN,
                    });
                }

                return list;
            }
        },
        // 获取任务成本报告
        getTaskCostReport: {
            exec: async function(params) {
                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'getTaskCostReport',
                        pars: {
                            id: params.id
                        },
                    }
                });

                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                    return r;
                }

                return r.data;
            }
        },
        // 获取事项成本报告
        getPTaskCostReport: {
            exec: async function(params) {
                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'getPTaskCostReport',
                        pars: {
                            id: params.id
                        },
                    }
                });

                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                    return r;
                }

                return r.data;
            }
        },
        // 列出我的未读任务更新历史
        listMyNotReadTaskUpdateHistory: {
            exec: async function(params) {
                const list = [];

                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'listMyNotReadTaskUpdHistory',
                        pars: {
                            ...params
                        },
                    }
                });

                // log(r);
                // log(JSON.stringify(r));

                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                    return r;
                }

                for (const i in r.data) {
                    const item = r.data[i];
                    list.push(item);
                }

                return list;
            }
        },
        // 查询某用户或某机构的手机号和电话号码
        searchUserOrOrgPhone: {
            exec: async function(params) {
                const list = [];

                const r = await ctx.execPlugin({
                    name: 'reqCZ2016ByCmd',
                    params: {
                        cmd: 'searchUserOrOrgPhone',
                        pars: {
                            kw: params.kw
                        },
                    }
                });

                // log('【插件调试】', '请求返回结果：', JSON.stringify(r));
                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                    return r;
                }

                for (const i in r.data.users) {
                    const item = r.data.users[i];
                    const newItem = {};
                    // 识别是用户
                    if (item.RealName && item.RealName.length > 0) {
                        newItem.type = 'user';
                        newItem.name = item.RealName;
                        newItem.phoneInfo = '';
                        if (item.MobilePhone && item.MobilePhone.length > 0) {
                            newItem.phoneInfo += `手机号码1:${item.MobilePhone}; `;
                        }
                        if (item.MobilePhone1 && item.MobilePhone1.length > 0) {
                            newItem.phoneInfo += `手机号码2:${item.MobilePhone1}; `;
                        }
                    }

                    if (newItem.phoneInfo && newItem.phoneInfo.length > 0) {
                        list.push(newItem);
                    }
                }
                
                for (const i in r.data.orgs) {
                    const item = r.data.orgs[i];
                    const newItem = {};
                    // 识别是机构
                    if (item.法定名称 && item.法定名称.length > 0) {
                        newItem.type = 'org';
                        newItem.name = item.法定名称;
                        newItem.phoneInfo = '';
                        if (item.电话 && item.电话.length > 0) {
                            newItem.phoneInfo += `电话:${item.电话}; `;
                        }
                        if (item.电话1 && item.电话1.length > 0) {
                            newItem.phoneInfo += `电话1:${item.电话1}; `;
                        }
                        if (item.电话2 && item.电话2.length > 0) {
                            newItem.phoneInfo += `电话2:${item.电话2}; `;
                        }
                    }

                    if (newItem.phoneInfo && newItem.phoneInfo.length > 0) {
                        list.push(newItem);
                    }
                }

                return list;
            }
        },
        // 获取某人任务队列列表
        getUserCtskQueueListByCtskRn: {
            exec: async function(params) {
                const list = [];

                const r = await ctx.execPlugin({
                    name: 'reqCZ2021TaskByCmd',
                    params: {
                        cmd: '/childtaskqueue/getUserAllListByCtskRn',
                        pars: {
                            ctskRn: params.ctskRn
                        },
                    }
                });
                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                    return r;
                }

                for (const i in r.data) {
                    const loadItem = r.data[i];
                    // log(JSON.stringify(loadItem))
                    const newItem = {};
                    // 序号
                    newItem.index = loadItem.index;
                    // 任务名
                    newItem.ctsk_name = loadItem.ctsk_name;
                    newItem.obj_name = loadItem.obj_name;

                    list.push(newItem);
                }

                return list;
            }
        },
        // 移动队列中某任务到最前位置
        moveCtskQueueToStartByCtskRn: {
            exec: async function(params) {
                const r = await ctx.execPlugin({
                    name: 'reqCZ2021TaskByCmd',
                    params: {
                        cmd: '/childtaskqueue/moveToStartByCtskRn',
                        pars: {
                            ctskRn: params.ctskRn,
                            fromIndex: params.fromIndex
                        },
                    }
                });
                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                }
                return r;
            }
        },
        // 移动队列中某任务到最后位置
        moveCtskQueueToEndByCtskRn: {
            exec: async function(params) {
                const r = await ctx.execPlugin({
                    name: 'reqCZ2021TaskByCmd',
                    params: {
                        cmd: '/childtaskqueue/moveToEndByCtskRn',
                        pars: {
                            ctskRn: params.ctskRn,
                            fromIndex: params.fromIndex
                        },
                    }
                });
                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                }
                return r;
            }
        },
        // 移动队列中某任务到指定位置
        moveCtskQueueToIndexByCtskRn: {
            exec: async function(params) {
                const r = await ctx.execPlugin({
                    name: 'reqCZ2021TaskByCmd',
                    params: {
                        cmd: '/childtaskqueue/moveToIndexByCtskRn',
                        pars: {
                            ctskRn: params.ctskRn,
                            fromIndex: params.fromIndex,
                            toIndex: params.toIndex
                        },
                    }
                });
                if (r.success === false) {
                    log(`【执行插件】请求失败 -> ${r.msg}`);
                }
                return r;
            }
        },
    };
})