(function () {
    return {
        sendMessageType: 'stream',
        // 输入框内部工具
        inputInnerTools: [
            // 模型选择器
            {
                name: 'model',
                type: 'select',
                placeholder: '请选择模型',
                value: 'gpt-4o-mini',
                options: [
                    /*** DeepSeek（官网） ***/
                    {
                        label: 'DeepSeek-V3-满血（官网）',
                        value: 'ds_deepseek-chat',
                        title: '上下文64k，输出8k',
                    },
                    {
                        label: 'DeepSeek-R1-满血（官网）',
                        value: 'ds_deepseek-reasoner',
                        title: '上下文64k，输出8k',
                    },
                    /*** 智谱AI（官网） ***/
                    {
                        label: 'GLM-4-plus（ZhiPu）',
                        value: 'zhipu_glm-4-plus',
                        title: '上下文128k',
                        canVision: true,
                        canToolCall: true,
                    },
                    {
                        label: 'GLM-4v-plus-0111（ZhiPu）',
                        value: 'zhipu_glm-4v-plus-0111',
                        title: '上下文16k',
                        canVision: true,
                    },
                    {
                        label: 'GLM-4v-plus（ZhiPu）',
                        value: 'zhipu_glm-4v-plus',
                        title: '上下文16k',
                        canVision: true,
                    },
                    {
                        label: 'GLM-4v',
                        value: 'zhipu_glm-4v',
                        title: '上下文4k',
                        canVision: true,
                    },
                    /*** MiniMax（官网） ***/
                    {
                        label: 'MiniMax-Text-01（MiniMax）',
                        value: 'minimax_MiniMax-Text-01',
                        title: '上下文1000k',
                    },
                    {
                        label: 'abab6.5s-chat（MiniMax）',
                        value: 'minimax_abab6.5s-chat',
                        title: '上下文245k',
                    },
                    {
                        label: 'DeepSeek-R1（MiniMax）',
                        value: 'minimax_DeepSeek-R1',
                        title: '上下文245k',
                    },
                    /*** ALiBL ***/
                    {
                        label: 'DeepSeek-R1-满血（ALiBL）',
                        value: 'albl_deepseek-r1',
                    },
                    {
                        label: 'DeepSeek-V3-满血（ALiBL）',
                        value: 'albl_deepseek-v3',
                    },
                    {
                        label: 'Qwen-max（ALiBL）',
                        value: 'albl_qwen-max',
                        title: '上下文32k，输出8k',
                    },
                    {
                        label: 'Qwen-plus（ALiBL）',
                        value: 'albl_qwen-plus',
                        title: '上下文13k，输出8k',
                    },
                    {
                        label: 'Qwen2.5-14b-instruct-1m（ALiBL）',
                        value: 'albl_qwen2.5-14b-instruct-1m',
                        title: '上下文100k，输出8k',
                    },
                    /*** Ollama ***/
                    {
                        label: 'QwQ-32b（OLL）',
                        value: 'ollama_qwq:32b',
                    },
                    {
                        label: 'DeepSeek-R1-32b（OLL）',
                        value: 'ollama_deepseek-r1:32b',
                    },
                    {
                        label: 'DeepSeek-R1-14b（OLL）',
                        value: 'ollama_deepseek-r1:14b',
                    },
                    {
                        label: 'Qwen2.5-14b（OLL）',
                        value: 'ollama_qwen2.5:14b',
                    },
                    {
                        label: 'DeepSeek-V2-16b（OLL）',
                        value: 'ollama_deepseek-v2:16b',
                    },
                    {
                        label: 'GLM4-9b（OLL）',
                        value: 'ollama_glm4:9b',
                    },
                    {
                        label: 'Phi4-14b（OLL）',
                        value: 'ollama_phi4:14b',
                    },
                    {
                        label: 'Llama3.2-Vision-11B（OLL）',
                        value: 'ollama_llama3.2-vision:11b',
                    },
                    {
                        label: 'DeepSeek-coder-v2-16b（OLL）',
                        value: 'ollama_deepseek-coder-v2:16b',
                    },
                    /*** OpenAI（Brain） ***/
                    {
                        label: 'GPT-4o-mini（Brain）',
                        value: 'br_gpt-4o-mini',
                        title: '上下文128k，输出16k',
                    },
                    {
                        label: 'GPT-4o（Brain）',
                        value: 'br_gpt-4o',
                        title: '上下文128k，输出16k',
                    },
                    {
                        label: 'o3-mini（Brain）',
                        value: 'br_o3-mini',
                        title: '上下文200k，输出100k',
                    },
                    {
                        label: 'o1-mini（Brain）',
                        value: 'br_o1-mini',
                        title: '上下文128k，输出65k',
                    },
                    {
                        label: 'o1（Brain）',
                        value: 'br_o1',
                        title: '上下文200k，输出100k',
                    },
                    /*** DeepSeek（Brain-Wild） ***/
                    {
                        label: 'DeepSeek-R1（Brain-Wild）',
                        value: 'brwild_deepseek-r1',
                        title: '上下文64k，输出8k',
                    },
                    /*** Anthropic（Brain-Wild） ***/
                    {
                        label: 'Claude-3-opus（Brain-Wild）',
                        value: 'brwild_claude-3-opus-20240229',
                        title: '上下文200k，输出4k',
                    },
                    {
                        label: 'Claude-3.7-sonnet（Brain-Wild）',
                        value: 'brwild_claude-3-7-sonnet-20250219',
                        title: '上下文200k，输出8k',
                    },
                    {
                        label: 'Claude-3.5-sonnet（Brain-Wild）',
                        value: 'brwild_claude-3-5-sonnet-20241022',
                        title: '上下文200k，输出8k',
                    },
                    {
                        label: 'Claude-3-sonnet（Brain-Wild）',
                        value: 'brwild_claude-3-sonnet-20240229',
                        title: '上下文200k，输出4k',
                    },
                    {
                        label: 'Claude-3-haiku（Brain-Wild）',
                        value: 'brwild_claude-3-haiku-20240307',
                        title: '上下文200k，输出4k',
                    },
                    /*** OpenAI（Brain-Wild） ***/
                    {
                        label: 'o3-mini（Brain-Wild）',
                        value: 'brwild_o3-mini',
                        title: '上下文200k，输出100k',
                    },
                    // 官网不让用o1
                    {
                        label: 'o1（Brain-Wild）',
                        value: 'brwild_o1',
                        title: '上下文200k，输出100k',
                    },
                    {
                        label: 'GPT-4o（Brain-Wild）',
                        value: 'brwild_gpt-4o',
                        title: '上下文128k，输出16k',
                    },
                    {
                        label: 'GPT-4o-mini（Brain-Wild）',
                        value: 'brwild_gpt-4o-mini',
                        title: '上下文128k，输出16k',
                    },
                    // {
                    //     label: 'o1-mini-128k（Brain-Wild）',
                    //     value: 'brwild_o1-mini',
                    //     title: '上下文128k，输出65k',
                    // },
                    // {
                    //     label: 'o1-128k（Brain-Wild）',
                    //     value: 'brwild_o1',
                    //     title: '上下文128k，输出100k',
                    // },
                    /*** DeepSeek（GJLD） ***/
                    {
                        label: 'DeepSeek-V3（GJLD）',
                        value: 'gjld_deepseek-ai/DeepSeek-V3',
                        title: '上下文64k，输出8k',
                    },
                    {
                        label: 'DeepSeek-R1（GJLD）',
                        value: 'gjld_deepseek-ai/DeepSeek-R1',
                        title: '上下文64k，输出8k',
                    },
                    {
                        label: 'Moonshot-v1-8k',
                        value: 'moonshot-v1-8k',
                        title: '上下文8k',
                    },
                    {
                        label: 'Moonshot-v1-32k',
                        value: 'moonshot-v1-32k',
                        title: '上下文32k',
                    },
                    {
                        label: 'Moonshot-v1-128k',
                        value: 'moonshot-v1-128k',
                        title: '上下文128k',
                    },
                ]
            }
        ],
    };
})