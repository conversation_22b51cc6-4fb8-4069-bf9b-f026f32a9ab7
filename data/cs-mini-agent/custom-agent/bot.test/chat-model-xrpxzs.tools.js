(function (ctx, opts) {
    return [
        // 查询员工手册
        {
            name: 'searchYGSC',
            descr: '查询员工手册',
            params: {
                required: [],
                props: {
                    query: {
                        type: 'string',
                        descr: '查询向量的源文本，根据对话总结一个用于查询的源文本',
                    },
                    kwList: {
                        type: 'string',
                        descr: `根据对话内容和用户查询意图生成的关键词列表，例：{["关键词1", "关键词2"]}\n\n`
                            + `关键词要求：\n`
                            + `1. 使用json格式\n`
                            + `2. 根据输入内容生成关键词集合。\n`
                            + `3. 提炼关键词要注意多样化，例：大人票多少钱 -> 票，钱。\n`
                            + `4. 要有举一反三的意识，例如：大人票多少钱 -> 票，门票，票价，钱，价格，金子，价值。\n`
                            + `5. 尽量提炼出最短关键词，一个字也可以，最好不要超过两个字长度；6. 关键词尽可能提炼的多一些。\n`
                    }
                }
            },
            call: async function(params) {
                const query = params.query;
                const kwListStr = params.kwList;
                log(`查询源文本：${query}, 关键词：${kwListStr}`);

                let parseKwListSuccess = false;
                let kwList = [];
                try {
                    kwList = JSON.parse(kwListStr);
                    parseKwListSuccess = true;
                } catch (e) {
                    log('关键词解析失败', e.message);
                }

                const list = await ctx.getService('CSMiniKbService')
                    .searchSync1(ctx.getConfig().kbId, query, kwList, ctx.getConfig().kbListMaxSize);

                // log('请求结果共', list.length, '条');

                let str = '';
                for (const item of list) {
                    str += `${item.title}\n`;
                    str += `${item.content}\n`
                    str += `\n`;
                }
                return str;
            }
        }
    ]
})