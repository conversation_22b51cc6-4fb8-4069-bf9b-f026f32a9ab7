(function (ctx) {
    let debug = true;

    return [
        // 1、我的XXX项目现在进行到了哪一步，下一步需要做什么
        {
            name: 'getProjectStateAndNext',
            descr: '查询项目进行到哪一步了并且下一步需要做什么（需要项目编号或项目名称关键词）',
            params: {
                required: [],
                props: {
                    num: {
                        type: 'string',
                        descr: '项目编号，例：1301AM0063'
                    },
                    nameKw: {
                        type: 'string',
                        descr: '项目名称关键词'
                    }
                }
            },
            // 调用工具
            call: async function(params, opts) {
                let result = '';
                const num = params.num;
                const nameKw = params.nameKw;

                opts.outputState('查询项目信息中');
                const r = await ctx.execPlugin({
                    name: 'reqByCmd',
                    params: {
                        cmd: '/renren-admin/outapi/AIManage/getProjectStateAndNext',
                        pars: {
                            num, nameKw,
                        }
                    }
                });
                log(`请求结果：${JSON.stringify(r)}`);

                if (r.code === 401) {
                    return {
                        reply: `登录状态已过期，请重新打开或登录`
                    };
                }
                if (r.code === 1) {
                    return {
                        reply: `操作失败 -> ${r.msg}`
                    };
                }
                if (r.success === false || r.code !== 0) {
                    return {
                        reply: `请求失败 -> ${r.msg}`
                    };
                }

                result = `当前步骤：${r.data.curState}</br>下一步骤：${r.data.nextState}`;
                if (debug) {
                    result += `<div style="font-size:12px;color:silver;">（调试信息传入：${JSON.stringify({ num, nameKw, })}）</div>`;
                    result += `<div style="font-size:12px;color:silver;">（调试信息返回：${JSON.stringify(r)}）</div>`;
                }

                return {
                    reply: result
                };
            }
        },
        // 2、我的某某项目整改内容是什么？
        {
            name: 'getProjectZGNR',
            descr: '查询项目整改内容（需要项目编号或项目名称关键词）',
            params: {
                required: [],
                props: {
                    num: {
                        type: 'string',
                        descr: '项目编号，例：1301AM0063'
                    },
                    nameKw: {
                        type: 'string',
                        descr: '项目名称关键词'
                    }
                }
            },
            // 调用工具
            call: async function(params, opts) {
                let result = '';
                const num = params.num;
                const nameKw = params.nameKw;

                opts.outputState('查询项目信息中');
                const r = await ctx.execPlugin({
                    name: 'reqByCmd',
                    params: {
                        cmd: '/renren-admin/outapi/AIManage/getProjectZGNR',
                        pars: {
                            num, nameKw,
                        }
                    }
                });
                log(`请求结果：${JSON.stringify(r)}`);

                if (r.code === 401) {
                    return {
                        reply: `登录状态已过期，请重新打开或登录`
                    };
                }
                if (r.code === 1) {
                    return {
                        reply: `操作失败 -> ${r.msg}`
                    };
                }
                if (r.success === false || r.code !== 0) {
                    return {
                        reply: `请求失败 -> ${r.msg}`
                    };
                }
                if (r.data == null && r.msg != null && r.msg.length > 0) {
                    return {
                        reply: `${r.msg}`
                    };
                }

                result = `${r.data.content}`;
                if (debug) {
                    result += `<div style="font-size:12px;color:silver;">（调试信息传入：${JSON.stringify({ num, nameKw, })}）</div>`;
                    result += `<div style="font-size:12px;color:silver;">（调试信息返回：${JSON.stringify(r)}）</div>`;
                }

                return {
                    reply: result
                };
            }
        },
        // 3、某某项目是否中标？
        {
            name: 'isProjectAwarded',
            descr: '查询项目是否中标（需要项目编号或项目名称关键词）',
            params: {
                required: [],
                props: {
                    num: {
                        type: 'string',
                        descr: '项目编号，例：1301AM0063'
                    },
                    nameKw: {
                        type: 'string',
                        descr: '项目名称关键词'
                    }
                }
            },
            // 调用工具
            call: async function(params, opts) {
                let result = '';
                const num = params.num;
                const nameKw = params.nameKw;

                opts.outputState('查询项目信息中');
                const r = await ctx.execPlugin({
                    name: 'reqByCmd',
                    params: {
                        cmd: '/renren-admin/outapi/AIManage/isProjectHasBid',
                        pars: {
                            num, nameKw,
                        }
                    }
                });
                log(`请求结果：${JSON.stringify(r)}`);

                if (r.code === 401) {
                    return {
                        reply: `登录状态已过期，请重新打开或登录`
                    };
                }
                if (r.code === 1) {
                    return {
                        reply: `操作失败 -> ${r.msg}`
                    };
                }
                if (r.success === false || r.code !== 0) {
                    return {
                        reply: `请求失败 -> ${r.msg}`
                    };
                }
                if (r.data == null && r.msg != null && r.msg.length > 0) {
                    return {
                        reply: `${r.msg}`
                    };
                }

                if (r.data.result === true) {
                    result = '项目已中标';
                }
                else {
                    result = '项目未中标';
                }
                if (debug) {
                    result += `<div style="font-size:12px;color:silver;">（调试信息传入：${JSON.stringify({ num, nameKw, })}）</div>`;
                    result += `<div style="font-size:12px;color:silver;">（调试信息返回：${JSON.stringify(r)}）</div>`;
                }

                return {
                    reply: result
                };
            }
        },
        // 4、某某项目验收材料还差哪些？
        {
            name: 'getProjectMissingMaterials',
            descr: '查询项目验收材料还缺少哪些（需要项目编号或项目名称关键词）',
            params: {
                required: [],
                props: {
                    num: {
                        type: 'string',
                        descr: '项目编号，例：1301AM0063'
                    },
                    nameKw: {
                        type: 'string',
                        descr: '项目名称关键词'
                    }
                }
            },
            // 调用工具
            call: async function(params, opts) {
                let result = '';
                const num = params.num;
                const nameKw = params.nameKw;

                opts.outputState('查询项目信息中');
                const r = await ctx.execPlugin({
                    name: 'reqByCmd',
                    params: {
                        cmd: '/renren-admin/outapi/AIManage/getProjectMissingMaterials',
                        pars: {
                            num, nameKw,
                        }
                    }
                });
                log(`请求结果：${JSON.stringify(r)}`);

                if (r.code === 401) {
                    return {
                        reply: `登录状态已过期，请重新打开或登录`
                    };
                }
                if (r.code === 1) {
                    return {
                        reply: `操作失败 -> ${r.msg}`
                    };
                }
                if (r.success === false || r.code !== 0) {
                    return {
                        reply: `请求失败 -> ${r.msg}`
                    };
                }
                if (r.data == null && r.msg != null && r.msg.length > 0) {
                    return {
                        reply: `${r.msg}`
                    };
                }

                result = `${r.data.content}`;
                if (debug) {
                    result += `<div style="font-size:12px;color:silver;">（调试信息传入：${JSON.stringify({ num, nameKw, })}）</div>`;
                    result += `<div style="font-size:12px;color:silver;">（调试信息返回：${JSON.stringify(r)}）</div>`;
                }

                return {
                    reply: result
                };
            }
        },
        // 5、今年我们单位申报项目总额是多少？
        {
            name: 'getProjectTotalAmount',
            descr: '查询单位某年申报项目总额（需要年份，默认是当前）',
            params: {
                required: [],
                props: {
                    year: {
                        type: 'string',
                        descr: '年份，例：2013'
                    },
                }
            },
            // 调用工具
            call: async function(params, opts) {
                let result = '';
                const year = params.year;

                opts.outputState('查询项目信息中');
                const r = await ctx.execPlugin({
                    name: 'reqByCmd',
                    params: {
                        cmd: '/renren-admin/outapi/AIManage/getProjectTotalAmount',
                        pars: {
                            year,
                        }
                    }
                });
                log(`请求结果：${JSON.stringify(r)}`);

                if (r.code === 401) {
                    return {
                        reply: `登录状态已过期，请重新打开或登录`
                    };
                }
                if (r.code === 1) {
                    return {
                        reply: `操作失败 -> ${r.msg}`
                    };
                }
                if (r.success === false || r.code !== 0) {
                    return {
                        reply: `请求失败 -> ${r.msg}`
                    };
                }
                if (r.data == null && r.msg != null && r.msg.length > 0) {
                    return {
                        reply: `${r.msg}`
                    };
                }

                result = `${r.data.content}`;
                if (debug) {
                    result += `<div style="font-size:12px;color:silver;">（调试信息传入：${JSON.stringify({ year })}）</div>`;
                    result += `<div style="font-size:12px;color:silver;">（调试信息返回：${JSON.stringify(r)}）</div>`;
                }

                return {
                    reply: result
                };
            }
        },
    ]
})