(function (ctx) {
    return {
        parentId: 'chat-model',
        name: '项目助手',
        summary: '查询项目信息',
        settings: {
            maxChatHistoryLength: 6, // 对话时塞入的历史对话条数限制
            chatHistoryBotReplyMaxSize: 100, // 对话时塞入的历史AI回复内容大小限制
            config: {
                // *** 李扬系统 ***
                base_url: 'http://test.ovinfo.com:3021',
            },
            chatOpts: {
                // *** 聊天模型 ***
                // toolModel: 'msa_kimi-k2-0711-preview',
                // toolModel: 'br_gpt-4.1',
                // chatModel: 'albl_qwen3-235b-a22b|no_think|',
                chatModel: 'ollama_qwen3:14b|no_think|',
                // chatModel: 'albl_qwen-plus',
                // chatModel: 'albl_qwen-turbo',
                // chatModel: 'ollama_qwq:32b',
                // *** 工具模型 ***
                // toolModel: 'msa_kimi-k2-0711-preview',
                // toolModel: 'br_gpt-4.1',
                // toolModel: 'albl_qwen3-235b-a22b|no_think|',
                // toolModel: 'ollama_qwen3:14b|no_think|',
                // toolModel: 'ollama_qwen3:8b|no_think|',
                // toolModel: 'minimax_MiniMax-Text-01',
                // toolModel: 'gjld_deepseek-ai/DeepSeek-V3',
                toolModel: 'ds_deepseek-chat',
                // toolModel: 'zhipu_glm-4-plus', // 查看我的任务队列在有2组对话历史就会有问题，就不调用工具了
                // toolModel: 'albl_qwen-plus',
                // toolModel: 'albl_qwen-max',
                // toolModel: 'br_gpt-4o-mini',
            },
            getSystemMessage(pars, opts) {
                let str = ``;

                str += `
                ### 上下文
                #### 当前日期
                ${ctx.timeUtil.toDateStr(new Date())}
                #### 当前时间
                ${ctx.timeUtil.toTimeStr(new Date())}
                `;

                str += `
                ### 任务说明
                #### 角色要求
                - 你是项目助手，帮助用户查询项目信息，可以通过调用工具来完成任务
                
                #### 工具要求
                - 优先调用工具，保证参考信息是最新的
                
                #### 回复要求
                - 如果没有合适的工具或者无法回答用户提问就回复“对不起，暂时没有相关知识或权限”
                `;

                return str;
            },
        },
    };
})