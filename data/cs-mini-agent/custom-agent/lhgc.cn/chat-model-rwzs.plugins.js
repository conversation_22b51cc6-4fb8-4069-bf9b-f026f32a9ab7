(function (ctx) {
    let debug = true;
    // 插件
    return {
        /*** 系统功能 ***/
        reqByCmd: {
            exec: async function(params) {
                const cmd = params.cmd;
                const form = params.pars;
                let token = '';

                const { httpUtil } = ctx;
                let cfg = ctx.agent.getSettings().config;
                let reqUrl = `${cfg.base_url}${cmd}`;
                token = cfg.token;

                if (debug) log(`【请求绿化工程接口】${token} | ${reqUrl} | ${JSON.stringify(form)}`)

                const r = await httpUtil.reqPostFormSync(reqUrl, form, {
                    headers: {
                        token: token
                    }
                });
                if (r.success !== true && typeof(r) === 'string') {
                    log(`【执行插件】请求异常 -> ${r}`);
                    return {
                        success: false,
                        msg: `返回内容无法解析${(r != null ? `（${r.substring(0, 30)}...）` : '')}`
                    }
                }
                return r;
            }
        },
    };
})