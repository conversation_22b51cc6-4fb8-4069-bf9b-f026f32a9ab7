const express = require('express');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');

import {Buffer} from "buffer";
const crypto = require('crypto');
import OpenAI from "openai";

const lancedb = require("@lancedb/lancedb");
const apacheArrow = require("apache-arrow");

// 用于主项目执行
const {start} = require('./src/main-start');
// 用于派生项目执行
// const {start} = require('./lib/main-start');

const imports = {
    express, bodyParser,
    fs, path,
    Buffer, crypto, OpenAI,
    lancedb, apacheArrow,
};
const ctx = start(imports, {
    startServer: false,
});

/*** 对内容进行拆分 ***/

async function main() {
    const content = readFileContent()

    // 根据字数和标点符号拆分内容
    const bdfhSplitList = splitContentByWordsByBDFH(content, 4000)
    saveResult(bdfhSplitList, 'BDFH')

    // // 根据字数和章节或段落拆分内容
    // const zjdlSplitList = splitContentByWordsByZJDL(content, 4000)
    // // 验证内容完整性
    // validateContentIntegrity(content, zjdlSplitList);
    // saveResultWithNames(zjdlSplitList, 'ZJDL')
    // console.log('\nZJDL拆分结果:')
    // zjdlSplitList.forEach((item, index) => {
    //     console.log(`${index + 1}. ${item.name} (${item.content.length}字)`)
    // })

    /*** 尝试用AI来拆分（失败）***/
    // const chatModel = ctx.shortTimeCacheService.getChatModel();

    // const messages = [];
    // messages.push({
    //     role: 'system',
    //     content: `
    //     ### 任务说明
    //     #### 角色要求
    //     - 你现在是内容切片专家，负责把一篇内容切成多个分片，每个分片最多不要超过4000个汉字，尽可能的合理拆分，这个是用于RAG技术来做知识库用的
    //     - 拆分后的内容用一个字符串数组来返回
    //
    //     #### 回复要求
    //     - 不要有任何解释，直接返回结果
    //     - 结果是一个json格式的字符串数组，包含拆分的序列
    //     `
    // })
    // messages.push({
    //     role: 'user',
    //     content: readFileContent()
    // })
    // const r = await chatModel.chat('ollama_gpt-oss:20b', messages)
    // console.log(JSON.stringify(r))
    // saveResult(r)

    console.log('执行完毕')
}

function readFileContent() {
    return fs.readFileSync('./data/test/绿道建设技术标准（报批稿）-0731.md', 'utf8')
}

function saveResult(list, splitTag) {
    for (const i in list) {
        const item = list[i]
        fs.writeFileSync(`./data/test/绿道建设技术标准（报批稿）-0731_${splitTag}_${i}.md`, item);
    }
}

function saveResultWithNames(list, splitTag) {
    // 确保目录存在
    const testDir = './data/test';
    if (!fs.existsSync(testDir)) {
        fs.mkdirSync(testDir, { recursive: true });
    }
    
    for (const i in list) {
        const item = list[i]
        // 限制文件名长度，避免过长
        let safeName = item.name.replace(/[\s\/\\:*?"<>|#]/g, '_');
        if (safeName.length > 100) {
            safeName = safeName.substring(0, 100) + '...';
        }
        
        const fileName = `./data/test/绿道建设技术标准（报批稿）-0731_${splitTag}_${i}_${safeName}.md`;
        const content = `# ${item.name}\n\n${item.content}`;
        
        try {
            fs.writeFileSync(fileName, content);
        } catch (error) {
            // 如果文件名仍然有问题，使用简化的文件名
            const simpleName = `./data/test/绿道建设技术标准（报批稿）-0731_${splitTag}_${i}.md`;
            fs.writeFileSync(simpleName, content);
            console.log(`文件名过长，已简化保存为: ${simpleName}`);
        }
    }
}

/**
 * 根据字数和标点符号拆分内容
 * 根据字数要求拆分内容为多个分片，每个分片字数不能超过最大要求，要按标点符号来拆，不要把一句话拆开
 * 保持原有的基本格式（换行符、段落结构等）
 * @param content 待拆分的内容
 * @param maxWords 每个分片的最大字数
 */
function splitContentByWordsByBDFH(content, maxWords) {
    if (!content || content.trim() === '') {
        return [];
    }
    
    if (maxWords <= 0) {
        throw new Error('maxWords must be greater than 0');
    }
    
    // 如果内容长度小于等于最大字数，直接返回
    if (content.length <= maxWords) {
        return [content];
    }
    
    const result = [];
    let currentChunk = '';
    
    // 首先按段落分割（双换行），保持段落结构
    const paragraphs = content.split(/\n\s*\n/);
    
    for (let paragraph of paragraphs) {
        // 如果段落本身就超过限制，需要进一步拆分
        if (paragraph.length > maxWords) {
            // 先保存当前块（如果有内容）
            if (currentChunk) {
                result.push(currentChunk);
                currentChunk = '';
            }
            
            // 对超长段落按句子拆分，保持换行
            const sentences = paragraph.split(/([。！？])/).filter(s => s !== '');
            
            // 重新组合句子和标点
            const completeSentences = [];
            for (let i = 0; i < sentences.length; i += 2) {
                const sentence = sentences[i];
                const punctuation = sentences[i + 1] || '';
                if (sentence) {
                    completeSentences.push(sentence + punctuation);
                }
            }
            
            for (let sentence of completeSentences) {
                // 如果单个句子就超过最大字数，按次级标点拆分
                if (sentence.length > maxWords) {
                    // 先保存当前块
                    if (currentChunk) {
                        result.push(currentChunk);
                        currentChunk = '';
                    }
                    
                    // 按逗号、分号等拆分
                    const subSentences = sentence.split(/([，；：、])/).filter(s => s !== '');
                    
                    // 重新组合子句和标点
                    const completeSubSentences = [];
                    for (let i = 0; i < subSentences.length; i += 2) {
                        const subSentence = subSentences[i];
                        const punctuation = subSentences[i + 1] || '';
                        if (subSentence) {
                            completeSubSentences.push(subSentence + punctuation);
                        }
                    }
                    
                    for (let subSentence of completeSubSentences) {
                        // 如果子句仍然超长，强制按字符拆分
                        if (subSentence.length > maxWords) {
                            let start = 0;
                            while (start < subSentence.length) {
                                const chunk = subSentence.slice(start, start + maxWords);
                                result.push(chunk);
                                start += maxWords;
                            }
                        } else {
                            // 检查是否可以添加到当前块
                            if (currentChunk.length + subSentence.length <= maxWords) {
                                currentChunk += subSentence;
                            } else {
                                // 保存当前块，开始新块
                                if (currentChunk) {
                                    result.push(currentChunk);
                                }
                                currentChunk = subSentence;
                            }
                        }
                    }
                } else {
                    // 检查是否可以添加到当前块
                    if (currentChunk.length + sentence.length <= maxWords) {
                        currentChunk += sentence;
                    } else {
                        // 保存当前块，开始新块
                        if (currentChunk) {
                            result.push(currentChunk);
                        }
                        currentChunk = sentence;
                    }
                }
            }
        } else {
            // 段落未超长，检查是否可以加入当前块
            const separator = currentChunk ? '\n\n' : '';
            const potentialChunk = currentChunk + separator + paragraph;
            
            if (potentialChunk.length <= maxWords) {
                currentChunk = potentialChunk;
            } else {
                // 保存当前块，开始新块
                if (currentChunk) {
                    result.push(currentChunk);
                }
                currentChunk = paragraph;
            }
        }
    }
    
    // 保存最后一个块
    if (currentChunk) {
        result.push(currentChunk);
    }
    
    return result;
}

/**
 * 根据字数和章节或段落拆分内容（不推荐，效果不好）
 * 根据字数要求拆分内容为多个分片，每个分片字数不能超过最大要求，尽量按章节来拆分如果不行再按段落来拆
 * 返回一个数组，字段：名称（章节/段落名），内容
 * 保持原有的基本格式（换行符、段落结构等）
 * @param content 待拆分的内容
 * @param maxWords 每个分片的最大字数
 * @returns {[{ name, content }]}
 */
function splitContentByWordsByZJDL(content, maxWords) {
    if (!content || content.trim() === '') {
        return [];
    }
    
    if (maxWords <= 0) {
        throw new Error('maxWords must be greater than 0');
    }
    
    const result = [];
    const minChunkSize = Math.max(200, maxWords * 0.3); // 最小分片大小，避免过度拆分
    
    // 首先尝试识别章节标题（# ## ###等markdown标题或者数字编号）
    const sections = extractSections(content);
    
    if (sections.length > 1) {
        // 按章节处理，但要合并小章节
        let currentGroup = null;
        
        for (const section of sections) {
            // 如果章节内容很少且当前组未满，尝试合并
            if (currentGroup && 
                section.content.length < minChunkSize && 
                (currentGroup.content.length + section.content.length + section.name.length + 10) <= maxWords) {
                
                // 合并到当前组，但不要让名称过长
                currentGroup.content += `\n\n## ${section.name}\n\n${section.content}`;
                // 只在名称不太长时才添加到名称中
                if (currentGroup.name.length < 50) {
                    currentGroup.name += ` + ${section.name}`;
                } else if (!currentGroup.name.includes('等多个章节')) {
                    currentGroup.name += ' 等多个章节';
                }
                continue;
            }
            
            // 保存当前组
            if (currentGroup) {
                if (currentGroup.content.length > maxWords) {
                    const subsections = splitLongSectionOptimized(currentGroup, maxWords, minChunkSize);
                    result.push(...subsections);
                } else {
                    result.push(currentGroup);
                }
            }
            
            // 开始新组
            currentGroup = {
                name: section.name,
                content: section.content
            };
        }
        
        // 处理最后一组
        if (currentGroup) {
            if (currentGroup.content.length > maxWords) {
                const subsections = splitLongSectionOptimized(currentGroup, maxWords, minChunkSize);
                result.push(...subsections);
            } else {
                result.push(currentGroup);
            }
        }
    } else {
        // 没有明显章节结构，按段落处理但要合并小段落
        const optimizedParts = splitByParagraphsOptimized(content, maxWords, minChunkSize);
        result.push(...optimizedParts);
    }
    
    return result;
}

/**
 * 提取章节信息（修复版，确保内容不丢失）
 * @param content 内容
 * @returns {[{ name, content }]}
 */
function extractSections(content) {
    const sections = [];
    
    // 匹配markdown标题或数字章节标题
    const sectionRegex = /^(#{1,6}\s+.*?|\d+[\.、].*?|第[一二三四五六七八九十\d]+[章节部分].*?)$/gm;
    
    const matches = [];
    let match;
    
    // 首先收集所有匹配的章节标题
    while ((match = sectionRegex.exec(content)) !== null) {
        matches.push({
            title: match[0].trim(),
            start: match.index,
            end: match.index + match[0].length
        });
    }
    
    if (matches.length === 0) {
        // 没有找到章节标题，返回整个内容
        return [{
            name: '全文',
            content: content
        }];
    }
    
    // 处理文档开头的内容（在第一个章节之前）
    if (matches[0].start > 0) {
        const introContent = content.slice(0, matches[0].start).trim();
        if (introContent) {
            sections.push({
                name: '前言',
                content: introContent
            });
        }
    }
    
    // 处理每个章节
    for (let i = 0; i < matches.length; i++) {
        const currentMatch = matches[i];
        const nextMatch = matches[i + 1];
        
        // 确定章节内容的范围
        const contentStart = currentMatch.start;
        const contentEnd = nextMatch ? nextMatch.start : content.length;
        
        // 提取章节内容（包括标题）
        const sectionContent = content.slice(contentStart, contentEnd).trim();
        
        if (sectionContent) {
            sections.push({
                name: currentMatch.title,
                content: sectionContent
            });
        }
    }
    
    return sections.filter(section => section.content.trim() !== '');
}

/**
 * 提取段落信息
 * @param content 内容
 * @returns {[{ name, content }]}
 */
function extractParagraphs(content) {
    const paragraphs = [];
    const paragraphTexts = content.split(/\n\s*\n/).filter(p => p.trim() !== '');
    
    paragraphTexts.forEach((text, index) => {
        paragraphs.push({
            name: `段落${index + 1}`,
            content: text.trim()
        });
    });
    
    return paragraphs;
}

/**
 * 拆分过长的章节（优化版，避免过度拆分）
 * @param section 章节对象
 * @param maxWords 最大字数
 * @param minChunkSize 最小分片大小
 * @returns {[{ name, content }]}
 */
function splitLongSectionOptimized(section, maxWords, minChunkSize) {
    const result = [];
    const paragraphs = section.content.split(/\n\s*\n/).filter(p => p.trim() !== '');
    
    let currentPart = {
        name: `${section.name} - 第1部分`,
        content: '',
        partIndex: 1
    };
    
    for (const paragraph of paragraphs) {
        const separator = currentPart.content ? '\n\n' : '';
        const potentialContent = currentPart.content + separator + paragraph;
        
        if (potentialContent.length <= maxWords) {
            currentPart.content = potentialContent;
        } else {
            // 保存当前部分（只有当达到最小大小时）
            if (currentPart.content && currentPart.content.length >= minChunkSize) {
                result.push({
                    name: currentPart.name,
                    content: currentPart.content
                });
                
                // 开始新部分
                currentPart.partIndex++;
                currentPart.name = `${section.name} - 第${currentPart.partIndex}部分`;
                currentPart.content = paragraph;
            } else {
                // 当前部分太小，强制添加这个段落
                currentPart.content = potentialContent;
            }
            
            // 如果单个段落过长，使用已有的拆分函数
            if (paragraph.length > maxWords) {
                if (currentPart.content !== paragraph) {
                    // 保存当前部分
                    result.push({
                        name: currentPart.name,
                        content: currentPart.content
                    });
                    currentPart.partIndex++;
                }
                
                const splitChunks = splitContentByWordsByBDFH(paragraph, maxWords);
                splitChunks.forEach((chunk, index) => {
                    result.push({
                        name: `${section.name} - 第${currentPart.partIndex + index}部分`,
                        content: chunk
                    });
                });
                currentPart.partIndex += splitChunks.length;
                currentPart.content = '';
            }
        }
    }
    
    // 保存最后一部分
    if (currentPart.content) {
        result.push({
            name: currentPart.name,
            content: currentPart.content
        });
    }
    
    return result;
}

/**
 * 按段落拆分内容（优化版，合并小段落）
 * @param content 内容
 * @param maxWords 最大字数
 * @param minChunkSize 最小分片大小
 * @returns {[{ name, content }]}
 */
function splitByParagraphsOptimized(content, maxWords, minChunkSize) {
    const result = [];
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim() !== '');
    
    let currentGroup = {
        name: '第1部分',
        content: '',
        index: 1
    };
    
    for (const paragraph of paragraphs) {
        const separator = currentGroup.content ? '\n\n' : '';
        const potentialContent = currentGroup.content + separator + paragraph;
        
        if (potentialContent.length <= maxWords) {
            currentGroup.content = potentialContent;
        } else {
            // 只有当当前组达到最小大小时才保存
            if (currentGroup.content.length >= minChunkSize) {
                result.push({
                    name: currentGroup.name,
                    content: currentGroup.content
                });
                
                // 开始新组
                currentGroup.index++;
                currentGroup.name = `第${currentGroup.index}部分`;
                currentGroup.content = paragraph;
            } else {
                // 当前组太小，强制添加这个段落
                currentGroup.content = potentialContent;
            }
            
            // 如果单个段落过长，需要拆分
            if (paragraph.length > maxWords) {
                if (currentGroup.content !== paragraph) {
                    // 保存当前组
                    result.push({
                        name: currentGroup.name,
                        content: currentGroup.content
                    });
                    currentGroup.index++;
                }
                
                const splitChunks = splitContentByWordsByBDFH(paragraph, maxWords);
                splitChunks.forEach((chunk, index) => {
                    result.push({
                        name: `第${currentGroup.index + index}部分`,
                        content: chunk
                    });
                });
                currentGroup.index += splitChunks.length;
                currentGroup.content = '';
            }
        }
    }
    
    // 保存最后一组
    if (currentGroup.content) {
        result.push({
            name: currentGroup.name,
            content: currentGroup.content
        });
    }
    
    return result;
}

/**
 * 验证内容完整性的辅助函数
 * @param originalContent 原始内容
 * @param sections 拆分后的章节
 */
function validateContentIntegrity(originalContent, sections) {
    console.log('\n=== 内容完整性验证 ===');
    
    const totalOriginal = originalContent.length;
    const totalSections = sections.reduce((sum, section) => sum + section.content.length, 0);
    
    console.log(`原始内容长度: ${totalOriginal}`);
    console.log(`拆分后总长度: ${totalSections}`);
    console.log(`差值: ${totalOriginal - totalSections}`);
    
    if (totalOriginal === totalSections) {
        console.log('✅ 内容完整性验证通过');
    } else {
        console.log('⚠️ 警告: 内容不完整');
        
        // 输出每个章节的信息
        sections.forEach((section, index) => {
            console.log(`  ${index + 1}. ${section.name}: ${section.content.length}字`);
        });
    }
    
    return totalOriginal === totalSections;
}

main();