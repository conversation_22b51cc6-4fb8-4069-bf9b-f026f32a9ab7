const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');

const lancedb = require("@lancedb/lancedb");
const apacheArrow = require("apache-arrow");

// 用于主项目执行
const {start} = require('./src/main-start');
// 用于派生项目执行
// const {start} = require('./lib/main-start');

const imports = {
    express, bodyParser, path,
    lancedb, apacheArrow,
};
const ctx = start(imports, {
    startServer: false,
});

/*** Moonshot AI 测试 ***/
// let model = 'moonshot-v1-8k';
let model = 'gpt-4o-mini';
const chatModel = new imports.ChatModel(ctx);
chatModel.chat(model, [
    {
        role: 'user', content: '“java自学”这个文件里讲了什么？'
    },
    // {
    //     role: 'assistant', content: '# 共查询到一个文件：\n- java自学.md'
    // },
    // {
    //     role: 'user', content: '继续'
    // },
], (r) => {
    console.log(r);
}, (err) => {
    console.error(err);
}, {
    tools: [
        {
            name: 'getFileContent',
            descr: '根据用户提供的文件名获取文件内容，此工具回返回文件内容',
            params: {
                required: ["fileName"],
                props: {
                    "fileName": {
                        type: "string",
                        descr: "文件名"
                    }
                }
            }
        },
        {
            name: 'FindLocalFiles',
            descr: '根据用户提供的文件名关键词查找本地文件，此工具回返回文件列表',
            params: {
                required: ["fileKw"],
                props: {
                    "fileKw": {
                        type: "string",
                        descr: "文件名关键词"
                    }
                }
            }
        },
    ]
});

// ctx.shortTimeCacheService.getMoonshotAIMapper().chat('moonshot-v1-8k', [
//     {
//         role: 'user', content: '“java自学”这个文件里讲了什么？'
//     }
// ], (r) => {
//     console.log(JSON.stringify(r));
// }, (err) => {
//
// }, {
//     tools: [
//         {
//             type: "function",
//             function: {
//                 name: "getFileContent",
//                 description: "根据用户提供的文件名获取文件内容，此工具回返回文件内容",
//                 parameters: {
//                     type: "object",
//                     required: ["fileName"],
//                     properties: {
//                         "fileName": {
//                             type: "string",
//                             description: "文件名"
//                         }
//                     }
//                 }
//             }
//         },
//         {
//             type: "function",
//             function: {
//                 name: "FindLocalFiles",
//                 description: "根据用户提供的文件名关键词查找本地文件，此工具回返回文件列表",
//                 parameters: {
//                     type: "object",
//                     required: ["fileKw"],
//                     properties: {
//                         "fileKw": {
//                             type: "string",
//                             description: "文件名关键词"
//                         }
//                     }
//                 }
//             }
//         },
//     ]
// });